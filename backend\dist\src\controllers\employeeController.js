"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.submitAttendance = exports.getEmployeeAttendance = exports.getEmployeeStatistics = exports.deleteEmployee = exports.updateEmployee = exports.createEmployee = exports.getEmployee = exports.getEmployees = void 0;
const client_1 = require("@prisma/client");
const zod_1 = require("zod");
const prisma = new client_1.PrismaClient();
// Validation schemas
const createEmployeeSchema = zod_1.z.object({
    officeId: zod_1.z.string().uuid(),
    name: zod_1.z.string().min(1, 'Name is required'),
    email: zod_1.z.string().email().optional(),
    phone: zod_1.z.string().optional(),
    employeeId: zod_1.z.string().min(1, 'Employee ID is required'),
    designation: zod_1.z.string().min(1, 'Designation is required'),
    department: zod_1.z.string().optional(),
    joinDate: zod_1.z.string().datetime(),
});
const updateEmployeeSchema = zod_1.z.object({
    name: zod_1.z.string().min(1).optional(),
    email: zod_1.z.string().email().optional(),
    phone: zod_1.z.string().optional(),
    designation: zod_1.z.string().min(1).optional(),
    department: zod_1.z.string().optional(),
    isActive: zod_1.z.boolean().optional(),
});
// Get all employees with filtering and pagination
const getEmployees = async (req, res) => {
    try {
        const { officeId, department, isActive, search, page = '1', limit = '20', sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
        // Build filter object
        const where = {};
        // Office-based access control
        if (officeId) {
            where.officeId = officeId;
        }
        if (department) {
            where.department = department;
        }
        if (isActive !== undefined) {
            where.isActive = isActive === 'true';
        }
        // Text search
        if (search) {
            where.OR = [
                { name: { contains: search, mode: 'insensitive' } },
                { email: { contains: search, mode: 'insensitive' } },
                { employeeId: { contains: search, mode: 'insensitive' } },
                { designation: { contains: search, mode: 'insensitive' } },
            ];
        }
        // Pagination
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        const skip = (pageNum - 1) * limitNum;
        // Sort
        const orderBy = {};
        orderBy[sortBy] = sortOrder === 'desc' ? 'desc' : 'asc';
        // Execute query
        const [employees, total] = await Promise.all([
            prisma.employee.findMany({
                where,
                include: {
                    office: {
                        select: {
                            id: true,
                            name: true,
                            type: true,
                        },
                    },
                    attendanceRecords: {
                        where: {
                            date: {
                                gte: new Date(new Date().setDate(new Date().getDate() - 30)),
                            },
                        },
                        select: {
                            date: true,
                            status: true,
                        },
                    },
                },
                orderBy,
                skip,
                take: limitNum,
            }),
            prisma.employee.count({ where }),
        ]);
        // Calculate pagination info
        const totalPages = Math.ceil(total / limitNum);
        const hasNext = pageNum < totalPages;
        const hasPrevious = pageNum > 1;
        res.json({
            success: true,
            data: {
                data: employees,
                total,
                page: pageNum,
                limit: limitNum,
                totalPages,
                hasNext,
                hasPrevious,
            },
        });
    }
    catch (error) {
        console.error('Error fetching employees:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch employees',
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
};
exports.getEmployees = getEmployees;
// Get single employee with details
const getEmployee = async (req, res) => {
    try {
        const { id } = req.params;
        const employee = await prisma.employee.findUnique({
            where: { id },
            include: {
                office: {
                    select: {
                        id: true,
                        name: true,
                        type: true,
                        address: true,
                    },
                },
                attendanceRecords: {
                    orderBy: { date: 'desc' },
                    take: 30, // Last 30 days
                    select: {
                        date: true,
                        status: true,
                        checkInTime: true,
                        checkOutTime: true,
                        hoursWorked: true,
                        notes: true,
                    },
                },
            },
        });
        if (!employee) {
            return res.status(404).json({
                success: false,
                message: 'Employee not found',
            });
        }
        res.json({
            success: true,
            data: employee,
        });
    }
    catch (error) {
        console.error('Error fetching employee:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch employee',
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
};
exports.getEmployee = getEmployee;
// Create new employee
const createEmployee = async (req, res) => {
    try {
        // Validate request body
        const validatedData = createEmployeeSchema.parse(req.body);
        // Check if employee ID already exists
        const existingEmployee = await prisma.employee.findUnique({
            where: { employeeId: validatedData.employeeId },
        });
        if (existingEmployee) {
            return res.status(400).json({
                success: false,
                message: 'Employee ID already exists',
            });
        }
        // Check if office exists
        const office = await prisma.office.findUnique({
            where: { id: validatedData.officeId },
        });
        if (!office) {
            return res.status(400).json({
                success: false,
                message: 'Office not found',
            });
        }
        // Create the employee
        const employee = await prisma.employee.create({
            data: {
                ...validatedData,
                joinDate: new Date(validatedData.joinDate),
            },
            include: {
                office: {
                    select: {
                        id: true,
                        name: true,
                        type: true,
                    },
                },
            },
        });
        res.status(201).json({
            success: true,
            data: employee,
            message: 'Employee created successfully',
        });
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: error.errors,
            });
        }
        console.error('Error creating employee:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create employee',
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
};
exports.createEmployee = createEmployee;
// Update employee
const updateEmployee = async (req, res) => {
    try {
        const { id } = req.params;
        // Validate request body
        const validatedData = updateEmployeeSchema.parse(req.body);
        // Check if employee exists
        const existingEmployee = await prisma.employee.findUnique({
            where: { id },
        });
        if (!existingEmployee) {
            return res.status(404).json({
                success: false,
                message: 'Employee not found',
            });
        }
        // Update the employee
        const employee = await prisma.employee.update({
            where: { id },
            data: validatedData,
            include: {
                office: {
                    select: {
                        id: true,
                        name: true,
                        type: true,
                    },
                },
            },
        });
        res.json({
            success: true,
            data: employee,
            message: 'Employee updated successfully',
        });
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: error.errors,
            });
        }
        console.error('Error updating employee:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update employee',
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
};
exports.updateEmployee = updateEmployee;
// Delete employee
const deleteEmployee = async (req, res) => {
    try {
        const { id } = req.params;
        // Check if employee exists
        const existingEmployee = await prisma.employee.findUnique({
            where: { id },
        });
        if (!existingEmployee) {
            return res.status(404).json({
                success: false,
                message: 'Employee not found',
            });
        }
        // Soft delete by setting isActive to false
        await prisma.employee.update({
            where: { id },
            data: { isActive: false },
        });
        res.json({
            success: true,
            message: 'Employee deleted successfully',
        });
    }
    catch (error) {
        console.error('Error deleting employee:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete employee',
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
};
exports.deleteEmployee = deleteEmployee;
// Get employee statistics
const getEmployeeStatistics = async (req, res) => {
    try {
        const { officeId, department } = req.query;
        // Build filter
        const where = {};
        if (officeId)
            where.officeId = officeId;
        if (department)
            where.department = department;
        // Get statistics
        const [totalEmployees, activeEmployees, departmentStats, recentJoins,] = await Promise.all([
            prisma.employee.count({ where }),
            prisma.employee.count({ where: { ...where, isActive: true } }),
            prisma.employee.groupBy({
                by: ['department'],
                where,
                _count: {
                    id: true,
                },
            }),
            prisma.employee.findMany({
                where: {
                    ...where,
                    joinDate: {
                        gte: new Date(new Date().setDate(new Date().getDate() - 30)),
                    },
                },
                select: {
                    id: true,
                    name: true,
                    joinDate: true,
                    department: true,
                },
                orderBy: { joinDate: 'desc' },
                take: 10,
            }),
        ]);
        const statistics = {
            totalEmployees,
            activeEmployees,
            inactiveEmployees: totalEmployees - activeEmployees,
            departmentBreakdown: departmentStats.map(stat => ({
                department: stat.department || 'Unassigned',
                count: stat._count.id,
            })),
            recentJoins,
        };
        res.json({
            success: true,
            data: statistics,
        });
    }
    catch (error) {
        console.error('Error fetching employee statistics:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch employee statistics',
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
};
exports.getEmployeeStatistics = getEmployeeStatistics;
// Get employee attendance
const getEmployeeAttendance = async (req, res) => {
    try {
        const { employeeId, officeId, fromDate, toDate, page = '1', limit = '30', } = req.query;
        // Build filter
        const where = {};
        if (employeeId)
            where.employeeId = employeeId;
        if (officeId)
            where.officeId = officeId;
        // Date range filter
        if (fromDate || toDate) {
            where.date = {};
            if (fromDate)
                where.date.gte = new Date(fromDate);
            if (toDate)
                where.date.lte = new Date(toDate);
        }
        // Pagination
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        const skip = (pageNum - 1) * limitNum;
        // Execute query
        const [attendance, total] = await Promise.all([
            prisma.attendanceRecord.findMany({
                where,
                include: {
                    employee: {
                        select: {
                            id: true,
                            name: true,
                            employeeId: true,
                            department: true,
                        },
                    },
                    office: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                },
                orderBy: { date: 'desc' },
                skip,
                take: limitNum,
            }),
            prisma.attendanceRecord.count({ where }),
        ]);
        // Calculate pagination info
        const totalPages = Math.ceil(total / limitNum);
        const hasNext = pageNum < totalPages;
        const hasPrevious = pageNum > 1;
        res.json({
            success: true,
            data: {
                data: attendance,
                total,
                page: pageNum,
                limit: limitNum,
                totalPages,
                hasNext,
                hasPrevious,
            },
        });
    }
    catch (error) {
        console.error('Error fetching employee attendance:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch employee attendance',
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
};
exports.getEmployeeAttendance = getEmployeeAttendance;
// Submit employee attendance
const submitAttendance = async (req, res) => {
    try {
        const { employeeId, officeId, date, status, checkInTime, checkOutTime, notes, } = req.body;
        // Validate required fields
        if (!employeeId || !officeId || !date || !status) {
            return res.status(400).json({
                success: false,
                message: 'Employee ID, Office ID, date, and status are required',
            });
        }
        // Check if attendance already exists for this date
        const existingAttendance = await prisma.attendanceRecord.findUnique({
            where: {
                officeId_employeeId_date: {
                    officeId,
                    employeeId,
                    date: new Date(date),
                },
            },
        });
        if (existingAttendance) {
            return res.status(400).json({
                success: false,
                message: 'Attendance already recorded for this date',
            });
        }
        // Calculate hours worked if both check-in and check-out times are provided
        let hoursWorked = 0;
        if (checkInTime && checkOutTime) {
            const checkIn = new Date(checkInTime);
            const checkOut = new Date(checkOutTime);
            hoursWorked = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
        }
        // Create attendance record
        const attendance = await prisma.attendanceRecord.create({
            data: {
                employeeId,
                officeId,
                date: new Date(date),
                status,
                checkInTime: checkInTime ? new Date(checkInTime) : null,
                checkOutTime: checkOutTime ? new Date(checkOutTime) : null,
                hoursWorked,
                notes,
            },
            include: {
                employee: {
                    select: {
                        id: true,
                        name: true,
                        employeeId: true,
                    },
                },
                office: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
            },
        });
        res.status(201).json({
            success: true,
            data: attendance,
            message: 'Attendance submitted successfully',
        });
    }
    catch (error) {
        console.error('Error submitting attendance:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to submit attendance',
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
};
exports.submitAttendance = submitAttendance;
