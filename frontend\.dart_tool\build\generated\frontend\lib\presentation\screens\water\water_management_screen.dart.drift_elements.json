{"valid_import": true, "imports": [{"uri": "package:flutter/material.dart", "transitive": false}, {"uri": "package:flutter_riverpod/flutter_riverpod.dart", "transitive": false}, {"uri": "package:frontend/core/theme/app_theme.dart", "transitive": false}, {"uri": "package:frontend/data/models/system.dart", "transitive": false}, {"uri": "package:frontend/presentation/providers/system_providers.dart", "transitive": false}, {"uri": "package:frontend/presentation/providers/auth_providers.dart", "transitive": false}, {"uri": "package:frontend/presentation/widgets/enhanced_error_widget.dart", "transitive": false}, {"uri": "package:frontend/presentation/widgets/retry_widget.dart", "transitive": false}, {"uri": "package:frontend/presentation/widgets/offline_indicator.dart", "transitive": false}, {"uri": "package:frontend/presentation/screens/main/main_navigation_screen.dart", "transitive": false}], "elements": []}