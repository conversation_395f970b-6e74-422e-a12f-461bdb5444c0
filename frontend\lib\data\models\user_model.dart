import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

enum UserRole {
  @JsonValue('SUPER_ADMIN')
  superAdmin,
  @JsonValue('PROPERTY_MANAGER')
  propertyManager,
  @JsonValue('OFFICE_MANAGER')
  officeManager,
  @JsonValue('SECURITY_PERSONNEL')
  securityPersonnel,
  @JsonValue('MAINTENANCE_STAFF')
  maintenanceStaff,
  @JsonValue('CONSTRUCTION_SUPERVISOR')
  constructionSupervisor,
}

@JsonSerializable()
class UserModel {
  final String id;
  final String name;
  final String email;
  final UserRole role;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<String>? assignedProperties;
  final String? phone;
  final String? avatar;
  final Map<String, dynamic>? metadata;

  const UserModel({
    required this.id,
    required this.name,
    required this.email,
    required this.role,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    this.assignedProperties,
    this.phone,
    this.avatar,
    this.metadata,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) => _$UserModelFromJson(json);
  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  UserModel copyWith({
    String? id,
    String? name,
    String? email,
    UserRole? role,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? assignedProperties,
    String? phone,
    String? avatar,
    Map<String, dynamic>? metadata,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      role: role ?? this.role,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      assignedProperties: assignedProperties ?? this.assignedProperties,
      phone: phone ?? this.phone,
      avatar: avatar ?? this.avatar,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel &&
        other.id == id &&
        other.name == name &&
        other.email == email &&
        other.role == role &&
        other.isActive == isActive;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        email.hashCode ^
        role.hashCode ^
        isActive.hashCode;
  }

  @override
  String toString() {
    return 'UserModel(id: $id, name: $name, email: $email, role: $role, isActive: $isActive)';
  }

  // Helper methods
  String get roleDisplayName {
    switch (role) {
      case UserRole.superAdmin:
        return 'Super Administrator';
      case UserRole.propertyManager:
        return 'Property Manager';
      case UserRole.officeManager:
        return 'Office Manager';
      case UserRole.securityPersonnel:
        return 'Security Personnel';
      case UserRole.maintenanceStaff:
        return 'Maintenance Staff';
      case UserRole.constructionSupervisor:
        return 'Construction Supervisor';
    }
  }

  String get initials {
    final parts = name.split(' ');
    if (parts.length >= 2) {
      return '${parts[0][0]}${parts[1][0]}'.toUpperCase();
    } else if (parts.isNotEmpty) {
      return parts[0][0].toUpperCase();
    }
    return 'U';
  }

  bool get isSuperAdmin => role == UserRole.superAdmin;
  bool get isPropertyManager => role == UserRole.propertyManager;
  bool get isOfficeManager => role == UserRole.officeManager;
  bool get isSecurityPersonnel => role == UserRole.securityPersonnel;
  bool get isMaintenanceStaff => role == UserRole.maintenanceStaff;
  bool get isConstructionSupervisor => role == UserRole.constructionSupervisor;

  bool hasPropertyAccess(String propertyId) {
    if (isSuperAdmin) return true;
    return assignedProperties?.contains(propertyId) ?? false;
  }

  bool canManageUsers() {
    return isSuperAdmin || isPropertyManager;
  }

  bool canViewReports() {
    return isSuperAdmin || isPropertyManager || isOfficeManager || isConstructionSupervisor;
  }

  bool canManageMaintenance() {
    return isSuperAdmin || isPropertyManager || isMaintenanceStaff;
  }

  bool canManageSecurity() {
    return isSuperAdmin || isPropertyManager || isSecurityPersonnel;
  }

  bool canManageEmployees() {
    return isSuperAdmin || isPropertyManager || isOfficeManager;
  }
}

// Login request model
@JsonSerializable()
class LoginRequest {
  final String email;
  final String password;

  const LoginRequest({
    required this.email,
    required this.password,
  });

  factory LoginRequest.fromJson(Map<String, dynamic> json) => _$LoginRequestFromJson(json);
  Map<String, dynamic> toJson() => _$LoginRequestToJson(this);
}

// Login response model
@JsonSerializable()
class LoginResponse {
  final bool success;
  final String? token;
  final String? refreshToken;
  final UserModel? user;
  final String? message;

  const LoginResponse({
    required this.success,
    this.token,
    this.refreshToken,
    this.user,
    this.message,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) => _$LoginResponseFromJson(json);
  Map<String, dynamic> toJson() => _$LoginResponseToJson(this);
}

// User creation request model
@JsonSerializable()
class CreateUserRequest {
  final String name;
  final String email;
  final String password;
  final UserRole role;
  final List<String>? assignedProperties;
  final String? phone;

  const CreateUserRequest({
    required this.name,
    required this.email,
    required this.password,
    required this.role,
    this.assignedProperties,
    this.phone,
  });

  factory CreateUserRequest.fromJson(Map<String, dynamic> json) => _$CreateUserRequestFromJson(json);
  Map<String, dynamic> toJson() => _$CreateUserRequestToJson(this);
}

// User update request model
@JsonSerializable()
class UpdateUserRequest {
  final String? name;
  final String? email;
  final UserRole? role;
  final bool? isActive;
  final List<String>? assignedProperties;
  final String? phone;

  const UpdateUserRequest({
    this.name,
    this.email,
    this.role,
    this.isActive,
    this.assignedProperties,
    this.phone,
  });

  factory UpdateUserRequest.fromJson(Map<String, dynamic> json) => _$UpdateUserRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UpdateUserRequestToJson(this);
}
