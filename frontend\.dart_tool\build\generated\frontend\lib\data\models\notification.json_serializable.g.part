// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NotificationModel _$NotificationModelFromJson(Map<String, dynamic> json) =>
    NotificationModel(
      id: json['id'] as String,
      title: json['title'] as String,
      message: json['message'] as String,
      type: json['type'] as String,
      priority: json['priority'] as String,
      isRead: json['isRead'] as bool,
      propertyId: json['propertyId'] as String?,
      property: json['property'] == null
          ? null
          : PropertyInfo.fromJson(json['property'] as Map<String, dynamic>),
      createdAt: json['createdAt'] as String,
      scheduledFor: json['scheduledFor'] as String?,
      expiresAt: json['expiresAt'] as String?,
    );

Map<String, dynamic> _$NotificationModelToJson(NotificationModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'message': instance.message,
      'type': instance.type,
      'priority': instance.priority,
      'isRead': instance.isRead,
      'propertyId': instance.propertyId,
      'property': instance.property,
      'createdAt': instance.createdAt,
      'scheduledFor': instance.scheduledFor,
      'expiresAt': instance.expiresAt,
    };

PropertyInfo _$PropertyInfoFromJson(Map<String, dynamic> json) => PropertyInfo(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
    );

Map<String, dynamic> _$PropertyInfoToJson(PropertyInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
    };

CreateNotificationRequest _$CreateNotificationRequestFromJson(
        Map<String, dynamic> json) =>
    CreateNotificationRequest(
      title: json['title'] as String,
      message: json['message'] as String,
      type: json['type'] as String,
      priority: json['priority'] as String?,
      targetUsers: (json['targetUsers'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      targetRoles: (json['targetRoles'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      propertyId: json['propertyId'] as String?,
      scheduledFor: json['scheduledFor'] as String?,
      expiresAt: json['expiresAt'] as String?,
    );

Map<String, dynamic> _$CreateNotificationRequestToJson(
        CreateNotificationRequest instance) =>
    <String, dynamic>{
      'title': instance.title,
      'message': instance.message,
      'type': instance.type,
      'priority': instance.priority,
      'targetUsers': instance.targetUsers,
      'targetRoles': instance.targetRoles,
      'propertyId': instance.propertyId,
      'scheduledFor': instance.scheduledFor,
      'expiresAt': instance.expiresAt,
    };

NotificationQueryParams _$NotificationQueryParamsFromJson(
        Map<String, dynamic> json) =>
    NotificationQueryParams(
      page: (json['page'] as num?)?.toInt(),
      limit: (json['limit'] as num?)?.toInt(),
      type: json['type'] as String?,
      priority: json['priority'] as String?,
      isRead: json['isRead'] as bool?,
      propertyId: json['propertyId'] as String?,
    );

Map<String, dynamic> _$NotificationQueryParamsToJson(
        NotificationQueryParams instance) =>
    <String, dynamic>{
      'page': instance.page,
      'limit': instance.limit,
      'type': instance.type,
      'priority': instance.priority,
      'isRead': instance.isRead,
      'propertyId': instance.propertyId,
    };

UnreadCountResponse _$UnreadCountResponseFromJson(Map<String, dynamic> json) =>
    UnreadCountResponse(
      unreadCount: (json['unreadCount'] as num).toInt(),
    );

Map<String, dynamic> _$UnreadCountResponseToJson(
        UnreadCountResponse instance) =>
    <String, dynamic>{
      'unreadCount': instance.unreadCount,
    };

MarkAllReadResponse _$MarkAllReadResponseFromJson(Map<String, dynamic> json) =>
    MarkAllReadResponse(
      markedCount: (json['markedCount'] as num).toInt(),
    );

Map<String, dynamic> _$MarkAllReadResponseToJson(
        MarkAllReadResponse instance) =>
    <String, dynamic>{
      'markedCount': instance.markedCount,
    };

NotificationStatistics _$NotificationStatisticsFromJson(
        Map<String, dynamic> json) =>
    NotificationStatistics(
      total: (json['total'] as num).toInt(),
      unread: (json['unread'] as num).toInt(),
      read: (json['read'] as num).toInt(),
      info: (json['info'] as num).toInt(),
      warning: (json['warning'] as num).toInt(),
      error: (json['error'] as num).toInt(),
      success: (json['success'] as num).toInt(),
      low: (json['low'] as num).toInt(),
      medium: (json['medium'] as num).toInt(),
      high: (json['high'] as num).toInt(),
      urgent: (json['urgent'] as num).toInt(),
    );

Map<String, dynamic> _$NotificationStatisticsToJson(
        NotificationStatistics instance) =>
    <String, dynamic>{
      'total': instance.total,
      'unread': instance.unread,
      'read': instance.read,
      'info': instance.info,
      'warning': instance.warning,
      'error': instance.error,
      'success': instance.success,
      'low': instance.low,
      'medium': instance.medium,
      'high': instance.high,
      'urgent': instance.urgent,
    };
