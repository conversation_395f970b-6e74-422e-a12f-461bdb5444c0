import { UserRole } from '@prisma/client';
export interface PermissionContext {
    userId: string;
    role: UserRole;
    propertyId?: string;
    officeId?: string;
    resourceId?: string;
    timestamp?: Date;
    ipAddress?: string;
    userAgent?: string;
}
export interface PermissionResult {
    granted: boolean;
    reason?: string;
    conditions?: Record<string, any>;
}
export interface UIPermissions {
    screens: string[];
    tabs: Record<string, {
        visible: boolean;
        enabled: boolean;
        accessLevel: 'full' | 'read_only' | 'restricted' | 'none';
        restrictions?: Record<string, any>;
    }>;
    widgets: Record<string, {
        visible: boolean;
        enabled: boolean;
        conditions?: Record<string, any>;
    }>;
    actions: string[];
    dataFilters: Record<string, any>;
}
export declare class PermissionService {
    /**
     * Check if user has permission for a specific action
     */
    static checkPermission(context: PermissionContext, resource: string, action: string): Promise<PermissionResult>;
    /**
     * Get comprehensive UI permissions for a user including tab-level control
     */
    static getUserUIPermissions(context: PermissionContext): Promise<UIPermissions>;
    /**
     * Check tab-level permission for a specific screen
     */
    static checkTabPermission(context: PermissionContext, screen: string, tabId: string): Promise<{
        hasAccess: boolean;
        accessLevel: string;
        restrictions?: Record<string, any>;
    }>;
    /**
     * Apply data filtering based on user permissions
     */
    static applyDataFilters(context: PermissionContext, resource: string, baseQuery: any): Promise<any>;
    /**
     * Evaluate ABAC conditions
     */
    private static evaluateABACConditions;
    /**
     * Evaluate access policies
     */
    private static evaluateAccessPolicies;
    /**
     * Process filter conditions with context substitution
     */
    private static processFilterConditions;
    /**
     * Audit permission usage
     */
    private static auditPermission;
}
