import '../constants/api_constants.dart';
import '../../data/models/api_response.dart';
import '../../data/models/dashboard.dart';
import '../../data/models/alert.dart';
import 'api_client.dart';

class DashboardService {
  static final DashboardService _instance = DashboardService._internal();
  factory DashboardService() => _instance;
  DashboardService._internal();

  final ApiClient _apiClient = ApiClient();

  // Get dashboard overview
  Future<ApiResponse<DashboardOverview>> getDashboardOverview({
    String? propertyIds,
    String timeRange = '24h',
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'timeRange': timeRange,
      };
      
      if (propertyIds != null) {
        queryParams['propertyIds'] = propertyIds;
      }

      final response = await _apiClient.get<DashboardOverview>(
        ApiConstants.dashboardOverview,
        queryParameters: queryParams,
        fromJson: (json) => DashboardOverview.fromJson(json),
      );

      return response;
    } catch (e) {
      return ApiResponse<DashboardOverview>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to fetch dashboard overview: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Get alerts with pagination and filtering
  Future<ApiResponse<PaginatedResponse<Alert>>> getAlerts({
    int page = 1,
    int limit = ApiConstants.defaultPageSize,
    String? severity,
    String? status,
    String? propertyId,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page.toString(),
        'limit': limit.toString(),
      };
      
      if (severity != null) queryParams['severity'] = severity;
      if (status != null) queryParams['status'] = status;
      if (propertyId != null) queryParams['propertyId'] = propertyId;

      final response = await _apiClient.get<PaginatedResponse<Alert>>(
        ApiConstants.dashboardAlerts,
        queryParameters: queryParams,
        fromJson: (json) => PaginatedResponse.fromJson(
          json,
          (item) => Alert.fromJson(item as Map<String, dynamic>),
        ),
      );

      return response;
    } catch (e) {
      return ApiResponse<PaginatedResponse<Alert>>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to fetch alerts: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Create new alert
  Future<ApiResponse<Alert>> createAlert({
    String? propertyId,
    required String title,
    required String message,
    required String severity,
    String? category,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final data = {
        'title': title,
        'message': message,
        'severity': severity,
        if (propertyId != null) 'propertyId': propertyId,
        if (category != null) 'category': category,
        if (metadata != null) 'metadata': metadata,
      };

      final response = await _apiClient.post<Alert>(
        ApiConstants.dashboardAlerts,
        data: data,
        fromJson: (json) => Alert.fromJson(json),
      );

      return response;
    } catch (e) {
      return ApiResponse<Alert>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to create alert: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Update alert status
  Future<ApiResponse<Alert>> updateAlert({
    required String alertId,
    String? status,
    String? resolvedAt,
  }) async {
    try {
      final data = <String, dynamic>{};
      if (status != null) data['status'] = status;
      if (resolvedAt != null) data['resolvedAt'] = resolvedAt;

      final response = await _apiClient.put<Alert>(
        '${ApiConstants.dashboardAlerts}/$alertId',
        data: data,
        fromJson: (json) => Alert.fromJson(json),
      );

      return response;
    } catch (e) {
      return ApiResponse<Alert>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to update alert: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Acknowledge alert
  Future<ApiResponse<Alert>> acknowledgeAlert(String alertId) async {
    return updateAlert(
      alertId: alertId,
      status: 'ACKNOWLEDGED',
    );
  }

  // Resolve alert
  Future<ApiResponse<Alert>> resolveAlert(String alertId) async {
    return updateAlert(
      alertId: alertId,
      status: 'RESOLVED',
      resolvedAt: DateTime.now().toIso8601String(),
    );
  }

  // Get time ranges for dropdown
  List<String> getTimeRanges() {
    return ApiConstants.timeRanges;
  }

  // Get alert severities for dropdown
  List<String> getAlertSeverities() {
    return ApiConstants.alertSeverities;
  }

  // Get alert statuses for dropdown
  List<String> getAlertStatuses() {
    return ApiConstants.alertStatusesList;
  }

  // Helper method to get time range display name
  String getTimeRangeDisplayName(String timeRange) {
    switch (timeRange) {
      case '24h':
        return 'Last 24 Hours';
      case '7d':
        return 'Last 7 Days';
      case '30d':
        return 'Last 30 Days';
      case '90d':
        return 'Last 90 Days';
      default:
        return timeRange;
    }
  }

  // Helper method to get alert severity display info
  Map<String, dynamic> getAlertSeverityInfo(String severity) {
    switch (severity) {
      case 'LOW':
        return {
          'name': 'Low',
          'color': 0xFF4CAF50, // Green
          'icon': 'info',
        };
      case 'MEDIUM':
        return {
          'name': 'Medium',
          'color': 0xFF2196F3, // Blue
          'icon': 'info',
        };
      case 'HIGH':
        return {
          'name': 'High',
          'color': 0xFFFF9800, // Orange
          'icon': 'warning',
        };
      case 'CRITICAL':
        return {
          'name': 'Critical',
          'color': 0xFFF44336, // Red
          'icon': 'error',
        };
      default:
        return {
          'name': severity,
          'color': 0xFF9E9E9E,
          'icon': 'help',
        };
    }
  }

  // Helper method to get alert status display info
  Map<String, dynamic> getAlertStatusInfo(String status) {
    switch (status) {
      case 'OPEN':
        return {
          'name': 'Open',
          'color': 0xFFF44336, // Red
          'icon': 'error_outline',
        };
      case 'ACKNOWLEDGED':
        return {
          'name': 'Acknowledged',
          'color': 0xFFFF9800, // Orange
          'icon': 'visibility',
        };
      case 'RESOLVED':
        return {
          'name': 'Resolved',
          'color': 0xFF4CAF50, // Green
          'icon': 'check_circle',
        };
      default:
        return {
          'name': status,
          'color': 0xFF9E9E9E,
          'icon': 'help',
        };
    }
  }

  // Calculate dashboard statistics
  Map<String, dynamic> calculateDashboardStats(DashboardOverview overview) {
    final properties = overview.properties;
    final alerts = overview.recentAlerts;
    
    // Property statistics
    final totalProperties = properties.length;
    final healthyProperties = properties.where((p) => p.isHealthy).length;
    final criticalProperties = properties.where((p) => p.isCritical).length;
    final offlineProperties = properties.where((p) => p.isOffline).length;
    
    // Alert statistics
    final totalAlerts = alerts.length;
    final criticalAlerts = alerts.where((a) => a.isCritical).length;
    final openAlerts = alerts.where((a) => a.isOpen).length;
    
    // System health average
    final avgHealthScore = properties.isNotEmpty
        ? properties.map((p) => p.healthScore).reduce((a, b) => a + b) / properties.length
        : 0.0;
    
    return {
      'totalProperties': totalProperties,
      'healthyProperties': healthyProperties,
      'criticalProperties': criticalProperties,
      'offlineProperties': offlineProperties,
      'totalAlerts': totalAlerts,
      'criticalAlerts': criticalAlerts,
      'openAlerts': openAlerts,
      'avgHealthScore': avgHealthScore,
      'healthPercentage': totalProperties > 0 ? (healthyProperties / totalProperties) * 100 : 0,
    };
  }

  // Get system health color based on score
  int getHealthScoreColor(double score) {
    if (score >= 80) return 0xFF4CAF50; // Green
    if (score >= 60) return 0xFFFF9800; // Orange
    if (score >= 40) return 0xFFF44336; // Red
    return 0xFF9E9E9E; // Grey
  }

  // Format health score for display
  String formatHealthScore(double score) {
    return '${score.toStringAsFixed(1)}%';
  }

  // Get trend direction based on data
  String getTrendDirection(List<TrendData> trends) {
    if (trends.length < 2) return 'stable';
    
    final latest = trends.last.value;
    final previous = trends[trends.length - 2].value;
    
    if (latest > previous) return 'up';
    if (latest < previous) return 'down';
    return 'stable';
  }
}
