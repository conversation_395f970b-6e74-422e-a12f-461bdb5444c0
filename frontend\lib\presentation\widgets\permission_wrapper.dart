import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/auth_providers.dart';

/// A widget that wraps its child and only displays it if the user has the required permission
class PermissionWrapper extends ConsumerWidget {
  final String permission;
  final String? propertyId;
  final Widget child;
  final Widget? fallback;
  final bool showFallbackOnError;

  const PermissionWrapper({
    super.key,
    required this.permission,
    this.propertyId,
    required this.child,
    this.fallback,
    this.showFallbackOnError = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userPermissions = ref.watch(userPermissionsProvider);
    
    // Check if user has the required permission
    final hasPermission = _checkPermission(userPermissions, permission, propertyId);
    
    if (hasPermission) {
      return child;
    } else {
      return fallback ?? const SizedBox.shrink();
    }
  }

  bool _checkPermission(UserPermissions userPermissions, String permission, String? propertyId) {
    // Check if user has the specific permission
    if (!userPermissions.hasPermission(permission)) {
      return false;
    }

    // If propertyId is specified, check property-based access
    if (propertyId != null && 
        userPermissions.assignedPropertyIds.isNotEmpty &&
        !userPermissions.assignedPropertyIds.contains(propertyId)) {
      return false;
    }

    return true;
  }
}

/// A widget that shows different content based on user permissions
class ConditionalPermissionWidget extends ConsumerWidget {
  final String permission;
  final String? propertyId;
  final Widget authorizedChild;
  final Widget unauthorizedChild;

  const ConditionalPermissionWidget({
    super.key,
    required this.permission,
    this.propertyId,
    required this.authorizedChild,
    required this.unauthorizedChild,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userPermissions = ref.watch(userPermissionsProvider);
    
    // Check if user has the required permission
    final hasPermission = _checkPermission(userPermissions, permission, propertyId);
    
    return hasPermission ? authorizedChild : unauthorizedChild;
  }

  bool _checkPermission(UserPermissions userPermissions, String permission, String? propertyId) {
    // Check if user has the specific permission
    if (!userPermissions.hasPermission(permission)) {
      return false;
    }

    // If propertyId is specified, check property-based access
    if (propertyId != null && 
        userPermissions.assignedPropertyIds.isNotEmpty &&
        !userPermissions.assignedPropertyIds.contains(propertyId)) {
      return false;
    }

    return true;
  }
}

/// A widget that shows a permission denied message
class PermissionDeniedWidget extends StatelessWidget {
  final String? message;
  final IconData? icon;
  final VoidCallback? onRetry;

  const PermissionDeniedWidget({
    super.key,
    this.message,
    this.icon,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon ?? Icons.lock_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              message ?? 'Access Denied',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'You don\'t have permission to access this content.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('Retry'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// A mixin that provides permission checking functionality
mixin PermissionMixin {
  bool hasPermission(WidgetRef ref, String permission, {String? propertyId}) {
    final userPermissions = ref.read(userPermissionsProvider);
    
    // Check if user has the specific permission
    if (!userPermissions.hasPermission(permission)) {
      return false;
    }

    // If propertyId is specified, check property-based access
    if (propertyId != null && 
        userPermissions.assignedPropertyIds.isNotEmpty &&
        !userPermissions.assignedPropertyIds.contains(propertyId)) {
      return false;
    }

    return true;
  }

  void showPermissionDeniedSnackBar(BuildContext context, {String? message}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message ?? 'You don\'t have permission to perform this action'),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}

/// Extension on WidgetRef for easier permission checking
extension PermissionExtension on WidgetRef {
  bool hasPermission(String permission, {String? propertyId}) {
    final userPermissions = read(userPermissionsProvider);
    
    // Check if user has the specific permission
    if (!userPermissions.hasPermission(permission)) {
      return false;
    }

    // If propertyId is specified, check property-based access
    if (propertyId != null && 
        userPermissions.assignedPropertyIds.isNotEmpty &&
        !userPermissions.assignedPropertyIds.contains(propertyId)) {
      return false;
    }

    return true;
  }
}
