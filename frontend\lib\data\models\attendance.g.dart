// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'attendance.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Attendance _$AttendanceFromJson(Map<String, dynamic> json) => Attendance(
      id: json['id'] as String,
      employeeId: json['employeeId'] as String,
      officeId: json['officeId'] as String,
      status: json['status'] as String,
      type: json['type'] as String,
      timestamp: json['timestamp'] as String,
      location: json['location'] as String?,
      notes: json['notes'] as String?,
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
      employee: json['employee'] == null
          ? null
          : EmployeeInfo.fromJson(json['employee'] as Map<String, dynamic>),
      office: json['office'] == null
          ? null
          : OfficeInfo.fromJson(json['office'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$AttendanceToJson(Attendance instance) =>
    <String, dynamic>{
      'id': instance.id,
      'employeeId': instance.employeeId,
      'officeId': instance.officeId,
      'status': instance.status,
      'type': instance.type,
      'timestamp': instance.timestamp,
      'location': instance.location,
      'notes': instance.notes,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
      'employee': instance.employee,
      'office': instance.office,
    };

EmployeeInfo _$EmployeeInfoFromJson(Map<String, dynamic> json) => EmployeeInfo(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      department: json['department'] as String?,
      position: json['position'] as String?,
    );

Map<String, dynamic> _$EmployeeInfoToJson(EmployeeInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'department': instance.department,
      'position': instance.position,
    };

OfficeInfo _$OfficeInfoFromJson(Map<String, dynamic> json) => OfficeInfo(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
      address: json['address'] as String?,
    );

Map<String, dynamic> _$OfficeInfoToJson(OfficeInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
      'address': instance.address,
    };

AttendanceStatistics _$AttendanceStatisticsFromJson(
        Map<String, dynamic> json) =>
    AttendanceStatistics(
      totalEmployees: (json['totalEmployees'] as num).toInt(),
      presentEmployees: (json['presentEmployees'] as num).toInt(),
      absentEmployees: (json['absentEmployees'] as num).toInt(),
      lateEmployees: (json['lateEmployees'] as num).toInt(),
      onLeaveEmployees: (json['onLeaveEmployees'] as num).toInt(),
      attendanceRate: (json['attendanceRate'] as num).toDouble(),
      attendanceByDepartment:
          Map<String, int>.from(json['attendanceByDepartment'] as Map),
      attendanceByHour: Map<String, int>.from(json['attendanceByHour'] as Map),
    );

Map<String, dynamic> _$AttendanceStatisticsToJson(
        AttendanceStatistics instance) =>
    <String, dynamic>{
      'totalEmployees': instance.totalEmployees,
      'presentEmployees': instance.presentEmployees,
      'absentEmployees': instance.absentEmployees,
      'lateEmployees': instance.lateEmployees,
      'onLeaveEmployees': instance.onLeaveEmployees,
      'attendanceRate': instance.attendanceRate,
      'attendanceByDepartment': instance.attendanceByDepartment,
      'attendanceByHour': instance.attendanceByHour,
    };

CreateAttendanceRequest _$CreateAttendanceRequestFromJson(
        Map<String, dynamic> json) =>
    CreateAttendanceRequest(
      employeeId: json['employeeId'] as String,
      officeId: json['officeId'] as String,
      status: json['status'] as String,
      type: json['type'] as String,
      timestamp: json['timestamp'] as String,
      location: json['location'] as String?,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$CreateAttendanceRequestToJson(
        CreateAttendanceRequest instance) =>
    <String, dynamic>{
      'employeeId': instance.employeeId,
      'officeId': instance.officeId,
      'status': instance.status,
      'type': instance.type,
      'timestamp': instance.timestamp,
      'location': instance.location,
      'notes': instance.notes,
    };

UpdateAttendanceRequest _$UpdateAttendanceRequestFromJson(
        Map<String, dynamic> json) =>
    UpdateAttendanceRequest(
      status: json['status'] as String?,
      type: json['type'] as String?,
      timestamp: json['timestamp'] as String?,
      location: json['location'] as String?,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$UpdateAttendanceRequestToJson(
        UpdateAttendanceRequest instance) =>
    <String, dynamic>{
      'status': instance.status,
      'type': instance.type,
      'timestamp': instance.timestamp,
      'location': instance.location,
      'notes': instance.notes,
    };

AttendanceQueryParams _$AttendanceQueryParamsFromJson(
        Map<String, dynamic> json) =>
    AttendanceQueryParams(
      page: (json['page'] as num?)?.toInt(),
      limit: (json['limit'] as num?)?.toInt(),
      status: json['status'] as String?,
      type: json['type'] as String?,
      employeeId: json['employeeId'] as String?,
      officeId: json['officeId'] as String?,
      dateFrom: json['dateFrom'] as String?,
      dateTo: json['dateTo'] as String?,
    );

Map<String, dynamic> _$AttendanceQueryParamsToJson(
        AttendanceQueryParams instance) =>
    <String, dynamic>{
      'page': instance.page,
      'limit': instance.limit,
      'status': instance.status,
      'type': instance.type,
      'employeeId': instance.employeeId,
      'officeId': instance.officeId,
      'dateFrom': instance.dateFrom,
      'dateTo': instance.dateTo,
    };
