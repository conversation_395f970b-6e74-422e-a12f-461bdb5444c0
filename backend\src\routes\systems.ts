import express from 'express';
import { authenticateToken } from '../middleware/auth';
import { rbac } from '../middleware/rbac';
import {
  getSystemStatuses,
  getWaterSystems,
  getElectricitySystems,
  getSecuritySystems,
  updateSystemStatus
} from '../controllers/systemController';

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

/**
 * @swagger
 * /v1/systems/status:
 *   get:
 *     summary: Get all system statuses with filtering
 *     tags: [Systems]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: propertyId
 *         schema:
 *           type: string
 *         description: Filter by property ID
 *       - in: query
 *         name: systemType
 *         schema:
 *           type: string
 *           enum: [WATER, ELECTRICITY, SECURITY, INTERNET, OTT, MAINTENANCE]
 *         description: Filter by system type
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [OPERATIONAL, WARNING, CRITIC<PERSON>, OFFLINE]
 *         description: Filter by status
 *     responses:
 *       200:
 *         description: System statuses retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       propertyId:
 *                         type: string
 *                       systemType:
 *                         type: string
 *                       status:
 *                         type: string
 *                       healthScore:
 *                         type: integer
 *                       description:
 *                         type: string
 *                       lastChecked:
 *                         type: string
 *                         format: date-time
 *                       property:
 *                         type: object
 *       403:
 *         description: Access denied
 *       500:
 *         description: Server error
 */
router.get('/status', rbac(['SUPER_ADMIN', 'PROPERTY_MANAGER', 'EMPLOYEE']), getSystemStatuses);

/**
 * @swagger
 * /v1/systems/water/{propertyId}:
 *   get:
 *     summary: Get water systems for a property
 *     tags: [Systems]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         required: true
 *         schema:
 *           type: string
 *         description: Property ID
 *     responses:
 *       200:
 *         description: Water systems retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     systems:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           propertyId:
 *                             type: string
 *                           tankName:
 *                             type: string
 *                           capacity:
 *                             type: number
 *                           currentLevel:
 *                             type: number
 *                           levelPercentage:
 *                             type: number
 *                           pumpStatus:
 *                             type: string
 *                           flowRate:
 *                             type: number
 *                           pressure:
 *                             type: number
 *                           quality:
 *                             type: string
 *                           lastMaintenance:
 *                             type: string
 *                             format: date-time
 *                           nextMaintenance:
 *                             type: string
 *                             format: date-time
 *                     summary:
 *                       type: object
 *                       properties:
 *                         totalSystems:
 *                           type: integer
 *                         totalCapacity:
 *                           type: number
 *                         totalCurrentLevel:
 *                           type: number
 *                         averageLevel:
 *                           type: integer
 *                         activePumps:
 *                           type: integer
 *                         maintenanceRequired:
 *                           type: integer
 *       403:
 *         description: Access denied
 *       500:
 *         description: Server error
 */
router.get('/water/:propertyId', rbac(['SUPER_ADMIN', 'PROPERTY_MANAGER', 'EMPLOYEE']), getWaterSystems);

/**
 * @swagger
 * /v1/systems/electricity/{propertyId}:
 *   get:
 *     summary: Get electricity systems for a property
 *     tags: [Systems]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         required: true
 *         schema:
 *           type: string
 *         description: Property ID
 *     responses:
 *       200:
 *         description: Electricity systems retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     systems:
 *                       type: array
 *                       items:
 *                         type: object
 *                     summary:
 *                       type: object
 *       403:
 *         description: Access denied
 *       500:
 *         description: Server error
 */
router.get('/electricity/:propertyId', rbac(['SUPER_ADMIN', 'PROPERTY_MANAGER', 'EMPLOYEE']), getElectricitySystems);

/**
 * @swagger
 * /v1/systems/security/{propertyId}:
 *   get:
 *     summary: Get security systems for a property
 *     tags: [Systems]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         required: true
 *         schema:
 *           type: string
 *         description: Property ID
 *     responses:
 *       200:
 *         description: Security systems retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     systems:
 *                       type: array
 *                       items:
 *                         type: object
 *                     summary:
 *                       type: object
 *       403:
 *         description: Access denied
 *       500:
 *         description: Server error
 */
router.get('/security/:propertyId', rbac(['SUPER_ADMIN', 'PROPERTY_MANAGER', 'EMPLOYEE']), getSecuritySystems);

/**
 * @swagger
 * /v1/systems/status/{id}:
 *   put:
 *     summary: Update system status
 *     tags: [Systems]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: System status ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [OPERATIONAL, WARNING, CRITICAL, OFFLINE]
 *               description:
 *                 type: string
 *               healthScore:
 *                 type: integer
 *                 minimum: 0
 *                 maximum: 100
 *               metadata:
 *                 type: object
 *     responses:
 *       200:
 *         description: System status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                 message:
 *                   type: string
 *       403:
 *         description: Access denied
 *       404:
 *         description: System status not found
 *       500:
 *         description: Server error
 */
router.put('/status/:id', rbac(['SUPER_ADMIN', 'PROPERTY_MANAGER']), updateSystemStatus);

export default router;
