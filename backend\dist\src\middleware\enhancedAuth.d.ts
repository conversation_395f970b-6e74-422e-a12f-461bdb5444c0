import { Request, Response, NextFunction } from 'express';
import { PermissionContext } from '@/services/PermissionService';
export declare const enhancedAuthorize: (resource: string, action: string, options?: {
    requirePropertyAccess?: boolean;
    requireOfficeAccess?: boolean;
    customConditions?: Record<string, any>;
}) => (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const applyDataFiltering: (resource: string) => (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const attachUIPermissions: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const filterResponseContent: (allowedFields?: string[], sensitiveFields?: string[]) => (req: Request, res: Response, next: NextFunction) => void;
declare global {
    namespace Express {
        interface Request {
            permissionContext?: PermissionContext;
            permissionResult?: any;
            filteredQuery?: any;
            uiPermissions?: any;
        }
    }
}
