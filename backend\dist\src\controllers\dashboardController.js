"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateDashboardMetrics = exports.getDashboardMetrics = exports.getDashboardOverview = void 0;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
// Get dashboard overview with real-time data
const getDashboardOverview = async (req, res) => {
    try {
        const userId = req.user?.id;
        const userRole = req.user?.role;
        const { propertyId } = req.query;
        // Build property filter based on user permissions
        let propertyFilter = {};
        if (userRole !== 'SUPER_ADMIN') {
            // Get user's assigned properties
            const userProperties = await prisma.userProperty.findMany({
                where: { userId },
                select: { propertyId: true }
            });
            const assignedPropertyIds = userProperties.map(up => up.propertyId);
            if (assignedPropertyIds.length > 0) {
                propertyFilter = { id: { in: assignedPropertyIds } };
            }
            else {
                // User has no assigned properties
                return res.json({
                    success: true,
                    data: {
                        totalProperties: 0,
                        activeProperties: 0,
                        totalAlerts: 0,
                        criticalAlerts: 0,
                        systemsOperational: 0,
                        systemsTotal: 0,
                        maintenanceOpen: 0,
                        maintenanceOverdue: 0,
                        properties: [],
                        systemHealth: [],
                        recentAlerts: [],
                        systemStatus: []
                    }
                });
            }
        }
        // If specific property requested, add to filter
        if (propertyId) {
            propertyFilter.id = propertyId;
        }
        // Get properties with system data
        const properties = await prisma.property.findMany({
            where: propertyFilter,
            include: {
                systemStatuses: true,
                alerts: {
                    where: { status: { in: ['OPEN', 'ACKNOWLEDGED'] } },
                    orderBy: { createdAt: 'desc' }
                },
                maintenanceIssues: {
                    where: { status: { in: ['OPEN', 'IN_PROGRESS'] } }
                },
                waterSystems: true,
                electricitySystems: true,
                securitySystems: true
            }
        });
        // Calculate overview metrics
        const totalProperties = properties.length;
        const activeProperties = properties.filter(p => p.isActive).length;
        // Calculate alerts
        const allAlerts = properties.flatMap(p => p.alerts);
        const totalAlerts = allAlerts.length;
        const criticalAlerts = allAlerts.filter(a => a.severity === 'CRITICAL').length;
        // Calculate system health
        const allSystemStatuses = properties.flatMap(p => p.systemStatuses);
        const systemsTotal = allSystemStatuses.length;
        const systemsOperational = allSystemStatuses.filter(s => s.status === 'OPERATIONAL').length;
        // Calculate maintenance
        const allMaintenanceIssues = properties.flatMap(p => p.maintenanceIssues);
        const maintenanceOpen = allMaintenanceIssues.length;
        const maintenanceOverdue = allMaintenanceIssues.filter(issue => {
            if (issue.expectedEndDate) {
                return new Date(issue.expectedEndDate) < new Date();
            }
            return false;
        }).length;
        // Get recent alerts (last 10)
        const recentAlerts = await prisma.alert.findMany({
            where: {
                propertyId: propertyFilter.id ? { in: Array.isArray(propertyFilter.id) ? propertyFilter.id : [propertyFilter.id] } : undefined,
                status: { in: ['OPEN', 'ACKNOWLEDGED'] }
            },
            include: {
                property: {
                    select: { id: true, name: true }
                }
            },
            orderBy: { createdAt: 'desc' },
            take: 10
        });
        // Prepare properties quick view
        const propertiesQuickView = properties.map(property => {
            const systemStatuses = property.systemStatuses;
            const alerts = property.alerts;
            const maintenanceIssues = property.maintenanceIssues;
            // Calculate health score
            const operationalSystems = systemStatuses.filter(s => s.status === 'OPERATIONAL').length;
            const totalSystems = systemStatuses.length;
            const healthScore = totalSystems > 0 ? Math.round((operationalSystems / totalSystems) * 100) : 100;
            return {
                id: property.id,
                name: property.name,
                type: property.type,
                address: property.address,
                isActive: property.isActive,
                healthScore,
                systemsOperational: operationalSystems,
                systemsTotal: totalSystems,
                alertsCount: alerts.length,
                criticalAlertsCount: alerts.filter(a => a.severity === 'CRITICAL').length,
                maintenanceCount: maintenanceIssues.length,
                lastUpdated: property.updatedAt
            };
        });
        // Prepare system health summary
        const systemHealthSummary = [
            {
                systemType: 'WATER',
                total: properties.reduce((sum, p) => sum + p.waterSystems.length, 0),
                operational: properties.reduce((sum, p) => sum + p.waterSystems.filter(w => w.pumpStatus === 'ON').length, 0),
                warning: properties.reduce((sum, p) => sum + p.waterSystems.filter(w => w.levelPercentage < 30 && w.levelPercentage >= 10).length, 0),
                critical: properties.reduce((sum, p) => sum + p.waterSystems.filter(w => w.levelPercentage < 10).length, 0)
            },
            {
                systemType: 'ELECTRICITY',
                total: properties.reduce((sum, p) => sum + p.electricitySystems.length, 0),
                operational: properties.reduce((sum, p) => sum + p.electricitySystems.filter(e => e.generatorStatus === 'ON' || e.mainsPowerStatus === 'AVAILABLE').length, 0),
                warning: properties.reduce((sum, p) => sum + p.electricitySystems.filter(e => (e.fuelLevel && e.fuelLevel < 30 && e.fuelLevel >= 10)).length, 0),
                critical: properties.reduce((sum, p) => sum + p.electricitySystems.filter(e => (e.fuelLevel && e.fuelLevel < 10)).length, 0)
            },
            {
                systemType: 'SECURITY',
                total: properties.reduce((sum, p) => sum + p.securitySystems.length, 0),
                operational: properties.reduce((sum, p) => sum + p.securitySystems.filter(s => s.alarmStatus === 'ARMED').length, 0),
                warning: properties.reduce((sum, p) => sum + p.securitySystems.filter(s => s.activeCameras < s.cameraCount).length, 0),
                critical: properties.reduce((sum, p) => sum + p.securitySystems.filter(s => s.alarmStatus === 'TRIGGERED').length, 0)
            }
        ];
        // Prepare system status overview
        const systemStatusOverview = allSystemStatuses.map(status => ({
            id: status.id,
            propertyId: status.propertyId,
            systemType: status.systemType,
            status: status.status,
            healthScore: status.healthScore,
            description: status.description,
            lastChecked: status.lastChecked
        }));
        const dashboardData = {
            totalProperties,
            activeProperties,
            totalAlerts,
            criticalAlerts,
            systemsOperational,
            systemsTotal,
            maintenanceOpen,
            maintenanceOverdue,
            properties: propertiesQuickView,
            systemHealth: systemHealthSummary,
            recentAlerts: recentAlerts.map(alert => ({
                id: alert.id,
                title: alert.title,
                message: alert.message,
                severity: alert.severity,
                status: alert.status,
                category: alert.category,
                propertyName: alert.property?.name || 'Unknown',
                createdAt: alert.createdAt
            })),
            systemStatus: systemStatusOverview
        };
        res.json({
            success: true,
            data: dashboardData
        });
    }
    catch (error) {
        console.error('Error fetching dashboard overview:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch dashboard overview',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};
exports.getDashboardOverview = getDashboardOverview;
// Get dashboard metrics for a specific date range
const getDashboardMetrics = async (req, res) => {
    try {
        const { propertyId, dateFrom, dateTo } = req.query;
        const userId = req.user?.id;
        const userRole = req.user?.role;
        // Build filter
        let filter = {};
        // Property-based access control
        if (userRole !== 'SUPER_ADMIN') {
            const userProperties = await prisma.userProperty.findMany({
                where: { userId },
                select: { propertyId: true }
            });
            const assignedPropertyIds = userProperties.map(up => up.propertyId);
            if (assignedPropertyIds.length > 0) {
                filter.propertyId = { in: assignedPropertyIds };
            }
            else {
                return res.json({ success: true, data: [] });
            }
        }
        if (propertyId) {
            filter.propertyId = propertyId;
        }
        // Date range filter
        if (dateFrom || dateTo) {
            filter.date = {};
            if (dateFrom)
                filter.date.gte = new Date(dateFrom);
            if (dateTo)
                filter.date.lte = new Date(dateTo);
        }
        const metrics = await prisma.dashboardMetrics.findMany({
            where: filter,
            include: {
                property: {
                    select: { id: true, name: true, type: true }
                }
            },
            orderBy: { date: 'desc' }
        });
        res.json({
            success: true,
            data: metrics
        });
    }
    catch (error) {
        console.error('Error fetching dashboard metrics:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch dashboard metrics',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};
exports.getDashboardMetrics = getDashboardMetrics;
// Update dashboard metrics (typically called by a scheduled job)
const updateDashboardMetrics = async (req, res) => {
    try {
        const { propertyId } = req.body;
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        // Get properties to update
        const properties = propertyId
            ? await prisma.property.findMany({ where: { id: propertyId } })
            : await prisma.property.findMany({ where: { isActive: true } });
        const updatedMetrics = [];
        for (const property of properties) {
            // Calculate metrics for this property
            const [totalAlerts, criticalAlerts, systemStatuses, maintenanceIssues] = await Promise.all([
                prisma.alert.count({
                    where: { propertyId: property.id, status: { in: ['OPEN', 'ACKNOWLEDGED'] } }
                }),
                prisma.alert.count({
                    where: { propertyId: property.id, severity: 'CRITICAL', status: { in: ['OPEN', 'ACKNOWLEDGED'] } }
                }),
                prisma.systemStatus.findMany({
                    where: { propertyId: property.id }
                }),
                prisma.maintenanceIssue.findMany({
                    where: { propertyId: property.id, status: { in: ['OPEN', 'IN_PROGRESS'] } }
                })
            ]);
            const systemsTotal = systemStatuses.length;
            const systemsOperational = systemStatuses.filter(s => s.status === 'OPERATIONAL').length;
            const maintenanceOpen = maintenanceIssues.length;
            const maintenanceOverdue = maintenanceIssues.filter(issue => {
                if (issue.expectedEndDate) {
                    return new Date(issue.expectedEndDate) < new Date();
                }
                return false;
            }).length;
            // Upsert metrics for today
            const metrics = await prisma.dashboardMetrics.upsert({
                where: {
                    propertyId_date: {
                        propertyId: property.id,
                        date: today
                    }
                },
                update: {
                    totalAlerts,
                    criticalAlerts,
                    systemsOperational,
                    systemsTotal,
                    maintenanceOpen,
                    maintenanceOverdue
                },
                create: {
                    propertyId: property.id,
                    totalProperties: 1,
                    activeProperties: property.isActive ? 1 : 0,
                    totalAlerts,
                    criticalAlerts,
                    systemsOperational,
                    systemsTotal,
                    maintenanceOpen,
                    maintenanceOverdue,
                    date: today
                }
            });
            updatedMetrics.push(metrics);
        }
        res.json({
            success: true,
            data: updatedMetrics,
            message: `Updated metrics for ${updatedMetrics.length} properties`
        });
    }
    catch (error) {
        console.error('Error updating dashboard metrics:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update dashboard metrics',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};
exports.updateDashboardMetrics = updateDashboardMetrics;
