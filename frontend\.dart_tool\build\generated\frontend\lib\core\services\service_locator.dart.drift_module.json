{"elements": {}, "imports": ["package:flutter/foundation.dart", "package:frontend/core/services/api_client.dart", "package:frontend/core/services/auth_service.dart", "package:frontend/core/services/property_service.dart", "package:frontend/core/services/dashboard_service.dart", "package:frontend/core/services/office_service.dart", "package:frontend/core/services/notification_service.dart", "package:frontend/core/services/file_service.dart", "package:frontend/core/services/cache_manager.dart", "package:frontend/core/services/connectivity_manager.dart", "package:frontend/data/repositories/alert_repository.dart", "package:frontend/data/repositories/auth_repository.dart", "package:frontend/data/repositories/property_repository.dart", "package:frontend/data/repositories/office_repository.dart", "package:frontend/data/repositories/system_repository.dart", "package:frontend/data/repositories/maintenance_repository.dart"]}