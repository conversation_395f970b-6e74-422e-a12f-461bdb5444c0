{"valid_import": true, "imports": [{"uri": "package:flutter/material.dart", "transitive": false}, {"uri": "package:flutter_riverpod/flutter_riverpod.dart", "transitive": false}, {"uri": "package:provider/provider.dart", "transitive": false}, {"uri": "package:frontend/core/providers/permission_provider.dart", "transitive": false}, {"uri": "package:frontend/data/services/rbac_service.dart", "transitive": false}, {"uri": "package:frontend/presentation/providers/auth_provider.dart", "transitive": false}], "elements": []}