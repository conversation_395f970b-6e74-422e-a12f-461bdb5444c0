import 'package:json_annotation/json_annotation.dart';
import 'property.dart';
import 'alert.dart';
import 'activity.dart';

part 'dashboard.g.dart';

@JsonSerializable()
class DashboardOverview {
  final DashboardSummary summary;
  final List<PropertySummary> properties;
  final List<Alert> recentAlerts;
  final List<SystemStatusOverview> systemStatuses;
  final List<Activity> activities;
  final DashboardStatistics statistics;

  const DashboardOverview({
    required this.summary,
    required this.properties,
    required this.recentAlerts,
    required this.systemStatuses,
    required this.activities,
    required this.statistics,
  });

  factory DashboardOverview.fromJson(Map<String, dynamic> json) =>
      _$DashboardOverviewFromJson(json);

  Map<String, dynamic> toJson() => _$DashboardOverviewToJson(this);
}

@JsonSerializable()
class DashboardSummary {
  final int totalProperties;
  final int activeProperties;
  final int totalAlerts;
  final int criticalAlerts;
  final double systemHealth;

  const DashboardSummary({
    required this.totalProperties,
    required this.activeProperties,
    required this.totalAlerts,
    required this.criticalAlerts,
    required this.systemHealth,
  });

  factory DashboardSummary.fromJson(Map<String, dynamic> json) =>
      _$DashboardSummaryFromJson(json);

  Map<String, dynamic> toJson() => _$DashboardSummaryToJson(this);
}

@JsonSerializable()
class PropertySummary {
  final String id;
  final String name;
  final String type;
  final String status;
  final double healthScore;
  final int alertCount;
  final String lastUpdate;

  const PropertySummary({
    required this.id,
    required this.name,
    required this.type,
    required this.status,
    required this.healthScore,
    required this.alertCount,
    required this.lastUpdate,
  });

  factory PropertySummary.fromJson(Map<String, dynamic> json) =>
      _$PropertySummaryFromJson(json);

  Map<String, dynamic> toJson() => _$PropertySummaryToJson(this);

  // Helper getters
  bool get isHealthy => status == 'OPERATIONAL';
  bool get hasWarnings => status == 'WARNING';
  bool get isCritical => status == 'CRITICAL';
  bool get isOffline => status == 'OFFLINE';
  bool get hasAlerts => alertCount > 0;
}

@JsonSerializable()
class SystemStatusOverview {
  final String systemType;
  final int operational;
  final int warning;
  final int critical;
  final int offline;
  final int total;

  const SystemStatusOverview({
    required this.systemType,
    required this.operational,
    required this.warning,
    required this.critical,
    required this.offline,
    required this.total,
  });

  factory SystemStatusOverview.fromJson(Map<String, dynamic> json) =>
      _$SystemStatusOverviewFromJson(json);

  Map<String, dynamic> toJson() => _$SystemStatusOverviewToJson(this);

  // Helper getters
  double get operationalPercentage => total > 0 ? (operational / total) * 100 : 0;
  double get warningPercentage => total > 0 ? (warning / total) * 100 : 0;
  double get criticalPercentage => total > 0 ? (critical / total) * 100 : 0;
  double get offlinePercentage => total > 0 ? (offline / total) * 100 : 0;
  
  bool get isHealthy => critical == 0 && offline == 0;
  bool get hasIssues => critical > 0 || offline > 0;
  bool get hasWarnings => warning > 0;
}

@JsonSerializable()
class DashboardStatistics {
  final String timeRange;
  final DashboardMetrics metrics;
  final List<TrendData> trends;

  const DashboardStatistics({
    required this.timeRange,
    required this.metrics,
    required this.trends,
  });

  factory DashboardStatistics.fromJson(Map<String, dynamic> json) =>
      _$DashboardStatisticsFromJson(json);

  Map<String, dynamic> toJson() => _$DashboardStatisticsToJson(this);
}

@JsonSerializable()
class DashboardMetrics {
  final double uptime;
  final int incidents;
  final int resolved;
  final double avgResponseTime;

  const DashboardMetrics({
    required this.uptime,
    required this.incidents,
    required this.resolved,
    required this.avgResponseTime,
  });

  factory DashboardMetrics.fromJson(Map<String, dynamic> json) =>
      _$DashboardMetricsFromJson(json);

  Map<String, dynamic> toJson() => _$DashboardMetricsToJson(this);

  // Helper getters
  double get resolutionRate => incidents > 0 ? (resolved / incidents) * 100 : 0;
  int get openIncidents => incidents - resolved;
}

@JsonSerializable()
class TrendData {
  final String date;
  final double value;

  const TrendData({
    required this.date,
    required this.value,
  });

  factory TrendData.fromJson(Map<String, dynamic> json) =>
      _$TrendDataFromJson(json);

  Map<String, dynamic> toJson() => _$TrendDataToJson(this);

  DateTime get dateTime => DateTime.parse(date);
}

@JsonSerializable()
class PropertyDetail extends Property {
  @JsonKey(name: 'systemStatuses')
  final List<SystemStatusSummary> systemStatusSummaries;
  final List<Activity> recentActivities;
  final PropertyStatistics statistics;

  const PropertyDetail({
    required super.id,
    required super.name,
    required super.type,
    required super.address,
    required super.city,
    required super.state,
    required super.zipCode,
    required super.country,
    super.description,
    super.totalUnits,
    super.occupiedUnits,
    super.monthlyRent,
    super.propertyValue,
    super.managerId,
    required super.status,
    super.amenities,
    super.images,
    super.coordinates,
    required super.isActive,
    required super.createdAt,
    required super.updatedAt,
    required this.systemStatusSummaries,
    required this.recentActivities,
    required this.statistics,
  }) : super(systemStatuses: const []);

  factory PropertyDetail.fromJson(Map<String, dynamic> json) =>
      _$PropertyDetailFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$PropertyDetailToJson(this);
}

@JsonSerializable()
class SystemStatusSummary {
  final String systemType;
  final String status;
  final String? description;
  final double? healthScore;
  final String lastChecked;

  const SystemStatusSummary({
    required this.systemType,
    required this.status,
    this.description,
    this.healthScore,
    required this.lastChecked,
  });

  factory SystemStatusSummary.fromJson(Map<String, dynamic> json) =>
      _$SystemStatusSummaryFromJson(json);

  Map<String, dynamic> toJson() => _$SystemStatusSummaryToJson(this);

  // Helper getters
  bool get isOperational => status == 'OPERATIONAL';
  bool get isWarning => status == 'WARNING';
  bool get isCritical => status == 'CRITICAL';
  bool get isOffline => status == 'OFFLINE';
  
  DateTime get lastCheckedDateTime => DateTime.parse(lastChecked);
}

@JsonSerializable()
class PropertyStatistics {
  final int totalSystems;
  final int operationalSystems;
  final int warningSystems;
  final int criticalSystems;
  final int offlineSystems;
  final double averageHealthScore;
  final double uptime;
  final String? lastIncident;

  const PropertyStatistics({
    required this.totalSystems,
    required this.operationalSystems,
    required this.warningSystems,
    required this.criticalSystems,
    required this.offlineSystems,
    required this.averageHealthScore,
    required this.uptime,
    this.lastIncident,
  });

  factory PropertyStatistics.fromJson(Map<String, dynamic> json) =>
      _$PropertyStatisticsFromJson(json);

  Map<String, dynamic> toJson() => _$PropertyStatisticsToJson(this);

  // Helper getters
  bool get isHealthy => criticalSystems == 0 && offlineSystems == 0;
  bool get hasIssues => criticalSystems > 0 || offlineSystems > 0;
  bool get hasWarnings => warningSystems > 0;
  
  DateTime? get lastIncidentDateTime => 
      lastIncident != null ? DateTime.parse(lastIncident!) : null;
}
