import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/theme/app_theme.dart';
import '../../../data/models/maintenance.dart';
import '../../providers/maintenance_providers.dart';
import '../../providers/auth_providers.dart';
import '../../widgets/offline_indicator.dart';
import '../main/main_navigation_screen.dart';

// Temporary static model for maintenance issues display
class StaticMaintenanceIssue {
  final String title;
  final String department;
  final DateTime reportedDate;
  final String priority;
  final String status;
  final Color statusColor;
  final String recurrence;

  const StaticMaintenanceIssue({
    required this.title,
    required this.department,
    required this.reportedDate,
    required this.priority,
    required this.status,
    required this.statusColor,
    required this.recurrence,
  });
}

class MaintenanceScreen extends ConsumerStatefulWidget {
  final String? propertyId;

  const MaintenanceScreen({super.key, this.propertyId});

  @override
  ConsumerState<MaintenanceScreen> createState() => _MaintenanceScreenState();
}

class _MaintenanceScreenState extends ConsumerState<MaintenanceScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final userPermissions = ref.watch(userPermissionsProvider);

    // Check RBAC permissions
    if (!userPermissions.hasPermission('maintenance.view')) {
      return Scaffold(
        appBar: CustomAppBar(title: 'Maintenance'),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.lock_outline,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'Access Denied',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'You don\'t have permission to view maintenance data',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Maintenance',
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
          if (userPermissions.hasPermission('maintenance.create'))
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: _showCreateIssueDialog,
            ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppTheme.primaryColor,
          unselectedLabelColor: Colors.grey[600],
          indicatorColor: AppTheme.primaryColor,
          tabs: [
            if (userPermissions.hasPermission('maintenance.create'))
              const Tab(text: 'Submit Issue'),
            const Tab(text: 'Issue Status'),
            const Tab(text: 'Statistics'),
          ],
        ),
      ),
      body: Column(
        children: [
          // Offline Indicator
          const OfflineIndicator(),

          // Tab Bar View
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                if (userPermissions.hasPermission('maintenance.create'))
                  SubmitIssueTab(propertyId: widget.propertyId),
                IssueStatusTab(propertyId: widget.propertyId),
                StatisticsTab(propertyId: widget.propertyId),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showSearchDialog() {
    final currentQuery = ref.read(maintenanceSearchQueryProvider);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Issues'),
        content: TextField(
          autofocus: true,
          controller: TextEditingController(text: currentQuery),
          decoration: const InputDecoration(
            hintText: 'Search by title, description, or department...',
            prefixIcon: Icon(Icons.search),
          ),
          onChanged: (value) {
            ref.read(maintenanceSearchQueryProvider.notifier).state = value;
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              ref.read(maintenanceSearchQueryProvider.notifier).state = '';
              Navigator.of(context).pop();
            },
            child: const Text('Clear'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => FilterDialog(),
    );
  }

  void _showCreateIssueDialog() {
    showDialog(
      context: context,
      builder: (context) => CreateIssueDialog(propertyId: widget.propertyId),
    );
  }
}

class SubmitIssueTab extends ConsumerStatefulWidget {
  final String? propertyId;

  const SubmitIssueTab({super.key, this.propertyId});

  @override
  ConsumerState<SubmitIssueTab> createState() => _SubmitIssueTabState();
}

class _SubmitIssueTabState extends ConsumerState<SubmitIssueTab> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _remarksController = TextEditingController();
  final _reportedByController = TextEditingController();
  
  String? _selectedDepartment;
  String? _selectedPriority = 'Medium - Needs attention soon';
  String? _selectedStatus = 'Open';
  DateTime _startDate = DateTime.now();
  DateTime? _expectedEndDate;
  bool _isRecurring = false;

  final List<String> _departments = [
    'Electricity',
    'Water',
    'Security',
    'Internet',
    'Generator',
    'CCTV',
    'Maintenance',
    'OTTs',
  ];

  final List<String> _priorities = [
    'Low - Not urgent',
    'Medium - Needs attention soon',
    'High - Urgent',
    'Critical - Immediate action required',
  ];

  final List<String> _statuses = [
    'Open',
    'In Progress',
    'Completed',
    'Resolved',
    'Closed',
    'On Hold',
  ];

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Submit an Issue',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Report a new maintenance issue or request',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 24),
                
                // Issue Title
                const Text(
                  'Issue Title',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _titleController,
                  decoration: const InputDecoration(
                    hintText: 'Brief description of the issue',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 16,
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter an issue title';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 20),

                // Department
                const Text(
                  'Department',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                DropdownButtonFormField<String>(
                  value: _selectedDepartment,
                  decoration: const InputDecoration(
                    hintText: 'Select a department',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 16,
                    ),
                  ),
                  items: _departments.map((department) {
                    return DropdownMenuItem(
                      value: department,
                      child: Text(department),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedDepartment = value;
                    });
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please select a department';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 20),

                // Date Row
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Start Date',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(height: 8),
                          InkWell(
                            onTap: () async {
                              final date = await showDatePicker(
                                context: context,
                                initialDate: _startDate,
                                firstDate: DateTime.now(),
                                lastDate: DateTime.now().add(const Duration(days: 365)),
                              );
                              if (date != null) {
                                setState(() {
                                  _startDate = date;
                                });
                              }
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 16,
                              ),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Row(
                                children: [
                                  Text(
                                    '${_startDate.day.toString().padLeft(2, '0')}-${_startDate.month.toString().padLeft(2, '0')}-${_startDate.year}',
                                  ),
                                  const Spacer(),
                                  const Icon(Icons.calendar_today, size: 20),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Expected End Date',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(height: 8),
                          InkWell(
                            onTap: () async {
                              final date = await showDatePicker(
                                context: context,
                                initialDate: _expectedEndDate ?? _startDate.add(const Duration(days: 7)),
                                firstDate: _startDate,
                                lastDate: DateTime.now().add(const Duration(days: 365)),
                              );
                              if (date != null) {
                                setState(() {
                                  _expectedEndDate = date;
                                });
                              }
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 16,
                              ),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Row(
                                children: [
                                  Text(
                                    _expectedEndDate != null
                                        ? '${_expectedEndDate!.day.toString().padLeft(2, '0')}-${_expectedEndDate!.month.toString().padLeft(2, '0')}-${_expectedEndDate!.year}'
                                        : 'dd-mm-yyyy',
                                    style: TextStyle(
                                      color: _expectedEndDate != null
                                          ? Colors.black
                                          : Colors.grey,
                                    ),
                                  ),
                                  const Spacer(),
                                  const Icon(Icons.calendar_today, size: 20),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // Priority
                const Text(
                  'Priority',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                DropdownButtonFormField<String>(
                  value: _selectedPriority,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 16,
                    ),
                  ),
                  items: _priorities.map((priority) {
                    return DropdownMenuItem(
                      value: priority,
                      child: Text(priority),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedPriority = value;
                    });
                  },
                ),
                const SizedBox(height: 20),

                // Status
                const Text(
                  'Status',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                DropdownButtonFormField<String>(
                  value: _selectedStatus,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 16,
                    ),
                  ),
                  items: _statuses.map((status) {
                    return DropdownMenuItem(
                      value: status,
                      child: Text(status),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedStatus = value;
                    });
                  },
                ),
                const SizedBox(height: 20),

                // Reported By
                const Text(
                  'Reported By',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _reportedByController,
                  decoration: const InputDecoration(
                    hintText: 'Your name',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 16,
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                // Recurring Issue Checkbox
                Row(
                  children: [
                    Checkbox(
                      value: _isRecurring,
                      onChanged: (value) {
                        setState(() {
                          _isRecurring = value ?? false;
                        });
                      },
                    ),
                    const Text('Recurring Issue'),
                  ],
                ),
                const SizedBox(height: 20),

                // Remarks
                const Text(
                  'Remarks',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _remarksController,
                  maxLines: 4,
                  decoration: const InputDecoration(
                    hintText: 'Please provide detailed information about the issue',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 16,
                    ),
                  ),
                ),
                const SizedBox(height: 32),

                // Submit Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => _submitIssue(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.black,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    child: const Text(
                      'Submit Issue',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _submitIssue() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      final params = CreateMaintenanceParams(
        propertyId: widget.propertyId ?? '',
        title: _titleController.text.trim(),
        description: _remarksController.text.trim(),
        priority: _selectedPriority!,
        department: _selectedDepartment!,
      );

      final success = await ref.read(createMaintenanceIssueProvider(params).future);

      if (success && mounted) {
        // Clear form
        _titleController.clear();
        _remarksController.clear();
        _reportedByController.clear();
        setState(() {
          _selectedDepartment = null;
          _selectedPriority = 'Medium - Needs attention soon';
          _selectedStatus = 'Open';
          _startDate = DateTime.now();
          _expectedEndDate = null;
          _isRecurring = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Issue submitted successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to submit issue: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _remarksController.dispose();
    _reportedByController.dispose();
    super.dispose();
  }
}

// Issue Status Tab
class IssueStatusTab extends ConsumerStatefulWidget {
  final String? propertyId;

  const IssueStatusTab({super.key, this.propertyId});

  @override
  ConsumerState<IssueStatusTab> createState() => _IssueStatusTabState();
}

class _IssueStatusTabState extends ConsumerState<IssueStatusTab> {
  // Remove static data - will use API data instead

  @override
  Widget build(BuildContext context) {
    final params = MaintenanceParams(
      propertyId: widget.propertyId,
      page: 1,
      limit: 100,
    );

    return Column(
      children: [
        // Header
        Container(
          padding: const EdgeInsets.all(16),
          color: Colors.white,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.list_alt, size: 24),
                  const SizedBox(width: 8),
                  const Text(
                    'Maintenance Issues',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              const Text(
                'Track the status of reported issues',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),

        // Issues List - Using API data
        Expanded(
          child: Consumer(
            builder: (context, ref, child) {
              final issuesAsync = ref.watch(maintenanceIssuesProvider(params));

              return issuesAsync.when(
                data: (issues) => RefreshIndicator(
                  onRefresh: () async {
                    ref.invalidate(maintenanceIssuesProvider(params));
                  },
                  child: issues.isEmpty
                      ? const Center(
                          child: Text(
                            'No maintenance issues found',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey,
                            ),
                          ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: issues.length,
                          itemBuilder: (context, index) {
                            final issue = issues[index];
                            return Card(
                              margin: const EdgeInsets.only(bottom: 12),
                              elevation: 2,
                              child: InkWell(
                                onTap: () {
                                  _showIssueDetails(context, issue);
                                },
                                child: Padding(
                                  padding: const EdgeInsets.all(16),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Expanded(
                                            child: Text(
                                              issue.title,
                                              style: const TextStyle(
                                                fontWeight: FontWeight.w600,
                                                fontSize: 16,
                                              ),
                                            ),
                                          ),
                                          Container(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 8,
                                              vertical: 4,
                                            ),
                                            decoration: BoxDecoration(
                                              color: _getStatusColor(issue.status).withOpacity(0.1),
                                              borderRadius: BorderRadius.circular(12),
                                            ),
                                            child: Text(
                                              issue.status,
                                              style: TextStyle(
                                                color: _getStatusColor(issue.status),
                                                fontSize: 12,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 8),
                                      Row(
                                        children: [
                                          Icon(
                                            _getDepartmentIcon(issue.department?.name ?? 'general'),
                                            size: 16,
                                            color: Colors.grey[600],
                                          ),
                                          const SizedBox(width: 4),
                                          Text(
                                            issue.department?.name ?? 'General',
                                            style: TextStyle(
                                              color: Colors.grey[600],
                                              fontSize: 14,
                                            ),
                                          ),
                                          const SizedBox(width: 16),
                                          Icon(
                                            Icons.calendar_today,
                                            size: 16,
                                            color: Colors.grey[600],
                                          ),
                                          const SizedBox(width: 4),
                                          Text(
                                            _formatDate(issue.createdAt),
                                            style: TextStyle(
                                              color: Colors.grey[600],
                                              fontSize: 14,
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 8),
                                      Row(
                                        children: [
                                          Container(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 6,
                                              vertical: 2,
                                            ),
                                            decoration: BoxDecoration(
                                              color: _getPriorityColor(issue.priority).withOpacity(0.1),
                                              borderRadius: BorderRadius.circular(8),
                                            ),
                                            child: Text(
                                              issue.priority,
                                              style: TextStyle(
                                                color: _getPriorityColor(issue.priority),
                                                fontSize: 12,
                                              ),
                                            ),
                                          ),
                                          if (issue.description != null) ...[
                                            const SizedBox(width: 8),
                                            Container(
                                              padding: const EdgeInsets.symmetric(
                                                horizontal: 6,
                                                vertical: 2,
                                              ),
                                              decoration: BoxDecoration(
                                                color: Colors.blue.withOpacity(0.1),
                                                borderRadius: BorderRadius.circular(8),
                                              ),
                                              child: Text(
                                                'Has Description',
                                                style: TextStyle(
                                                  color: Colors.blue[700],
                                                  fontSize: 12,
                                                ),
                                              ),
                                            ),
                                          ],
                                          const Spacer(),
                                          Row(
                                            children: [
                                              IconButton(
                                                icon: const Icon(Icons.visibility, size: 20),
                                                onPressed: () => _showIssueDetails(context, issue),
                                                tooltip: 'View',
                                              ),
                                              IconButton(
                                                icon: const Icon(Icons.edit, size: 20),
                                                onPressed: () => _editIssue(context, issue),
                                                tooltip: 'Edit',
                                              ),
                                              IconButton(
                                                icon: const Icon(Icons.delete, size: 20, color: Colors.red),
                                                onPressed: () => _deleteIssue(context, issue),
                                                tooltip: 'Delete',
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                ),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, stack) => Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error, size: 64, color: Colors.red),
                      const SizedBox(height: 16),
                      Text(
                        'Error loading maintenance issues',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        error.toString(),
                        style: Theme.of(context).textTheme.bodySmall,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () => ref.invalidate(maintenanceIssuesProvider(params)),
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'open':
        return Colors.orange;
      case 'in_progress':
        return Colors.blue;
      case 'resolved':
        return Colors.green;
      case 'closed':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  IconData _getDepartmentIcon(String department) {
    switch (department.toLowerCase()) {
      case 'electricity':
        return Icons.electrical_services;
      case 'water':
        return Icons.water_drop;
      case 'security':
        return Icons.security;
      case 'internet':
        return Icons.wifi;
      case 'generator':
        return Icons.power;
      default:
        return Icons.build;
    }
  }

  Color _getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'high':
        return Colors.red;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  String _getMonthName(int month) {
    const months = [
      '', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return months[month];
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return '${date.day} ${_getMonthName(date.month)}, ${date.year}';
    } catch (e) {
      return dateString; // Return original string if parsing fails
    }
  }

  void _showIssueDetails(BuildContext context, MaintenanceIssue issue) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(issue.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Department: ${issue.department?.name ?? 'General'}'),
            const SizedBox(height: 8),
            Text('Priority: ${issue.priority}'),
            const SizedBox(height: 8),
            Text('Status: ${issue.status}'),
            const SizedBox(height: 8),
            if (issue.description != null) ...[
              Text('Description: ${issue.description}'),
              const SizedBox(height: 8),
            ],
            Text('Created: ${_formatDate(issue.createdAt)}'),
            if (issue.updatedAt != issue.createdAt) ...[
              const SizedBox(height: 8),
              Text('Updated: ${_formatDate(issue.updatedAt)}'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _editIssue(BuildContext context, MaintenanceIssue issue) {
    showDialog(
      context: context,
      builder: (context) => EditMaintenanceIssueDialog(
        issue: issue,
        propertyId: widget.propertyId,
      ),
    );
  }

  void _deleteIssue(BuildContext context, MaintenanceIssue issue) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Issue'),
        content: Text('Are you sure you want to delete "${issue.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                final params = DeleteMaintenanceParams(
                  id: issue.id,
                  propertyId: issue.propertyId,
                );

                await ref.read(deleteMaintenanceIssueProvider(params).future);

                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Issue deleted successfully'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Failed to delete issue: ${e.toString()}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}

// Functions List Tab
class FunctionsListTab extends StatelessWidget {
  const FunctionsListTab({super.key});

  final List<MaintenanceFunction> _functions = const [
    MaintenanceFunction(
      name: 'Attendance - Office',
      subFunction: 'Staff Presence',
      department: 'Maintenance',
      input: 'Check-in/check-out logs',
      process: 'Compare vs shift schedule',
      output: 'Present/Absent',
      thresholdLimits: 'Max 1 absence/week',
      responsibleAgent: 'Office Incharge',
    ),
    MaintenanceFunction(
      name: 'CCTV',
      subFunction: 'Feed Uptime',
      department: 'CCTV',
      input: 'IP stream status',
      process: 'Ping every hour',
      output: 'Online/Offline',
      thresholdLimits: 'Max downtime: 2 hrs',
      responsibleAgent: 'CCTV Vendor',
    ),
    MaintenanceFunction(
      name: 'Electricity',
      subFunction: 'Consumption Tracker',
      department: 'Electricity',
      input: 'Energy meter data (kWh)',
      process: 'Calculate daily/monthly usage',
      output: 'kWh per day/month',
      thresholdLimits: 'Max daily: 10 kWh',
      responsibleAgent: 'Electrician',
    ),
    MaintenanceFunction(
      name: 'Generator',
      subFunction: 'Fuel Level Monitoring',
      department: 'Generator',
      input: 'Manual entry or sensor',
      process: 'Log/check daily',
      output: 'Fuel % or litres',
      thresholdLimits: 'Min: 20%',
      responsibleAgent: 'Site Security Incharge',
    ),
    MaintenanceFunction(
      name: 'Water',
      subFunction: 'Tank Level Monitoring',
      department: 'Water',
      input: 'Sensor reading (%, time)',
      process: 'Fetch from sensor/API',
      output: 'Current water level (%)',
      thresholdLimits: 'Min: 20%, Max: 80%',
      responsibleAgent: 'Facility Supervisor',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Header
        Container(
          padding: const EdgeInsets.all(16),
          color: Colors.white,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.functions, size: 24),
                  const SizedBox(width: 8),
                  const Text(
                    'Maintenance Functions',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              const Text(
                'Operational functions and processes',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),

        // Functions List
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _functions.length,
            itemBuilder: (context, index) {
              final function = _functions[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 12),
                elevation: 2,
                child: ExpansionTile(
                  title: Text(
                    function.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 4),
                      Text(
                        function.subFunction,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.blue,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            _getDepartmentIcon(function.department),
                            size: 16,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            function.department,
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          _buildDetailRow('Input', function.input),
                          _buildDetailRow('Process', function.process),
                          _buildDetailRow('Output', function.output),
                          _buildDetailRow('Threshold Limits', function.thresholdLimits),
                          _buildDetailRow('Responsible Agent', function.responsibleAgent),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getDepartmentIcon(String department) {
    switch (department.toLowerCase()) {
      case 'electricity':
        return Icons.electrical_services;
      case 'water':
        return Icons.water_drop;
      case 'security':
        return Icons.security;
      case 'internet':
        return Icons.wifi;
      case 'generator':
        return Icons.power;
      case 'cctv':
        return Icons.videocam;
      case 'maintenance':
        return Icons.build;
      default:
        return Icons.settings;
    }
  }
}

class MaintenanceFunction {
  final String name;
  final String subFunction;
  final String department;
  final String input;
  final String process;
  final String output;
  final String thresholdLimits;
  final String responsibleAgent;

  const MaintenanceFunction({
    required this.name,
    required this.subFunction,
    required this.department,
    required this.input,
    required this.process,
    required this.output,
    required this.thresholdLimits,
    required this.responsibleAgent,
  });
}

class IssueDetailsDialog extends StatelessWidget {
  final MaintenanceIssue issue;

  const IssueDetailsDialog({super.key, required this.issue});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Issue Details',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Text(
              issue.title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),

            _buildDetailRow('Department', issue.departmentName),
            _buildDetailRow('Priority', issue.priority),
            _buildDetailRow('Status', issue.status),
            _buildDetailRow('Recurrence', issue.frequency ?? 'One-time'),
            _buildDetailRow(
              'Reported Date',
              _formatDate(issue.reportedDate ?? issue.createdAt),
            ),

            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getMonthName(int month) {
    const months = [
      '', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return months[month];
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return '${date.day} ${_getMonthName(date.month)}, ${date.year}';
    } catch (e) {
      return dateString; // Return original string if parsing fails
    }
  }
}

// Statistics Tab
class StatisticsTab extends ConsumerWidget {
  final String? propertyId;

  const StatisticsTab({super.key, this.propertyId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Statistics Header
          Row(
            children: [
              const Icon(Icons.bar_chart, size: 24),
              const SizedBox(width: 8),
              const Text(
                'Maintenance Statistics',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Quick Stats Cards
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Total Issues',
                  '24',
                  Icons.assignment,
                  Colors.blue,
                  'This month',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Resolved',
                  '18',
                  Icons.check_circle,
                  Colors.green,
                  '75% completion',
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'In Progress',
                  '4',
                  Icons.pending,
                  Colors.orange,
                  'Active work',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Open',
                  '2',
                  Icons.error_outline,
                  Colors.red,
                  'Needs attention',
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Department Breakdown
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Issues by Department',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 16),

                _buildDepartmentItem('Electricity', 8, Colors.amber),
                _buildDepartmentItem('Water', 6, Colors.blue),
                _buildDepartmentItem('Security', 4, Colors.red),
                _buildDepartmentItem('HVAC', 3, Colors.green),
                _buildDepartmentItem('General', 3, Colors.grey),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Priority Distribution
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Priority Distribution',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 16),

                _buildPriorityItem('High Priority', 3, Colors.red),
                _buildPriorityItem('Medium Priority', 12, Colors.orange),
                _buildPriorityItem('Low Priority', 9, Colors.green),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Recent Trends
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Text(
                      'Monthly Trends',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Spacer(),
                    TextButton.icon(
                      onPressed: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Detailed analytics will be implemented in next phase'),
                          ),
                        );
                      },
                      icon: const Icon(Icons.open_in_new, size: 16),
                      label: const Text('View Details'),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                _buildTrendItem('January', 18, 15),
                _buildTrendItem('February', 22, 19),
                _buildTrendItem('March', 24, 18),
                _buildTrendItem('April (Current)', 24, 18),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Action Buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    _exportMaintenanceReport(context, ref);
                  },
                  icon: const Icon(Icons.download),
                  label: const Text('Export Report'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    Navigator.pushNamed(context, '/analytics/maintenance');
                  },
                  icon: const Icon(Icons.analytics),
                  label: const Text('View Analytics'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color, String subtitle) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: color,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 10,
              color: color.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDepartmentItem(String department, int count, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              department,
              style: const TextStyle(fontSize: 14),
            ),
          ),
          Text(
            count.toString(),
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriorityItem(String priority, int count, Color color) {
    final total = 24; // Total issues
    final percentage = (count / total * 100).round();

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  priority,
                  style: const TextStyle(fontSize: 14),
                ),
              ),
              Text(
                '$count ($percentage%)',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          LinearProgressIndicator(
            value: count / total,
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ],
      ),
    );
  }

  Widget _buildTrendItem(String month, int total, int resolved) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              month,
              style: const TextStyle(fontSize: 14),
            ),
          ),
          Expanded(
            child: Text(
              'Total: $total',
              style: const TextStyle(fontSize: 14),
            ),
          ),
          Expanded(
            child: Text(
              'Resolved: $resolved',
              style: TextStyle(
                fontSize: 14,
                color: Colors.green[700],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _exportMaintenanceReport(BuildContext context, WidgetRef ref) {
    final userPermissions = ref.read(userPermissionsProvider);

    if (!userPermissions.hasPermission('maintenance.export')) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Insufficient permissions to export maintenance reports'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Maintenance Report'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Choose export format:'),
            SizedBox(height: 16),
            // Export options would go here
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Maintenance report export initiated'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('Export'),
          ),
        ],
      ),
    );
  }
}

// Filter Dialog
class FilterDialog extends ConsumerWidget {
  const FilterDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedStatus = ref.watch(maintenanceStatusFilterProvider);
    final selectedPriority = ref.watch(maintenancePriorityFilterProvider);
    final selectedDepartment = ref.watch(maintenanceDepartmentFilterProvider);

    return AlertDialog(
      title: const Text('Filter Issues'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Status:', style: TextStyle(fontWeight: FontWeight.w500)),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: ['all', 'OPEN', 'IN_PROGRESS', 'RESOLVED', 'CLOSED'].map((status) {
              return FilterChip(
                label: Text(status == 'all' ? 'All' : status.replaceAll('_', ' ')),
                selected: selectedStatus == status,
                onSelected: (selected) {
                  ref.read(maintenanceStatusFilterProvider.notifier).state = status;
                },
              );
            }).toList(),
          ),

          const SizedBox(height: 16),
          const Text('Priority:', style: TextStyle(fontWeight: FontWeight.w500)),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: ['all', 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL'].map((priority) {
              return FilterChip(
                label: Text(priority == 'all' ? 'All' : priority),
                selected: selectedPriority == priority,
                onSelected: (selected) {
                  ref.read(maintenancePriorityFilterProvider.notifier).state = priority;
                },
              );
            }).toList(),
          ),

          const SizedBox(height: 16),
          const Text('Department:', style: TextStyle(fontWeight: FontWeight.w500)),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: ['all', 'ELECTRICITY', 'WATER', 'SECURITY', 'INTERNET', 'GENERAL'].map((dept) {
              return FilterChip(
                label: Text(dept == 'all' ? 'All' : dept),
                selected: selectedDepartment == dept,
                onSelected: (selected) {
                  ref.read(maintenanceDepartmentFilterProvider.notifier).state = dept;
                },
              );
            }).toList(),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () {
            ref.read(maintenanceStatusFilterProvider.notifier).state = 'all';
            ref.read(maintenancePriorityFilterProvider.notifier).state = 'all';
            ref.read(maintenanceDepartmentFilterProvider.notifier).state = 'all';
          },
          child: const Text('Clear All'),
        ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }
}

// Create Issue Dialog
class CreateIssueDialog extends ConsumerStatefulWidget {
  final String? propertyId;

  const CreateIssueDialog({super.key, this.propertyId});

  @override
  ConsumerState<CreateIssueDialog> createState() => _CreateIssueDialogState();
}

class _CreateIssueDialogState extends ConsumerState<CreateIssueDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();

  String? _selectedDepartment;
  String? _selectedPriority;
  bool _isSubmitting = false;

  final List<String> _departments = [
    'ELECTRICITY',
    'WATER',
    'SECURITY',
    'INTERNET',
    'GENERAL',
  ];

  final List<String> _priorities = [
    'LOW',
    'MEDIUM',
    'HIGH',
    'CRITICAL',
  ];

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Create Maintenance Issue'),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'Title *',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a title';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              DropdownButtonFormField<String>(
                value: _selectedDepartment,
                decoration: const InputDecoration(
                  labelText: 'Department *',
                  border: OutlineInputBorder(),
                ),
                items: _departments.map((dept) {
                  return DropdownMenuItem(
                    value: dept,
                    child: Text(dept),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedDepartment = value;
                  });
                },
                validator: (value) {
                  if (value == null) {
                    return 'Please select a department';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              DropdownButtonFormField<String>(
                value: _selectedPriority,
                decoration: const InputDecoration(
                  labelText: 'Priority *',
                  border: OutlineInputBorder(),
                ),
                items: _priorities.map((priority) {
                  return DropdownMenuItem(
                    value: priority,
                    child: Text(priority),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedPriority = value;
                  });
                },
                validator: (value) {
                  if (value == null) {
                    return 'Please select a priority';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              TextFormField(
                controller: _descriptionController,
                maxLines: 3,
                decoration: const InputDecoration(
                  labelText: 'Description *',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please provide a description';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isSubmitting ? null : _submitIssue,
          child: _isSubmitting
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Submit'),
        ),
      ],
    );
  }

  Future<void> _submitIssue() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      final params = CreateMaintenanceParams(
        propertyId: widget.propertyId ?? '',
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        priority: _selectedPriority!,
        department: _selectedDepartment!,
      );

      final success = await ref.read(createMaintenanceIssueProvider(params).future);

      if (success) {
        if (mounted) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Issue submitted successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        throw Exception('Failed to submit issue');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to submit issue: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }
}

// Edit Maintenance Issue Dialog
class EditMaintenanceIssueDialog extends ConsumerStatefulWidget {
  final MaintenanceIssue issue;
  final String? propertyId;

  const EditMaintenanceIssueDialog({
    super.key,
    required this.issue,
    this.propertyId,
  });

  @override
  ConsumerState<EditMaintenanceIssueDialog> createState() => _EditMaintenanceIssueDialogState();
}

class _EditMaintenanceIssueDialogState extends ConsumerState<EditMaintenanceIssueDialog> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _titleController;
  late final TextEditingController _descriptionController;
  late final TextEditingController _remarksController;

  late String _selectedPriority;
  late String _selectedStatus;
  late String? _selectedDepartment;
  bool _isLoading = false;

  final List<String> _priorities = [
    'LOW',
    'MEDIUM',
    'HIGH',
    'CRITICAL',
  ];

  final List<String> _statuses = [
    'OPEN',
    'IN_PROGRESS',
    'RESOLVED',
    'CLOSED',
  ];

  final List<String> _departments = [
    'Electricity',
    'Water',
    'Security',
    'Internet',
    'Generator',
    'CCTV',
    'Maintenance',
    'OTTs',
  ];

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.issue.title);
    _descriptionController = TextEditingController(text: widget.issue.description ?? '');
    _remarksController = TextEditingController();
    _selectedPriority = widget.issue.priority;
    _selectedStatus = widget.issue.status;
    _selectedDepartment = widget.issue.department?.name;
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _remarksController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final userPermissions = ref.watch(userPermissionsProvider);

    return AlertDialog(
      title: const Text('Edit Maintenance Issue'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8,
        child: SingleChildScrollView(
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title
                TextFormField(
                  controller: _titleController,
                  decoration: const InputDecoration(
                    labelText: 'Issue Title',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter issue title';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Description
                TextFormField(
                  controller: _descriptionController,
                  maxLines: 3,
                  decoration: const InputDecoration(
                    labelText: 'Description',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),

                // Priority and Status Row
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedPriority,
                        decoration: const InputDecoration(
                          labelText: 'Priority',
                          border: OutlineInputBorder(),
                        ),
                        items: _priorities.map((priority) {
                          return DropdownMenuItem(
                            value: priority,
                            child: Text(priority),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedPriority = value!;
                          });
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedStatus,
                        decoration: const InputDecoration(
                          labelText: 'Status',
                          border: OutlineInputBorder(),
                        ),
                        items: _statuses.map((status) {
                          return DropdownMenuItem(
                            value: status,
                            child: Text(status.replaceAll('_', ' ')),
                          );
                        }).toList(),
                        onChanged: userPermissions.canUpdateMaintenance ? (value) {
                          setState(() {
                            _selectedStatus = value!;
                          });
                        } : null,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Department
                DropdownButtonFormField<String>(
                  value: _selectedDepartment,
                  decoration: const InputDecoration(
                    labelText: 'Department',
                    border: OutlineInputBorder(),
                  ),
                  items: _departments.map((dept) {
                    return DropdownMenuItem(
                      value: dept,
                      child: Text(dept),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedDepartment = value;
                    });
                  },
                ),
                const SizedBox(height: 16),

                // Remarks
                TextFormField(
                  controller: _remarksController,
                  maxLines: 2,
                  decoration: const InputDecoration(
                    labelText: 'Update Remarks (Optional)',
                    hintText: 'Add any notes about this update',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _updateIssue,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Update'),
        ),
      ],
    );
  }

  Future<void> _updateIssue() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final params = UpdateMaintenanceParams(
        id: widget.issue.id,
        propertyId: widget.issue.propertyId,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        priority: _selectedPriority,
        status: _selectedStatus,
        department: _selectedDepartment,
        remarks: _remarksController.text.trim().isEmpty
            ? null
            : _remarksController.text.trim(),
      );

      await ref.read(updateMaintenanceIssueProvider(params).future);

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Issue updated successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update issue: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
