import '../models/api_response.dart';
import '../models/water_system.dart';
import '../../core/services/api_client.dart';
import '../../core/services/service_locator.dart';
import '../../core/constants/api_constants.dart';

class WaterRepository {
  final ApiClient _apiClient = serviceLocator.apiClient;

  Future<ApiResponse<List<WaterSystem>>> getWaterSystems({required String propertyId}) async {
    try {
      final endpoint = ApiConstants.waterSystems.replaceAll('{propertyId}', propertyId);
      final response = await _apiClient.get(endpoint);

      if (response.isSuccess) {
        final responseData = response.data['data'];
        final List<dynamic> systems = responseData['systems'] ?? [];
        final waterSystems = systems.map((json) => WaterSystem.fromBackendJson(json)).toList();
        return ApiResponse.success(data: waterSystems);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch water systems: $e');
    }
  }


}
