import '../models/api_response.dart';
import '../models/user.dart';
import '../../core/services/api_client.dart';
import '../../core/services/service_locator.dart';
import '../../core/constants/api_constants.dart';

class AuthRepository {
  final ApiClient _apiClient = serviceLocator.apiClient;

  Future<ApiResponse<Map<String, dynamic>>> login({
    required String email,
    required String password,
    String? deviceId,
    String? deviceName,
  }) async {
    try {
      final response = await _apiClient.post(
        ApiConstants.login,
        data: {
          'email': email,
          'password': password,
          if (deviceId != null) 'deviceId': deviceId,
          if (deviceName != null) 'deviceName': deviceName,
        },
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data['data']);
      } else {
        return ApiResponse.error(error: response.message ?? 'Login failed');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Login failed: $e');
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> register({
    required String name,
    required String email,
    required String password,
    required String role,
    String? phone,
    List<String>? assignedPropertyIds,
    List<String>? assignedOfficeIds,
  }) async {
    try {
      final response = await _apiClient.post(
        ApiConstants.register,
        data: {
          'name': name,
          'email': email,
          'password': password,
          'role': role,
          if (phone != null) 'phone': phone,
          if (assignedPropertyIds != null) 'assignedPropertyIds': assignedPropertyIds,
          if (assignedOfficeIds != null) 'assignedOfficeIds': assignedOfficeIds,
        },
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data['data']);
      } else {
        return ApiResponse.error(error: response.message ?? 'Registration failed');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Registration failed: $e');
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> refreshToken(String refreshToken) async {
    try {
      final response = await _apiClient.post(
        ApiConstants.refreshToken,
        data: {'refreshToken': refreshToken},
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data['data']);
      } else {
        return ApiResponse.error(error: response.message ?? 'Token refresh failed');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Token refresh failed: $e');
    }
  }

  Future<ApiResponse<void>> logout() async {
    try {
      final response = await _apiClient.post(ApiConstants.logout);

      if (response.isSuccess) {
        return ApiResponse.success(data: null);
      } else {
        return ApiResponse.error(error: response.message ?? 'Logout failed');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Logout failed: $e');
    }
  }

  Future<ApiResponse<User>> getCurrentUser() async {
    try {
      final response = await _apiClient.get(ApiConstants.userProfile);

      if (response.isSuccess) {
        final user = User.fromJson(response.data['data']);
        return ApiResponse.success(data: user);
      } else {
        return ApiResponse.error(error: response.message ?? 'Failed to get user profile');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to get user profile: $e');
    }
  }

  Future<ApiResponse<User>> updateProfile({
    String? name,
    String? email,
    String? phone,
    String? avatar,
  }) async {
    try {
      final data = <String, dynamic>{};
      if (name != null) data['name'] = name;
      if (email != null) data['email'] = email;
      if (phone != null) data['phone'] = phone;
      if (avatar != null) data['avatar'] = avatar;

      final response = await _apiClient.put(
        ApiConstants.userProfile,
        data: data,
      );

      if (response.isSuccess) {
        final user = User.fromJson(response.data['data']);
        return ApiResponse.success(data: user);
      } else {
        return ApiResponse.error(error: response.message ?? 'Failed to update profile');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to update profile: $e');
    }
  }

  Future<ApiResponse<void>> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      final response = await _apiClient.post(
        '${ApiConstants.userProfile}/change-password',
        data: {
          'currentPassword': currentPassword,
          'newPassword': newPassword,
        },
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: null);
      } else {
        return ApiResponse.error(error: response.message ?? 'Failed to change password');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to change password: $e');
    }
  }

  Future<ApiResponse<void>> forgotPassword(String email) async {
    try {
      final response = await _apiClient.post(
        ApiConstants.forgotPassword,
        data: {'email': email},
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: null);
      } else {
        return ApiResponse.error(error: response.message ?? 'Failed to send reset email');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to send reset email: $e');
    }
  }

  Future<ApiResponse<void>> resetPassword({
    required String token,
    required String newPassword,
  }) async {
    try {
      final response = await _apiClient.post(
        ApiConstants.resetPassword,
        data: {
          'token': token,
          'newPassword': newPassword,
        },
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: null);
      } else {
        return ApiResponse.error(error: response.message ?? 'Failed to reset password');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to reset password: $e');
    }
  }

  Future<ApiResponse<void>> verifyEmail(String token) async {
    try {
      final response = await _apiClient.post(
        ApiConstants.verifyEmail,
        data: {'token': token},
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: null);
      } else {
        return ApiResponse.error(error: response.message ?? 'Failed to verify email');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to verify email: $e');
    }
  }

  Future<ApiResponse<void>> resendVerificationEmail() async {
    try {
      final response = await _apiClient.post(ApiConstants.resendVerification);

      if (response.isSuccess) {
        return ApiResponse.success(data: null);
      } else {
        return ApiResponse.error(error: response.message ?? 'Failed to resend verification email');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to resend verification email: $e');
    }
  }
}
