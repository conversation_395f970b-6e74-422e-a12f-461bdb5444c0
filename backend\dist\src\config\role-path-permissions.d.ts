export interface PathPermissionRule {
    path: string;
    accessLevel: 'full' | 'read' | 'write' | 'restricted' | 'none';
    permissions: string[];
    restrictions?: {
        hideComponents?: string[];
        disableComponents?: string[];
        hideFields?: string[];
        customConditions?: Record<string, any>;
    };
    conditions?: Record<string, any>;
}
export interface RolePathPermissions {
    role: string;
    permissions: PathPermissionRule[];
    inheritance?: {
        inheritsFrom?: string;
        overrides?: string[];
    };
}
export declare const ROLE_PATH_PERMISSIONS: RolePathPermissions[];
export declare function resolvePathPermissions(role: string, requestedPath: string): PathPermissionRule | null;
