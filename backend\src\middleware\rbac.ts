import { Request, Response, NextFunction } from 'express';
import { UserRole } from '@prisma/client';

// Simple RBAC middleware for backward compatibility
export const rbac = (allowedRoles: string[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const user = req.user;
      
      if (!user) {
        res.status(401).json({
          success: false,
          error: 'UNAUTHORIZED',
          message: 'Authentication required',
          timestamp: new Date().toISOString(),
          path: req.path,
        });
        return;
      }

      // Convert string roles to UserRole enum values
      const allowedUserRoles = allowedRoles.map(role => {
        switch (role) {
          case 'SUPER_ADMIN':
            return UserRole.SUPER_ADMIN;
          case 'PROPERTY_MANAGER':
            return UserRole.PROPERTY_MANAGER;
          case 'OFFICE_MANAGER':
            return UserRole.OFFICE_MANAGER;
          case 'SECURITY_PERSONNEL':
            return UserRole.SECURITY_PERSONNEL;
          case 'MAINTENANCE_STAFF':
            return UserRole.MAINTENANCE_STAFF;
          case 'CONSTRUCTION_SUPERVISOR':
            return UserRole.CONSTRUCTION_SUPERVISOR;
          case 'EMPLOYEE':
            // Allow any employee role
            return user.role;
          default:
            return null;
        }
      }).filter(Boolean);

      // Check if user's role is in allowed roles
      const hasPermission = allowedUserRoles.includes(user.role) || 
                           (allowedRoles.includes('EMPLOYEE') && user.role);

      if (!hasPermission) {
        res.status(403).json({
          success: false,
          error: 'FORBIDDEN',
          message: 'Insufficient permissions',
          timestamp: new Date().toISOString(),
          path: req.path,
        });
        return;
      }

      next();
    } catch (error) {
      console.error('RBAC middleware error:', error);
      res.status(500).json({
        success: false,
        error: 'INTERNAL_SERVER_ERROR',
        message: 'Authorization service error',
        timestamp: new Date().toISOString(),
        path: req.path,
      });
    }
  };
};

// Enhanced permission check function
export const checkPermission = (permission: string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const user = req.user;
      
      if (!user) {
        res.status(401).json({
          success: false,
          error: 'UNAUTHORIZED',
          message: 'Authentication required',
          timestamp: new Date().toISOString(),
          path: req.path,
        });
        return;
      }

      // Simple permission mapping based on role
      const rolePermissions: Record<UserRole, string[]> = {
        [UserRole.SUPER_ADMIN]: ['*'], // All permissions
        [UserRole.PROPERTY_MANAGER]: [
          'systems.view',
          'systems.update',
          'employees.view',
          'employees.create',
          'employees.update',
          'employees.delete',
          'employees.attendance.view',
          'employees.attendance.create',
          'maintenance.view',
          'maintenance.create',
          'maintenance.update',
          'dashboard.view',
          'alerts.view',
          'alerts.update',
        ],
        [UserRole.OFFICE_MANAGER]: [
          'employees.view',
          'employees.create',
          'employees.update',
          'employees.attendance.view',
          'employees.attendance.create',
          'dashboard.view',
          'systems.view',
        ],
        [UserRole.SECURITY_PERSONNEL]: [
          'systems.view',
          'security.view',
          'security.update',
          'alerts.view',
          'alerts.update',
          'dashboard.view',
        ],
        [UserRole.MAINTENANCE_STAFF]: [
          'maintenance.view',
          'maintenance.create',
          'maintenance.update',
          'systems.view',
          'dashboard.view',
        ],
        [UserRole.CONSTRUCTION_SUPERVISOR]: [
          'employees.view',
          'employees.attendance.view',
          'systems.view',
          'dashboard.view',
        ],
      };

      const userPermissions = rolePermissions[user.role] || [];
      const hasPermission = userPermissions.includes('*') || userPermissions.includes(permission);

      if (!hasPermission) {
        res.status(403).json({
          success: false,
          error: 'FORBIDDEN',
          message: `Permission denied: ${permission}`,
          timestamp: new Date().toISOString(),
          path: req.path,
        });
        return;
      }

      next();
    } catch (error) {
      console.error('Permission check error:', error);
      res.status(500).json({
        success: false,
        error: 'INTERNAL_SERVER_ERROR',
        message: 'Permission service error',
        timestamp: new Date().toISOString(),
        path: req.path,
      });
    }
  };
};



// Extend Express Request interface
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        role: UserRole;
        assignedProperties: string[];
      };
    }
  }
}
