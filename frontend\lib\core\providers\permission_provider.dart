import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import '../services/auth_service.dart';
import '../services/api_client.dart';

// Permission models
class UIPermissions {
  final List<String> screens;
  final Map<String, TabPermission> tabs;
  final Map<String, WidgetPermission> widgets;
  final List<String> actions;
  final Map<String, dynamic> dataFilters;

  const UIPermissions({
    required this.screens,
    required this.tabs,
    required this.widgets,
    required this.actions,
    required this.dataFilters,
  });

  factory UIPermissions.fromJson(Map<String, dynamic> json) {
    return UIPermissions(
      screens: List<String>.from(json['screens'] ?? []),
      tabs: (json['tabs'] as Map<String, dynamic>? ?? {})
          .map((key, value) => MapEntry(key, TabPermission.fromJson(value))),
      widgets: (json['widgets'] as Map<String, dynamic>? ?? {})
          .map((key, value) => MapEntry(key, WidgetPermission.fromJson(value))),
      actions: List<String>.from(json['actions'] ?? []),
      dataFilters: json['dataFilters'] ?? {},
    );
  }

  factory UIPermissions.empty() {
    return const UIPermissions(
      screens: [],
      tabs: {},
      widgets: {},
      actions: ['read'],
      dataFilters: {},
    );
  }
}

class TabPermission {
  final bool visible;
  final bool enabled;
  final String accessLevel; // 'full', 'read_only', 'restricted', 'none'
  final Map<String, dynamic>? restrictions;

  const TabPermission({
    required this.visible,
    required this.enabled,
    required this.accessLevel,
    this.restrictions,
  });

  factory TabPermission.fromJson(Map<String, dynamic> json) {
    return TabPermission(
      visible: json['visible'] ?? true,
      enabled: json['enabled'] ?? true,
      accessLevel: json['accessLevel'] ?? 'full',
      restrictions: json['restrictions'],
    );
  }

  bool get isReadOnly => accessLevel == 'read_only';
  bool get isRestricted => accessLevel == 'restricted';
  bool get hasFullAccess => accessLevel == 'full';
  bool get hasNoAccess => accessLevel == 'none' || !visible;
}

class WidgetPermission {
  final bool visible;
  final bool enabled;
  final String accessLevel;
  final Map<String, dynamic>? conditions;
  final Map<String, dynamic>? restrictions;

  const WidgetPermission({
    required this.visible,
    required this.enabled,
    required this.accessLevel,
    this.conditions,
    this.restrictions,
  });

  factory WidgetPermission.fromJson(Map<String, dynamic> json) {
    return WidgetPermission(
      visible: json['visible'] ?? true,
      enabled: json['enabled'] ?? true,
      accessLevel: json['accessLevel'] ?? 'full',
      conditions: json['conditions'],
      restrictions: json['restrictions'],
    );
  }

  bool get isReadOnly => accessLevel == 'read_only';
  bool get isRestricted => accessLevel == 'restricted';
  bool get hasFullAccess => accessLevel == 'full';

  bool isFieldHidden(String fieldName) {
    final hideFields = restrictions?['hideFields'] as List<dynamic>?;
    return hideFields?.contains(fieldName) ?? false;
  }

  bool isActionDisabled(String actionName) {
    final disableActions = restrictions?['disableActions'] as List<dynamic>?;
    return disableActions?.contains(actionName) ?? false;
  }
}

class PermissionContext {
  final String? propertyId;
  final String? officeId;
  final String? resourceId;
  final Map<String, dynamic>? metadata;

  const PermissionContext({
    this.propertyId,
    this.officeId,
    this.resourceId,
    this.metadata,
  });
}

// Permission service
class PermissionService {
  final ApiClient _apiClient = ApiClient();
  UIPermissions? _cachedPermissions;
  DateTime? _lastFetch;
  static const Duration _cacheExpiry = Duration(minutes: 5);

  Future<UIPermissions> getUserPermissions({bool forceRefresh = false}) async {
    // Return cached permissions if still valid
    if (!forceRefresh && 
        _cachedPermissions != null && 
        _lastFetch != null &&
        DateTime.now().difference(_lastFetch!) < _cacheExpiry) {
      return _cachedPermissions!;
    }

    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        '/auth/permissions',
        fromJson: (json) => json as Map<String, dynamic>,
      );

      if (response.success && response.data != null) {
        _cachedPermissions = UIPermissions.fromJson(response.data!);
        _lastFetch = DateTime.now();
        return _cachedPermissions!;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching permissions: $e');
      }
    }

    return UIPermissions.empty();
  }

  Future<bool> checkPermission(
    String resource,
    String action, {
    PermissionContext? context,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'resource': resource,
        'action': action,
      };

      if (context != null) {
        if (context.propertyId != null) queryParams['propertyId'] = context.propertyId;
        if (context.officeId != null) queryParams['officeId'] = context.officeId;
        if (context.resourceId != null) queryParams['resourceId'] = context.resourceId;
      }

      final response = await _apiClient.get<Map<String, dynamic>>(
        '/auth/check-permission',
        queryParameters: queryParams,
        fromJson: (json) => json as Map<String, dynamic>,
      );

      return response.success && (response.data?['granted'] == true);
    } catch (e) {
      if (kDebugMode) {
        print('Error checking permission: $e');
      }
      return false;
    }
  }

  void clearCache() {
    _cachedPermissions = null;
    _lastFetch = null;
  }
}

// Providers
final permissionServiceProvider = Provider<PermissionService>((ref) {
  return PermissionService();
});

final uiPermissionsProvider = FutureProvider<UIPermissions>((ref) async {
  final permissionService = ref.watch(permissionServiceProvider);
  final authService = AuthService();
  
  if (!authService.isAuthenticated) {
    return UIPermissions.empty();
  }

  return await permissionService.getUserPermissions();
});

final permissionCheckerProvider = Provider.family<Future<bool>, PermissionRequest>((ref, request) async {
  final permissionService = ref.watch(permissionServiceProvider);
  return await permissionService.checkPermission(
    request.resource,
    request.action,
    context: request.context,
  );
});

class PermissionRequest {
  final String resource;
  final String action;
  final PermissionContext? context;

  const PermissionRequest({
    required this.resource,
    required this.action,
    this.context,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PermissionRequest &&
          runtimeType == other.runtimeType &&
          resource == other.resource &&
          action == other.action &&
          context == other.context;

  @override
  int get hashCode => resource.hashCode ^ action.hashCode ^ context.hashCode;
}

// Permission-aware widget builder
class PermissionBuilder extends ConsumerWidget {
  final String? screen;
  final String? widget;
  final String? permission;
  final PermissionContext? context;
  final Widget Function(BuildContext context, bool hasPermission) builder;
  final Widget? fallback;

  const PermissionBuilder({
    super.key,
    this.screen,
    this.widget,
    this.permission,
    this.context,
    required this.builder,
    this.fallback,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final uiPermissionsAsync = ref.watch(uiPermissionsProvider);

    return uiPermissionsAsync.when(
      data: (permissions) {
        bool hasPermission = true;

        // Check screen permission
        if (screen != null) {
          hasPermission = permissions.screens.contains(screen);
        }

        // Check widget permission
        if (hasPermission && widget != null) {
          final widgetPerm = permissions.widgets[widget!];
          hasPermission = widgetPerm?.visible ?? false;
        }

        // Check specific permission
        if (hasPermission && permission != null) {
          hasPermission = permissions.actions.contains(permission);
        }

        if (hasPermission) {
          return builder(context, true);
        } else {
          return fallback ?? const SizedBox.shrink();
        }
      },
      loading: () => const SizedBox.shrink(),
      error: (error, stack) {
        if (kDebugMode) {
          print('Permission check error: $error');
        }
        return fallback ?? const SizedBox.shrink();
      },
    );
  }
}
