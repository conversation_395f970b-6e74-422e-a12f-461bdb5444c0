import 'package:flutter/material.dart';
import 'dart:async';

class RetryWidget extends StatefulWidget {
  final Future<void> Function() onRetry;
  final Widget child;
  final int maxRetries;
  final Duration retryDelay;
  final Widget Function(Object error, int retryCount, VoidCallback retry)? errorBuilder;
  final bool autoRetry;

  const RetryWidget({
    super.key,
    required this.onRetry,
    required this.child,
    this.maxRetries = 3,
    this.retryDelay = const Duration(seconds: 2),
    this.errorBuilder,
    this.autoRetry = false,
  });

  @override
  State<RetryWidget> createState() => _RetryWidgetState();
}

class _RetryWidgetState extends State<RetryWidget> {
  bool _isLoading = false;
  Object? _error;
  int _retryCount = 0;
  Timer? _retryTimer;

  @override
  void dispose() {
    _retryTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      if (widget.errorBuilder != null) {
        return widget.errorBuilder!(_error!, _retryCount, _retry);
      }
      
      return _buildDefaultErrorWidget();
    }

    if (_isLoading) {
      return _buildLoadingWidget();
    }

    return widget.child;
  }

  Widget _buildDefaultErrorWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.withValues(alpha: 0.7),
            ),
            
            const SizedBox(height: 16),
            
            Text(
              'Something went wrong',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 8),
            
            Text(
              _error.toString(),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
            
            if (_retryCount > 0) ...[
              const SizedBox(height: 8),
              Text(
                'Retry attempt: $_retryCount/${widget.maxRetries}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[500],
                ),
              ),
            ],
            
            const SizedBox(height: 24),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton.icon(
                  onPressed: _canRetry ? _retry : null,
                  icon: const Icon(Icons.refresh),
                  label: Text(_canRetry ? 'Retry' : 'Max retries reached'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _canRetry ? Colors.blue : Colors.grey,
                    foregroundColor: Colors.white,
                  ),
                ),
                
                const SizedBox(width: 12),
                
                OutlinedButton.icon(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.arrow_back),
                  label: const Text('Go Back'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(
            _retryCount > 0 ? 'Retrying... ($_retryCount/${widget.maxRetries})' : 'Loading...',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  bool get _canRetry => _retryCount < widget.maxRetries;

  void _retry() {
    if (!_canRetry) return;

    setState(() {
      _isLoading = true;
      _error = null;
      _retryCount++;
    });

    widget.onRetry().then((_) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _error = null;
        });
      }
    }).catchError((error) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _error = error;
        });

        // Auto retry if enabled and we haven't reached max retries
        if (widget.autoRetry && _canRetry) {
          _scheduleAutoRetry();
        }
      }
    });
  }

  void _scheduleAutoRetry() {
    _retryTimer?.cancel();
    _retryTimer = Timer(widget.retryDelay, () {
      if (mounted && _canRetry) {
        _retry();
      }
    });
  }
}

/// Exponential backoff retry widget
class ExponentialBackoffRetryWidget extends StatefulWidget {
  final Future<void> Function() onRetry;
  final Widget child;
  final int maxRetries;
  final Duration initialDelay;
  final double backoffMultiplier;
  final Duration maxDelay;
  final Widget Function(Object error, int retryCount, VoidCallback retry)? errorBuilder;

  const ExponentialBackoffRetryWidget({
    super.key,
    required this.onRetry,
    required this.child,
    this.maxRetries = 5,
    this.initialDelay = const Duration(seconds: 1),
    this.backoffMultiplier = 2.0,
    this.maxDelay = const Duration(seconds: 30),
    this.errorBuilder,
  });

  @override
  State<ExponentialBackoffRetryWidget> createState() => _ExponentialBackoffRetryWidgetState();
}

class _ExponentialBackoffRetryWidgetState extends State<ExponentialBackoffRetryWidget> {
  bool _isLoading = false;
  Object? _error;
  int _retryCount = 0;
  Timer? _retryTimer;

  @override
  void dispose() {
    _retryTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      if (widget.errorBuilder != null) {
        return widget.errorBuilder!(_error!, _retryCount, _retry);
      }
      
      return _buildDefaultErrorWidget();
    }

    if (_isLoading) {
      return _buildLoadingWidget();
    }

    return widget.child;
  }

  Widget _buildDefaultErrorWidget() {
    final nextDelay = _calculateNextDelay();
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.withValues(alpha: 0.7),
            ),
            
            const SizedBox(height: 16),
            
            Text(
              'Connection failed',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 8),
            
            Text(
              'Retrying automatically with exponential backoff...',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 16),
            
            LinearProgressIndicator(
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
            ),
            
            const SizedBox(height: 8),
            
            Text(
              'Attempt $_retryCount/${widget.maxRetries} • Next retry in ${nextDelay.inSeconds}s',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[500],
              ),
            ),
            
            const SizedBox(height: 24),
            
            ElevatedButton.icon(
              onPressed: _canRetry ? _retry : null,
              icon: const Icon(Icons.refresh),
              label: Text(_canRetry ? 'Retry Now' : 'Max retries reached'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(
            'Attempting connection... ($_retryCount/${widget.maxRetries})',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  bool get _canRetry => _retryCount < widget.maxRetries;

  Duration _calculateNextDelay() {
    if (_retryCount == 0) return widget.initialDelay;
    
    final delay = widget.initialDelay * 
        (widget.backoffMultiplier * _retryCount);
    
    return delay > widget.maxDelay ? widget.maxDelay : delay;
  }

  void _retry() {
    if (!_canRetry) return;

    setState(() {
      _isLoading = true;
      _error = null;
      _retryCount++;
    });

    widget.onRetry().then((_) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _error = null;
          _retryCount = 0; // Reset on success
        });
      }
    }).catchError((error) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _error = error;
        });

        if (_canRetry) {
          _scheduleAutoRetry();
        }
      }
    });
  }

  void _scheduleAutoRetry() {
    _retryTimer?.cancel();
    final delay = _calculateNextDelay();
    
    _retryTimer = Timer(delay, () {
      if (mounted && _canRetry) {
        _retry();
      }
    });
  }
}
