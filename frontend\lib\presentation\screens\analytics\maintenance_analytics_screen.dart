import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../providers/auth_providers.dart';
import '../../providers/maintenance_providers.dart';
import '../../widgets/permission_wrapper.dart';
import '../../../core/theme/app_theme.dart';

class MaintenanceAnalyticsScreen extends ConsumerStatefulWidget {
  final String? propertyId;

  const MaintenanceAnalyticsScreen({
    super.key,
    this.propertyId,
  });

  @override
  ConsumerState<MaintenanceAnalyticsScreen> createState() => _MaintenanceAnalyticsScreenState();
}

class _MaintenanceAnalyticsScreenState extends ConsumerState<MaintenanceAnalyticsScreen> {
  String _selectedPeriod = '30_days';
  String _selectedView = 'overview';

  final List<String> _periods = [
    '7_days',
    '30_days',
    '90_days',
    '1_year',
  ];

  final List<String> _views = [
    'overview',
    'trends',
    'departments',
    'performance',
  ];

  @override
  Widget build(BuildContext context) {
    return PermissionWrapper(
      permission: 'maintenance.analytics.view',
      propertyId: widget.propertyId,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Maintenance Analytics'),
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
          actions: [
            PermissionWrapper(
              permission: 'maintenance.analytics.export',
              propertyId: widget.propertyId,
              child: IconButton(
                icon: const Icon(Icons.download),
                onPressed: _exportAnalytics,
                tooltip: 'Export Analytics',
              ),
            ),
            PermissionWrapper(
              permission: 'maintenance.analytics.settings',
              propertyId: widget.propertyId,
              child: IconButton(
                icon: const Icon(Icons.settings),
                onPressed: _showAnalyticsSettings,
                tooltip: 'Analytics Settings',
              ),
            ),
          ],
        ),
        body: Column(
          children: [
            // Controls
            Container(
              padding: const EdgeInsets.all(16),
              color: Colors.white,
              child: Row(
                children: [
                  // Period Selector
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _selectedPeriod,
                      decoration: const InputDecoration(
                        labelText: 'Time Period',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: _periods.map((period) {
                        return DropdownMenuItem(
                          value: period,
                          child: Text(_getPeriodLabel(period)),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedPeriod = value!;
                        });
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  
                  // View Selector
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _selectedView,
                      decoration: const InputDecoration(
                        labelText: 'View Type',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: _views.map((view) {
                        return DropdownMenuItem(
                          value: view,
                          child: Text(_getViewLabel(view)),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedView = value!;
                        });
                      },
                    ),
                  ),
                ],
              ),
            ),
            
            // Analytics Content
            Expanded(
              child: _buildAnalyticsContent(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalyticsContent() {
    switch (_selectedView) {
      case 'overview':
        return _buildOverviewAnalytics();
      case 'trends':
        return _buildTrendsAnalytics();
      case 'departments':
        return _buildDepartmentAnalytics();
      case 'performance':
        return _buildPerformanceAnalytics();
      default:
        return _buildOverviewAnalytics();
    }
  }

  Widget _buildOverviewAnalytics() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // KPI Cards
          Row(
            children: [
              Expanded(child: _buildKPICard('Total Issues', '156', Icons.assignment, Colors.blue, '+12%')),
              const SizedBox(width: 12),
              Expanded(child: _buildKPICard('Resolved', '142', Icons.check_circle, Colors.green, '+8%')),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(child: _buildKPICard('Avg Resolution', '2.3 days', Icons.timer, Colors.orange, '-15%')),
              const SizedBox(width: 12),
              Expanded(child: _buildKPICard('Open Issues', '14', Icons.error_outline, Colors.red, '+3')),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Charts Section
          _buildIssueStatusChart(),
          
          const SizedBox(height: 24),
          
          _buildPriorityDistributionChart(),
        ],
      ),
    );
  }

  Widget _buildTrendsAnalytics() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Issue Trends',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          
          _buildTrendChart(),
          
          const SizedBox(height: 24),
          
          _buildResolutionTimeChart(),
        ],
      ),
    );
  }

  Widget _buildDepartmentAnalytics() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Department Performance',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          
          _buildDepartmentPerformanceTable(),
          
          const SizedBox(height: 24),
          
          _buildDepartmentWorkloadChart(),
        ],
      ),
    );
  }

  Widget _buildPerformanceAnalytics() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Performance Metrics',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          
          _buildPerformanceMetrics(),
          
          const SizedBox(height: 24),
          
          _buildSLAComplianceChart(),
        ],
      ),
    );
  }

  Widget _buildKPICard(String title, String value, IconData icon, Color color, String change) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: color,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            change,
            style: TextStyle(
              fontSize: 12,
              color: change.startsWith('+') ? Colors.green : Colors.red,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrendChart() {
    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Issues Over Time',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: LineChart(
              LineChartData(
                lineBarsData: [
                  LineChartBarData(
                    spots: [
                      const FlSpot(0, 12),
                      const FlSpot(1, 18),
                      const FlSpot(2, 15),
                      const FlSpot(3, 22),
                      const FlSpot(4, 19),
                      const FlSpot(5, 25),
                      const FlSpot(6, 21),
                    ],
                    isCurved: true,
                    color: AppTheme.primaryColor,
                    barWidth: 3,
                    dotData: const FlDotData(show: true),
                  ),
                ],
                titlesData: FlTitlesData(
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (value, meta) {
                        final days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
                        if (value.toInt() < days.length) {
                          return Text(days[value.toInt()]);
                        }
                        return const Text('');
                      },
                    ),
                  ),
                  leftTitles: const AxisTitles(sideTitles: SideTitles(showTitles: true)),
                  topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                ),
                gridData: const FlGridData(show: true),
                borderData: FlBorderData(show: true),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResolutionTimeChart() {
    return Container(
      height: 250,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Average Resolution Time (Days)',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: BarChart(
              BarChartData(
                barGroups: [
                  BarChartGroupData(x: 0, barRods: [BarChartRodData(toY: 1.2, color: Colors.green)]),
                  BarChartGroupData(x: 1, barRods: [BarChartRodData(toY: 2.8, color: Colors.orange)]),
                  BarChartGroupData(x: 2, barRods: [BarChartRodData(toY: 4.5, color: Colors.red)]),
                  BarChartGroupData(x: 3, barRods: [BarChartRodData(toY: 7.2, color: Colors.purple)]),
                ],
                titlesData: FlTitlesData(
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (value, meta) {
                        switch (value.toInt()) {
                          case 0: return const Text('Low');
                          case 1: return const Text('Medium');
                          case 2: return const Text('High');
                          case 3: return const Text('Critical');
                          default: return const Text('');
                        }
                      },
                    ),
                  ),
                  leftTitles: const AxisTitles(sideTitles: SideTitles(showTitles: true)),
                  topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDepartmentPerformanceTable() {
    final departments = [
      {'name': 'Electricity', 'total': 45, 'resolved': 42, 'avgTime': '2.1 days', 'sla': '93%'},
      {'name': 'Water', 'total': 32, 'resolved': 30, 'avgTime': '1.8 days', 'sla': '94%'},
      {'name': 'Security', 'total': 28, 'resolved': 25, 'avgTime': '3.2 days', 'sla': '89%'},
      {'name': 'HVAC', 'total': 22, 'resolved': 20, 'avgTime': '2.5 days', 'sla': '91%'},
      {'name': 'General', 'total': 29, 'resolved': 25, 'avgTime': '2.8 days', 'sla': '86%'},
    ];

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: const Row(
              children: [
                Expanded(flex: 2, child: Text('Department', style: TextStyle(fontWeight: FontWeight.w600))),
                Expanded(child: Text('Total', style: TextStyle(fontWeight: FontWeight.w600))),
                Expanded(child: Text('Resolved', style: TextStyle(fontWeight: FontWeight.w600))),
                Expanded(child: Text('Avg Time', style: TextStyle(fontWeight: FontWeight.w600))),
                Expanded(child: Text('SLA', style: TextStyle(fontWeight: FontWeight.w600))),
              ],
            ),
          ),

          // Data rows
          ...departments.asMap().entries.map((entry) {
            final index = entry.key;
            final dept = entry.value;
            final isLast = index == departments.length - 1;

            return Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: isLast ? null : Border(
                  bottom: BorderSide(color: Colors.grey[200]!),
                ),
              ),
              child: Row(
                children: [
                  Expanded(flex: 2, child: Text(dept['name'] as String)),
                  Expanded(child: Text(dept['total'].toString())),
                  Expanded(child: Text(dept['resolved'].toString())),
                  Expanded(child: Text(dept['avgTime'] as String)),
                  Expanded(
                    child: Text(
                      dept['sla'] as String,
                      style: TextStyle(
                        color: _getSLAColor(dept['sla'] as String),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildDepartmentWorkloadChart() {
    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Department Workload',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: PieChart(
              PieChartData(
                sections: [
                  PieChartSectionData(value: 45, color: Colors.blue, title: 'Electricity\n45'),
                  PieChartSectionData(value: 32, color: Colors.cyan, title: 'Water\n32'),
                  PieChartSectionData(value: 28, color: Colors.red, title: 'Security\n28'),
                  PieChartSectionData(value: 22, color: Colors.green, title: 'HVAC\n22'),
                  PieChartSectionData(value: 29, color: Colors.orange, title: 'General\n29'),
                ],
                centerSpaceRadius: 40,
                sectionsSpace: 2,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceMetrics() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(child: _buildMetricCard('First Response Time', '2.3 hrs', Icons.timer, Colors.blue)),
            const SizedBox(width: 12),
            Expanded(child: _buildMetricCard('Resolution Rate', '91%', Icons.check_circle, Colors.green)),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(child: _buildMetricCard('Customer Satisfaction', '4.2/5', Icons.star, Colors.orange)),
            const SizedBox(width: 12),
            Expanded(child: _buildMetricCard('Escalation Rate', '8%', Icons.trending_up, Colors.red)),
          ],
        ),
      ],
    );
  }

  Widget _buildMetricCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSLAComplianceChart() {
    return Container(
      height: 250,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'SLA Compliance Trend',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: LineChart(
              LineChartData(
                lineBarsData: [
                  LineChartBarData(
                    spots: [
                      const FlSpot(0, 88),
                      const FlSpot(1, 91),
                      const FlSpot(2, 89),
                      const FlSpot(3, 93),
                      const FlSpot(4, 91),
                      const FlSpot(5, 94),
                      const FlSpot(6, 92),
                    ],
                    isCurved: true,
                    color: Colors.green,
                    barWidth: 3,
                    dotData: const FlDotData(show: true),
                  ),
                ],
                titlesData: FlTitlesData(
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (value, meta) {
                        final weeks = ['W1', 'W2', 'W3', 'W4', 'W5', 'W6', 'W7'];
                        if (value.toInt() < weeks.length) {
                          return Text(weeks[value.toInt()]);
                        }
                        return const Text('');
                      },
                    ),
                  ),
                  leftTitles: const AxisTitles(sideTitles: SideTitles(showTitles: true)),
                  topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                ),
                gridData: const FlGridData(show: true),
                borderData: FlBorderData(show: true),
                minY: 80,
                maxY: 100,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  String _getPeriodLabel(String period) {
    switch (period) {
      case '7_days': return 'Last 7 Days';
      case '30_days': return 'Last 30 Days';
      case '90_days': return 'Last 90 Days';
      case '1_year': return 'Last Year';
      default: return period;
    }
  }

  String _getViewLabel(String view) {
    switch (view) {
      case 'overview': return 'Overview';
      case 'trends': return 'Trends';
      case 'departments': return 'Departments';
      case 'performance': return 'Performance';
      default: return view;
    }
  }

  Color _getSLAColor(String sla) {
    final percentage = int.tryParse(sla.replaceAll('%', '')) ?? 0;
    if (percentage >= 95) return Colors.green;
    if (percentage >= 90) return Colors.orange;
    return Colors.red;
  }

  void _exportAnalytics() {
    final userPermissions = ref.read(userPermissionsProvider);

    if (!userPermissions.canExportAnalytics) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Insufficient permissions to export analytics'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Analytics'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Choose export format:'),
            SizedBox(height: 16),
            // Export options would go here
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Analytics export will be implemented in next phase'),
                ),
              );
            },
            child: const Text('Export'),
          ),
        ],
      ),
    );
  }

  void _showAnalyticsSettings() {
    final userPermissions = ref.read(userPermissionsProvider);

    if (!userPermissions.canConfigureAnalytics) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Insufficient permissions to configure analytics'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Analytics Settings'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Analytics configuration options will be available here.'),
            SizedBox(height: 16),
            // Settings options would go here
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

  Widget _buildIssueStatusChart() {
    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Issue Status Distribution',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: PieChart(
              PieChartData(
                sections: [
                  PieChartSectionData(value: 142, color: Colors.green, title: 'Resolved\n142'),
                  PieChartSectionData(value: 14, color: Colors.red, title: 'Open\n14'),
                  PieChartSectionData(value: 8, color: Colors.orange, title: 'In Progress\n8'),
                  PieChartSectionData(value: 2, color: Colors.grey, title: 'Closed\n2'),
                ],
                centerSpaceRadius: 40,
                sectionsSpace: 2,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriorityDistributionChart() {
    return Container(
      height: 250,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Priority Distribution',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: BarChart(
              BarChartData(
                barGroups: [
                  BarChartGroupData(x: 0, barRods: [BarChartRodData(toY: 45, color: Colors.green)]),
                  BarChartGroupData(x: 1, barRods: [BarChartRodData(toY: 78, color: Colors.orange)]),
                  BarChartGroupData(x: 2, barRods: [BarChartRodData(toY: 28, color: Colors.red)]),
                  BarChartGroupData(x: 3, barRods: [BarChartRodData(toY: 5, color: Colors.purple)]),
                ],
                titlesData: FlTitlesData(
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (value, meta) {
                        switch (value.toInt()) {
                          case 0: return const Text('Low');
                          case 1: return const Text('Medium');
                          case 2: return const Text('High');
                          case 3: return const Text('Critical');
                          default: return const Text('');
                        }
                      },
                    ),
                  ),
                  leftTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
