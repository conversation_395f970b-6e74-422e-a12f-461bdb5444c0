import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import '../services/api_client.dart';

// Breadcrumb models
class BreadcrumbItem {
  final String path;
  final String name;
  final int level;
  final bool accessible;
  final String accessLevel;

  const BreadcrumbItem({
    required this.path,
    required this.name,
    required this.level,
    required this.accessible,
    required this.accessLevel,
  });

  factory BreadcrumbItem.fromJson(Map<String, dynamic> json) {
    return BreadcrumbItem(
      path: json['path'] ?? '',
      name: json['name'] ?? '',
      level: json['level'] ?? 0,
      accessible: json['accessible'] ?? false,
      accessLevel: json['accessLevel'] ?? 'none',
    );
  }
}

class PathPermissionResult {
  final bool hasAccess;
  final String accessLevel;
  final List<String> permissions;
  final Map<String, dynamic>? restrictions;
  final Map<String, ComponentPermission> components;
  final String? reason;

  const PathPermissionResult({
    required this.hasAccess,
    required this.accessLevel,
    required this.permissions,
    this.restrictions,
    required this.components,
    this.reason,
  });

  factory PathPermissionResult.fromJson(Map<String, dynamic> json) {
    final componentsMap = <String, ComponentPermission>{};
    final components = json['components'] as Map<String, dynamic>? ?? {};
    
    components.forEach((key, value) {
      componentsMap[key] = ComponentPermission.fromJson(value);
    });

    return PathPermissionResult(
      hasAccess: json['hasAccess'] ?? false,
      accessLevel: json['accessLevel'] ?? 'none',
      permissions: List<String>.from(json['permissions'] ?? []),
      restrictions: json['restrictions'],
      components: componentsMap,
      reason: json['reason'],
    );
  }

  factory PathPermissionResult.denied(String reason) {
    return PathPermissionResult(
      hasAccess: false,
      accessLevel: 'none',
      permissions: [],
      components: {},
      reason: reason,
    );
  }
}

class ComponentPermission {
  final bool visible;
  final bool enabled;
  final List<String> permissions;
  final Map<String, dynamic>? restrictions;

  const ComponentPermission({
    required this.visible,
    required this.enabled,
    required this.permissions,
    this.restrictions,
  });

  factory ComponentPermission.fromJson(Map<String, dynamic> json) {
    return ComponentPermission(
      visible: json['visible'] ?? true,
      enabled: json['enabled'] ?? true,
      permissions: List<String>.from(json['permissions'] ?? []),
      restrictions: json['restrictions'],
    );
  }

  bool hasPermission(String permission) {
    return permissions.contains(permission) || permissions.contains('*');
  }

  bool isFieldHidden(String fieldName) {
    final hideFields = restrictions?['hideFields'] as List<dynamic>?;
    return hideFields?.contains(fieldName) ?? false;
  }

  bool isActionDisabled(String actionName) {
    final disableActions = restrictions?['disableActions'] as List<dynamic>?;
    return disableActions?.contains(actionName) ?? false;
  }
}

// Breadcrumb permission service
class BreadcrumbPermissionService {
  final ApiClient _apiClient = ApiClient();

  Future<PathPermissionResult> checkPathPermission({
    required String path,
    Map<String, String>? params,
    Map<String, dynamic>? query,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final requestData = {
        'path': path,
        if (params != null) 'params': params,
        if (query != null) 'query': query,
        if (metadata != null) 'metadata': metadata,
      };

      final response = await _apiClient.post<Map<String, dynamic>>(
        '/auth/check-path-permission',
        data: requestData,
        fromJson: (json) => json as Map<String, dynamic>,
      );

      if (response.success && response.data != null) {
        return PathPermissionResult.fromJson(response.data!);
      } else {
        return PathPermissionResult.denied(response.message ?? 'Permission check failed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error checking path permission: $e');
      }
      return PathPermissionResult.denied('System error during permission check');
    }
  }

  Future<List<BreadcrumbItem>> getBreadcrumbNavigation({
    required String path,
    Map<String, String>? params,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'path': path,
        if (params != null) ...params,
      };

      final response = await _apiClient.get<List<dynamic>>(
        '/auth/breadcrumb-navigation',
        queryParameters: queryParams,
        fromJson: (json) => json as List<dynamic>,
      );

      if (response.success && response.data != null) {
        return response.data!
            .map((item) => BreadcrumbItem.fromJson(item as Map<String, dynamic>))
            .toList();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error getting breadcrumb navigation: $e');
      }
    }

    return [];
  }

  Future<List<String>> getAccessiblePaths() async {
    try {
      final response = await _apiClient.get<List<dynamic>>(
        '/auth/accessible-paths',
        fromJson: (json) => json as List<dynamic>,
      );

      if (response.success && response.data != null) {
        return response.data!.cast<String>();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error getting accessible paths: $e');
      }
    }

    return [];
  }
}

// Providers
final breadcrumbPermissionServiceProvider = Provider<BreadcrumbPermissionService>((ref) {
  return BreadcrumbPermissionService();
});

final pathPermissionProvider = FutureProvider.family<PathPermissionResult, PathPermissionRequest>((ref, request) async {
  final service = ref.watch(breadcrumbPermissionServiceProvider);
  return await service.checkPathPermission(
    path: request.path,
    params: request.params,
    query: request.query,
    metadata: request.metadata,
  );
});

final breadcrumbNavigationProvider = FutureProvider.family<List<BreadcrumbItem>, BreadcrumbRequest>((ref, request) async {
  final service = ref.watch(breadcrumbPermissionServiceProvider);
  return await service.getBreadcrumbNavigation(
    path: request.path,
    params: request.params,
  );
});

final accessiblePathsProvider = FutureProvider<List<String>>((ref) async {
  final service = ref.watch(breadcrumbPermissionServiceProvider);
  return await service.getAccessiblePaths();
});

// Request models
class PathPermissionRequest {
  final String path;
  final Map<String, String>? params;
  final Map<String, dynamic>? query;
  final Map<String, dynamic>? metadata;

  const PathPermissionRequest({
    required this.path,
    this.params,
    this.query,
    this.metadata,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PathPermissionRequest &&
          runtimeType == other.runtimeType &&
          path == other.path &&
          _mapEquals(params, other.params) &&
          _mapEquals(query, other.query) &&
          _mapEquals(metadata, other.metadata);

  @override
  int get hashCode => path.hashCode ^ params.hashCode ^ query.hashCode ^ metadata.hashCode;

  bool _mapEquals(Map? a, Map? b) {
    if (a == null && b == null) return true;
    if (a == null || b == null) return false;
    if (a.length != b.length) return false;
    for (final key in a.keys) {
      if (!b.containsKey(key) || a[key] != b[key]) return false;
    }
    return true;
  }
}

class BreadcrumbRequest {
  final String path;
  final Map<String, String>? params;

  const BreadcrumbRequest({
    required this.path,
    this.params,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BreadcrumbRequest &&
          runtimeType == other.runtimeType &&
          path == other.path &&
          _mapEquals(params, other.params);

  @override
  int get hashCode => path.hashCode ^ params.hashCode;

  bool _mapEquals(Map<String, String>? a, Map<String, String>? b) {
    if (a == null && b == null) return true;
    if (a == null || b == null) return false;
    if (a.length != b.length) return false;
    for (final key in a.keys) {
      if (!b.containsKey(key) || a[key] != b[key]) return false;
    }
    return true;
  }
}
