export interface TabPermission {
    tabId: string;
    tabName: string;
    accessLevel: 'full' | 'read_only' | 'restricted' | 'none';
    restrictions?: {
        hideFields?: string[];
        disableActions?: string[];
        limitData?: boolean;
        customConditions?: Record<string, any>;
    };
}
export interface ScreenPermissions {
    screen: string;
    tabs: Record<string, TabPermission>;
    globalRestrictions?: Record<string, any>;
}
export declare const SECURITY_PERMISSIONS: Record<string, ScreenPermissions>;
export declare const ELECTRICITY_PERMISSIONS: Record<string, ScreenPermissions>;
export declare const WATER_PERMISSIONS: Record<string, ScreenPermissions>;
