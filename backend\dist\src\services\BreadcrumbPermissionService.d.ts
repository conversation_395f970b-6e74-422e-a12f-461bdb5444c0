import { UserRole } from '@prisma/client';
import { PathPermissionRule } from '@/config/role-path-permissions';
export interface BreadcrumbContext {
    userId: string;
    role: UserRole;
    path: string;
    params?: Record<string, string>;
    query?: Record<string, any>;
    metadata?: Record<string, any>;
    userAssignedProperties?: string[];
    userAssignedOffices?: string[];
}
export interface BreadcrumbPermissionResult {
    hasAccess: boolean;
    accessLevel: 'full' | 'read' | 'write' | 'restricted' | 'none';
    permissions: string[];
    restrictions?: Record<string, any>;
    tabs: TabPermissionMap;
    components: ComponentPermissionMap;
    reason?: string;
}
export interface TabPermissionMap {
    [tabId: string]: {
        visible: boolean;
        enabled: boolean;
        accessLevel: 'full' | 'read' | 'restricted' | 'none';
        restrictions?: Record<string, any>;
        components: ComponentPermissionMap;
    };
}
export interface ComponentPermissionMap {
    [componentId: string]: {
        visible: boolean;
        enabled: boolean;
        permissions: string[];
        restrictions?: Record<string, any>;
    };
}
export declare class BreadcrumbPermissionService {
    /**
     * Check permissions for a breadcrumb path
     */
    static checkPathPermission(context: BreadcrumbContext): Promise<BreadcrumbPermissionResult>;
    /**
     * Check property-level access control
     */
    static checkPropertyAccess(context: BreadcrumbContext, resolvedPath: string): Promise<{
        granted: boolean;
        reason?: string;
    }>;
    /**
     * Get tab-level permissions for a path
     */
    static getTabPermissions(context: BreadcrumbContext, resolvedPath: string, pathRule: any): Promise<TabPermissionMap>;
    /**
     * Get component-level permissions for a path
     */
    static getComponentPermissions(context: BreadcrumbContext, resolvedPath: string, pathRule: PathPermissionRule): Promise<ComponentPermissionMap>;
    /**
     * Get breadcrumb navigation for a path with permission filtering
     */
    static getBreadcrumbNavigation(context: BreadcrumbContext): Promise<BreadcrumbItem[]>;
    /**
     * Get all accessible paths for a user (for navigation menu)
     */
    static getAccessiblePaths(context: Omit<BreadcrumbContext, 'path'>): Promise<string[]>;
    /**
     * Resolve path parameters
     */
    private static resolvePath;
    /**
     * Resolve breadcrumb display name with parameters
     */
    private static resolveBreadcrumbName;
    /**
     * Evaluate ABAC conditions
     */
    private static evaluateConditions;
    /**
     * Audit path access
     */
    private static auditAccess;
}
export interface BreadcrumbItem {
    path: string;
    name: string;
    level: number;
    accessible: boolean;
    accessLevel: string;
}
