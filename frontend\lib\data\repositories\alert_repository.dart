import '../models/api_response.dart';
import '../models/alert.dart';
import '../../core/services/api_client.dart';
import '../../core/services/service_locator.dart';
import '../../core/constants/api_constants.dart';

class AlertRepository {
  final ApiClient _apiClient = serviceLocator.apiClient;

  Future<ApiResponse<List<Alert>>> getAlerts({
    String? propertyId,
    String? severity,
    String? status,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (propertyId != null) queryParams['propertyId'] = propertyId;
      if (severity != null) queryParams['severity'] = severity;
      if (status != null) queryParams['status'] = status;

      final response = await _apiClient.get(
        ApiConstants.alertsList,
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        final alerts = data.map((json) => Alert.fromJson(json)).toList();
        return ApiResponse.success(data: alerts);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch alerts: $e');
    }
  }

  Future<ApiResponse<Alert>> getAlert(String id) async {
    try {
      final endpoint = ApiConstants.alertDetail.replaceAll('{alertId}', id);
      final response = await _apiClient.get(endpoint);

      if (response.isSuccess) {
        final alert = Alert.fromJson(response.data['data']);
        return ApiResponse.success(data: alert);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch alert: $e');
    }
  }

  Future<ApiResponse<Alert>> createAlert(Alert alert) async {
    try {
      final response = await _apiClient.post(
        ApiConstants.alertsList,
        data: alert.toJson(),
      );

      if (response.isSuccess) {
        final createdAlert = Alert.fromJson(response.data['data']);
        return ApiResponse.success(data: createdAlert);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to create alert: $e');
    }
  }

  Future<ApiResponse<Alert>> updateAlert(String id, Alert alert) async {
    try {
      final endpoint = ApiConstants.alertDetail.replaceAll('{alertId}', id);
      final response = await _apiClient.put(
        endpoint,
        data: alert.toJson(),
      );

      if (response.isSuccess) {
        final updatedAlert = Alert.fromJson(response.data['data']);
        return ApiResponse.success(data: updatedAlert);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to update alert: $e');
    }
  }

  Future<ApiResponse<void>> deleteAlert(String id) async {
    try {
      final endpoint = ApiConstants.alertDetail.replaceAll('{alertId}', id);
      final response = await _apiClient.delete(endpoint);

      if (response.isSuccess) {
        return ApiResponse.success(data: null);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to delete alert: $e');
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> getAlertStatistics({
    String? propertyId,
    String? dateFrom,
    String? dateTo,
  }) async {
    try {
      final queryParams = <String, dynamic>{};

      if (propertyId != null) queryParams['propertyId'] = propertyId;
      if (dateFrom != null) queryParams['dateFrom'] = dateFrom;
      if (dateTo != null) queryParams['dateTo'] = dateTo;

      final response = await _apiClient.get(
        '${ApiConstants.alertsList}/stats',
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data['data']);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch alert statistics: $e');
    }
  }

  Future<ApiResponse<Alert>> updateAlertStatus({
    required String alertId,
    required String status,
    String? notes,
  }) async {
    try {
      final endpoint = ApiConstants.alertDetail.replaceAll('{alertId}', alertId);
      final response = await _apiClient.put(
        '$endpoint/status',
        data: {
          'status': status,
          if (notes != null) 'notes': notes,
        },
      );

      if (response.isSuccess) {
        final alert = Alert.fromJson(response.data['data']);
        return ApiResponse.success(data: alert);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to update alert status: $e');
    }
  }
}

// Paginated response wrapper for alerts
class PaginatedResponse<T> {
  final List<T> data;
  final int total;
  final int page;
  final int limit;
  final int totalPages;
  final bool hasNext;
  final bool hasPrevious;

  const PaginatedResponse({
    required this.data,
    required this.total,
    required this.page,
    required this.limit,
    required this.totalPages,
    required this.hasNext,
    required this.hasPrevious,
  });

  factory PaginatedResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic) fromJsonT,
  ) {
    final dataList = (json['data'] as List)
        .map((item) => fromJsonT(item))
        .toList();

    return PaginatedResponse<T>(
      data: dataList,
      total: json['total'] ?? 0,
      page: json['page'] ?? 1,
      limit: json['limit'] ?? 20,
      totalPages: json['totalPages'] ?? 1,
      hasNext: json['hasNext'] ?? false,
      hasPrevious: json['hasPrevious'] ?? false,
    );
  }

  factory PaginatedResponse.empty() {
    return PaginatedResponse<T>(
      data: const [],
      total: 0,
      page: 1,
      limit: 20,
      totalPages: 0,
      hasNext: false,
      hasPrevious: false,
    );
  }

  Map<String, dynamic> toJson(Map<String, dynamic> Function(T) toJsonT) {
    return {
      'data': data.map((item) => toJsonT(item)).toList(),
      'total': total,
      'page': page,
      'limit': limit,
      'totalPages': totalPages,
      'hasNext': hasNext,
      'hasPrevious': hasPrevious,
    };
  }
}


