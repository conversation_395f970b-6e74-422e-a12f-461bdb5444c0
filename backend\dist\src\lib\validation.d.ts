import { z } from 'zod';
export declare const loginSchema: z.ZodObject<{
    email: z.ZodString;
    password: z.ZodString;
    deviceId: z.ZodOptional<z.ZodString>;
    deviceName: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    email: string;
    password: string;
    deviceId?: string | undefined;
    deviceName?: string | undefined;
}, {
    email: string;
    password: string;
    deviceId?: string | undefined;
    deviceName?: string | undefined;
}>;
export declare const refreshTokenSchema: z.ZodObject<{
    refreshToken: z.ZodString;
}, "strip", z.ZodTypeAny, {
    refreshToken: string;
}, {
    refreshToken: string;
}>;
export declare const createUserSchema: z.ZodObject<{
    name: z.ZodString;
    email: z.ZodString;
    phone: z.ZodString;
    role: z.<PERSON><PERSON><PERSON>num<["SUPER_ADMIN", "PROPERTY_MANAGER", "OFFICE_MANAGER", "SECURITY_PERSONNEL", "MAINTENANCE_STAFF", "CONSTRUCTION_SUPERVISOR"]>;
    assignedProperties: z.<PERSON><PERSON>Optional<z.ZodArray<z.ZodString, "many">>;
    password: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    name: string;
    email: string;
    phone: string;
    role: "SUPER_ADMIN" | "PROPERTY_MANAGER" | "OFFICE_MANAGER" | "SECURITY_PERSONNEL" | "MAINTENANCE_STAFF" | "CONSTRUCTION_SUPERVISOR";
    password?: string | undefined;
    assignedProperties?: string[] | undefined;
}, {
    name: string;
    email: string;
    phone: string;
    role: "SUPER_ADMIN" | "PROPERTY_MANAGER" | "OFFICE_MANAGER" | "SECURITY_PERSONNEL" | "MAINTENANCE_STAFF" | "CONSTRUCTION_SUPERVISOR";
    password?: string | undefined;
    assignedProperties?: string[] | undefined;
}>;
export declare const updateUserSchema: z.ZodObject<{
    name: z.ZodOptional<z.ZodString>;
    phone: z.ZodOptional<z.ZodString>;
    avatar: z.ZodOptional<z.ZodString>;
    timezone: z.ZodOptional<z.ZodString>;
    language: z.ZodOptional<z.ZodEnum<["en", "hi", "te"]>>;
}, "strip", z.ZodTypeAny, {
    name?: string | undefined;
    phone?: string | undefined;
    avatar?: string | undefined;
    timezone?: string | undefined;
    language?: "en" | "hi" | "te" | undefined;
}, {
    name?: string | undefined;
    phone?: string | undefined;
    avatar?: string | undefined;
    timezone?: string | undefined;
    language?: "en" | "hi" | "te" | undefined;
}>;
export declare const createPropertySchema: z.ZodObject<{
    name: z.ZodString;
    type: z.ZodEnum<["RESIDENTIAL", "OFFICE", "CONSTRUCTION"]>;
    address: z.ZodString;
    description: z.ZodString;
    latitude: z.ZodOptional<z.ZodNumber>;
    longitude: z.ZodOptional<z.ZodNumber>;
    images: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, "strip", z.ZodTypeAny, {
    name: string;
    description: string;
    type: "RESIDENTIAL" | "OFFICE" | "CONSTRUCTION";
    address: string;
    latitude?: number | undefined;
    longitude?: number | undefined;
    images?: string[] | undefined;
}, {
    name: string;
    description: string;
    type: "RESIDENTIAL" | "OFFICE" | "CONSTRUCTION";
    address: string;
    latitude?: number | undefined;
    longitude?: number | undefined;
    images?: string[] | undefined;
}>;
export declare const updatePropertySchema: z.ZodObject<{
    name: z.ZodOptional<z.ZodString>;
    address: z.ZodOptional<z.ZodString>;
    description: z.ZodOptional<z.ZodString>;
    latitude: z.ZodOptional<z.ZodNumber>;
    longitude: z.ZodOptional<z.ZodNumber>;
    images: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    isActive: z.ZodOptional<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    name?: string | undefined;
    description?: string | undefined;
    address?: string | undefined;
    isActive?: boolean | undefined;
    latitude?: number | undefined;
    longitude?: number | undefined;
    images?: string[] | undefined;
}, {
    name?: string | undefined;
    description?: string | undefined;
    address?: string | undefined;
    isActive?: boolean | undefined;
    latitude?: number | undefined;
    longitude?: number | undefined;
    images?: string[] | undefined;
}>;
export declare const updateSystemSchema: z.ZodObject<{
    status: z.ZodEnum<["OPERATIONAL", "WARNING", "CRITICAL", "OFFLINE"]>;
    description: z.ZodOptional<z.ZodString>;
    metadata: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
    healthScore: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    status: "OPERATIONAL" | "WARNING" | "CRITICAL" | "OFFLINE";
    description?: string | undefined;
    metadata?: Record<string, any> | undefined;
    healthScore?: number | undefined;
}, {
    status: "OPERATIONAL" | "WARNING" | "CRITICAL" | "OFFLINE";
    description?: string | undefined;
    metadata?: Record<string, any> | undefined;
    healthScore?: number | undefined;
}>;
export declare const createOfficeSchema: z.ZodObject<{
    name: z.ZodString;
    type: z.ZodEnum<["OFFICE", "CONSTRUCTION_SITE"]>;
    address: z.ZodString;
    latitude: z.ZodOptional<z.ZodNumber>;
    longitude: z.ZodOptional<z.ZodNumber>;
    workingHours: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
}, "strip", z.ZodTypeAny, {
    name: string;
    type: "OFFICE" | "CONSTRUCTION_SITE";
    address: string;
    latitude?: number | undefined;
    longitude?: number | undefined;
    workingHours?: Record<string, any> | undefined;
}, {
    name: string;
    type: "OFFICE" | "CONSTRUCTION_SITE";
    address: string;
    latitude?: number | undefined;
    longitude?: number | undefined;
    workingHours?: Record<string, any> | undefined;
}>;
export declare const createEmployeeSchema: z.ZodObject<{
    officeId: z.ZodString;
    name: z.ZodString;
    email: z.ZodOptional<z.ZodString>;
    phone: z.ZodOptional<z.ZodString>;
    employeeId: z.ZodString;
    designation: z.ZodString;
    department: z.ZodOptional<z.ZodString>;
    joinDate: z.ZodString;
}, "strip", z.ZodTypeAny, {
    name: string;
    officeId: string;
    employeeId: string;
    designation: string;
    joinDate: string;
    department?: string | undefined;
    email?: string | undefined;
    phone?: string | undefined;
}, {
    name: string;
    officeId: string;
    employeeId: string;
    designation: string;
    joinDate: string;
    department?: string | undefined;
    email?: string | undefined;
    phone?: string | undefined;
}>;
export declare const submitAttendanceSchema: z.ZodObject<{
    date: z.ZodString;
    records: z.ZodArray<z.ZodObject<{
        employeeId: z.ZodOptional<z.ZodString>;
        userId: z.ZodOptional<z.ZodString>;
        status: z.ZodEnum<["PRESENT", "ABSENT", "LATE", "HALF_DAY", "LEAVE"]>;
        checkInTime: z.ZodOptional<z.ZodString>;
        checkOutTime: z.ZodOptional<z.ZodString>;
        hoursWorked: z.ZodOptional<z.ZodNumber>;
        overtime: z.ZodOptional<z.ZodNumber>;
        notes: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        status: "PRESENT" | "ABSENT" | "LATE" | "HALF_DAY" | "LEAVE";
        userId?: string | undefined;
        employeeId?: string | undefined;
        checkInTime?: string | undefined;
        checkOutTime?: string | undefined;
        hoursWorked?: number | undefined;
        overtime?: number | undefined;
        notes?: string | undefined;
    }, {
        status: "PRESENT" | "ABSENT" | "LATE" | "HALF_DAY" | "LEAVE";
        userId?: string | undefined;
        employeeId?: string | undefined;
        checkInTime?: string | undefined;
        checkOutTime?: string | undefined;
        hoursWorked?: number | undefined;
        overtime?: number | undefined;
        notes?: string | undefined;
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    date: string;
    records: {
        status: "PRESENT" | "ABSENT" | "LATE" | "HALF_DAY" | "LEAVE";
        userId?: string | undefined;
        employeeId?: string | undefined;
        checkInTime?: string | undefined;
        checkOutTime?: string | undefined;
        hoursWorked?: number | undefined;
        overtime?: number | undefined;
        notes?: string | undefined;
    }[];
}, {
    date: string;
    records: {
        status: "PRESENT" | "ABSENT" | "LATE" | "HALF_DAY" | "LEAVE";
        userId?: string | undefined;
        employeeId?: string | undefined;
        checkInTime?: string | undefined;
        checkOutTime?: string | undefined;
        hoursWorked?: number | undefined;
        overtime?: number | undefined;
        notes?: string | undefined;
    }[];
}>;
export declare const createAlertSchema: z.ZodObject<{
    propertyId: z.ZodOptional<z.ZodString>;
    title: z.ZodString;
    message: z.ZodString;
    severity: z.ZodEnum<["LOW", "MEDIUM", "HIGH", "CRITICAL"]>;
    category: z.ZodOptional<z.ZodString>;
    metadata: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
}, "strip", z.ZodTypeAny, {
    severity: "CRITICAL" | "LOW" | "MEDIUM" | "HIGH";
    title: string;
    message: string;
    propertyId?: string | undefined;
    metadata?: Record<string, any> | undefined;
    category?: string | undefined;
}, {
    severity: "CRITICAL" | "LOW" | "MEDIUM" | "HIGH";
    title: string;
    message: string;
    propertyId?: string | undefined;
    metadata?: Record<string, any> | undefined;
    category?: string | undefined;
}>;
export declare const updateAlertSchema: z.ZodObject<{
    status: z.ZodOptional<z.ZodEnum<["OPEN", "ACKNOWLEDGED", "RESOLVED"]>>;
    resolvedAt: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    status?: "OPEN" | "ACKNOWLEDGED" | "RESOLVED" | undefined;
    resolvedAt?: string | undefined;
}, {
    status?: "OPEN" | "ACKNOWLEDGED" | "RESOLVED" | undefined;
    resolvedAt?: string | undefined;
}>;
export declare const createOTTServiceSchema: z.ZodObject<{
    platform: z.ZodString;
    plan: z.ZodString;
    loginId: z.ZodOptional<z.ZodString>;
    password: z.ZodOptional<z.ZodString>;
    nextPayment: z.ZodOptional<z.ZodString>;
    status: z.ZodOptional<z.ZodEnum<["PENDING", "ACTIVE", "EXPIRED", "CANCELLED"]>>;
}, "strip", z.ZodTypeAny, {
    platform: string;
    plan: string;
    status?: "PENDING" | "ACTIVE" | "EXPIRED" | "CANCELLED" | undefined;
    password?: string | undefined;
    loginId?: string | undefined;
    nextPayment?: string | undefined;
}, {
    platform: string;
    plan: string;
    status?: "PENDING" | "ACTIVE" | "EXPIRED" | "CANCELLED" | undefined;
    password?: string | undefined;
    loginId?: string | undefined;
    nextPayment?: string | undefined;
}>;
export declare const updateOTTServiceSchema: z.ZodObject<{
    platform: z.ZodOptional<z.ZodString>;
    plan: z.ZodOptional<z.ZodString>;
    loginId: z.ZodOptional<z.ZodString>;
    password: z.ZodOptional<z.ZodString>;
    nextPayment: z.ZodOptional<z.ZodString>;
    status: z.ZodOptional<z.ZodEnum<["PENDING", "ACTIVE", "EXPIRED", "CANCELLED"]>>;
}, "strip", z.ZodTypeAny, {
    status?: "PENDING" | "ACTIVE" | "EXPIRED" | "CANCELLED" | undefined;
    password?: string | undefined;
    platform?: string | undefined;
    plan?: string | undefined;
    loginId?: string | undefined;
    nextPayment?: string | undefined;
}, {
    status?: "PENDING" | "ACTIVE" | "EXPIRED" | "CANCELLED" | undefined;
    password?: string | undefined;
    platform?: string | undefined;
    plan?: string | undefined;
    loginId?: string | undefined;
    nextPayment?: string | undefined;
}>;
export declare const paginationSchema: z.ZodObject<{
    page: z.ZodDefault<z.ZodNumber>;
    limit: z.ZodDefault<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    page: number;
    limit: number;
}, {
    page?: number | undefined;
    limit?: number | undefined;
}>;
export declare const propertyQuerySchema: z.ZodObject<{
    page: z.ZodDefault<z.ZodNumber>;
    limit: z.ZodDefault<z.ZodNumber>;
} & {
    type: z.ZodOptional<z.ZodEnum<["RESIDENTIAL", "OFFICE", "CONSTRUCTION"]>>;
    status: z.ZodOptional<z.ZodEnum<["OPERATIONAL", "WARNING", "CRITICAL", "OFFLINE"]>>;
    search: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    page: number;
    limit: number;
    status?: "OPERATIONAL" | "WARNING" | "CRITICAL" | "OFFLINE" | undefined;
    type?: "RESIDENTIAL" | "OFFICE" | "CONSTRUCTION" | undefined;
    search?: string | undefined;
}, {
    status?: "OPERATIONAL" | "WARNING" | "CRITICAL" | "OFFLINE" | undefined;
    type?: "RESIDENTIAL" | "OFFICE" | "CONSTRUCTION" | undefined;
    page?: number | undefined;
    limit?: number | undefined;
    search?: string | undefined;
}>;
export declare const alertQuerySchema: z.ZodObject<{
    page: z.ZodDefault<z.ZodNumber>;
    limit: z.ZodDefault<z.ZodNumber>;
} & {
    severity: z.ZodOptional<z.ZodEnum<["LOW", "MEDIUM", "HIGH", "CRITICAL"]>>;
    status: z.ZodOptional<z.ZodEnum<["OPEN", "ACKNOWLEDGED", "RESOLVED"]>>;
    propertyId: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    page: number;
    limit: number;
    propertyId?: string | undefined;
    status?: "OPEN" | "ACKNOWLEDGED" | "RESOLVED" | undefined;
    severity?: "CRITICAL" | "LOW" | "MEDIUM" | "HIGH" | undefined;
}, {
    propertyId?: string | undefined;
    status?: "OPEN" | "ACKNOWLEDGED" | "RESOLVED" | undefined;
    page?: number | undefined;
    limit?: number | undefined;
    severity?: "CRITICAL" | "LOW" | "MEDIUM" | "HIGH" | undefined;
}>;
export declare const attendanceQuerySchema: z.ZodObject<{
    date: z.ZodOptional<z.ZodString>;
    startDate: z.ZodOptional<z.ZodString>;
    endDate: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    startDate?: string | undefined;
    date?: string | undefined;
    endDate?: string | undefined;
}, {
    startDate?: string | undefined;
    date?: string | undefined;
    endDate?: string | undefined;
}>;
export declare const dashboardQuerySchema: z.ZodObject<{
    propertyIds: z.ZodOptional<z.ZodString>;
    timeRange: z.ZodDefault<z.ZodEnum<["24h", "7d", "30d", "90d"]>>;
}, "strip", z.ZodTypeAny, {
    timeRange: "7d" | "24h" | "30d" | "90d";
    propertyIds?: string | undefined;
}, {
    propertyIds?: string | undefined;
    timeRange?: "7d" | "24h" | "30d" | "90d" | undefined;
}>;
export declare const fileUploadSchema: z.ZodObject<{
    fieldname: z.ZodString;
    originalname: z.ZodString;
    encoding: z.ZodString;
    mimetype: z.ZodEffects<z.ZodString, string, string>;
    size: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    fieldname: string;
    originalname: string;
    encoding: string;
    mimetype: string;
    size: number;
}, {
    fieldname: string;
    originalname: string;
    encoding: string;
    mimetype: string;
    size: number;
}>;
export declare function validateUUID(id: string): boolean;
export declare function validateEmail(email: string): boolean;
export declare function validatePhone(phone: string): boolean;
export declare function validateDate(date: string): boolean;
export declare function validateTime(time: string): boolean;
