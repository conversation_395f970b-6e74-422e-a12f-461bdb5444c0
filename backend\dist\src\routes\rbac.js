"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middleware/auth");
const client_1 = require("@prisma/client");
const router = express_1.default.Router();
const prisma = new client_1.PrismaClient();
// Apply authentication to all routes
router.use(auth_1.authenticateToken);
/**
 * @swagger
 * /v1/rbac/roles:
 *   get:
 *     summary: Get all available roles and their permissions
 *     tags: [RBAC]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Roles and permissions retrieved successfully
 */
router.get('/roles', (0, auth_1.enhancedAuthorize)('rbac', 'view'), async (req, res) => {
    try {
        const rolePermissions = {
            [client_1.UserRole.SUPER_ADMIN]: {
                name: 'Super Administrator',
                description: 'Full system access with all permissions',
                permissions: ['*'],
                screens: ['*'],
                features: ['*']
            },
            [client_1.UserRole.PROPERTY_MANAGER]: {
                name: 'Property Manager',
                description: 'Manages properties, employees, and systems',
                permissions: [
                    'employees.*', 'maintenance.*', 'systems.*', 'dashboard.*',
                    'alerts.*', 'properties.*', 'reports.view'
                ],
                screens: [
                    'dashboard', 'employees', 'maintenance', 'systems',
                    'properties', 'alerts', 'reports'
                ],
                features: [
                    'employee_management', 'maintenance_tracking', 'system_monitoring',
                    'property_overview', 'alert_management', 'report_generation'
                ]
            },
            [client_1.UserRole.OFFICE_MANAGER]: {
                name: 'Office Manager',
                description: 'Manages office operations and employee attendance',
                permissions: [
                    'employees.view', 'employees.create', 'employees.update',
                    'employees.attendance.*', 'dashboard.view', 'reports.view'
                ],
                screens: ['dashboard', 'employees', 'reports'],
                features: ['employee_management', 'attendance_tracking', 'basic_reporting']
            },
            [client_1.UserRole.SECURITY_PERSONNEL]: {
                name: 'Security Personnel',
                description: 'Monitors security systems and manages alerts',
                permissions: [
                    'systems.view', 'security.*', 'alerts.*', 'dashboard.view'
                ],
                screens: ['dashboard', 'systems', 'alerts'],
                features: ['security_monitoring', 'alert_management', 'system_status']
            },
            [client_1.UserRole.MAINTENANCE_STAFF]: {
                name: 'Maintenance Staff',
                description: 'Handles maintenance requests and system upkeep',
                permissions: [
                    'maintenance.*', 'systems.view', 'dashboard.view'
                ],
                screens: ['dashboard', 'maintenance', 'systems'],
                features: ['maintenance_management', 'system_monitoring', 'work_orders']
            },
            [client_1.UserRole.CONSTRUCTION_SUPERVISOR]: {
                name: 'Construction Supervisor',
                description: 'Oversees construction projects and worker attendance',
                permissions: [
                    'employees.view', 'employees.attendance.view', 'systems.view',
                    'dashboard.view', 'reports.view'
                ],
                screens: ['dashboard', 'employees', 'systems', 'reports'],
                features: ['worker_oversight', 'attendance_monitoring', 'progress_tracking']
            }
        };
        res.json({
            success: true,
            data: rolePermissions
        });
    }
    catch (error) {
        console.error('Error fetching roles:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch roles',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
/**
 * @swagger
 * /v1/rbac/permissions:
 *   get:
 *     summary: Get all available permissions in the system
 *     tags: [RBAC]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Permissions retrieved successfully
 */
router.get('/permissions', (0, auth_1.enhancedAuthorize)('rbac', 'view'), async (req, res) => {
    try {
        const allPermissions = {
            employees: {
                name: 'Employee Management',
                permissions: ['view', 'create', 'update', 'delete', 'attendance.view', 'attendance.create']
            },
            maintenance: {
                name: 'Maintenance Management',
                permissions: ['view', 'create', 'update', 'delete', 'assign', 'complete']
            },
            systems: {
                name: 'System Management',
                permissions: ['view', 'update', 'monitor', 'configure']
            },
            security: {
                name: 'Security Management',
                permissions: ['view', 'update', 'monitor', 'configure', 'alerts']
            },
            properties: {
                name: 'Property Management',
                permissions: ['view', 'create', 'update', 'delete', 'configure']
            },
            alerts: {
                name: 'Alert Management',
                permissions: ['view', 'create', 'update', 'delete', 'acknowledge']
            },
            dashboard: {
                name: 'Dashboard Access',
                permissions: ['view', 'configure']
            },
            reports: {
                name: 'Reports & Analytics',
                permissions: ['view', 'generate', 'export']
            },
            rbac: {
                name: 'Role & Permission Management',
                permissions: ['view', 'update', 'assign']
            }
        };
        res.json({
            success: true,
            data: allPermissions
        });
    }
    catch (error) {
        console.error('Error fetching permissions:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch permissions',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
/**
 * @swagger
 * /v1/rbac/user/{userId}/permissions:
 *   get:
 *     summary: Get user's effective permissions
 *     tags: [RBAC]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: User permissions retrieved successfully
 */
router.get('/user/:userId/permissions', (0, auth_1.enhancedAuthorize)('rbac', 'view'), async (req, res) => {
    try {
        const { userId } = req.params;
        const user = await prisma.user.findUnique({
            where: { id: userId },
            select: { id: true, email: true, role: true, name: true }
        });
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }
        // Get role-based permissions (simplified version)
        const rolePermissions = {
            [client_1.UserRole.SUPER_ADMIN]: ['*'],
            [client_1.UserRole.PROPERTY_MANAGER]: [
                'employees.*', 'maintenance.*', 'systems.*', 'dashboard.*',
                'alerts.*', 'properties.*', 'reports.view'
            ],
            [client_1.UserRole.OFFICE_MANAGER]: [
                'employees.view', 'employees.create', 'employees.update',
                'employees.attendance.*', 'dashboard.view'
            ],
            [client_1.UserRole.SECURITY_PERSONNEL]: [
                'systems.view', 'security.*', 'alerts.*', 'dashboard.view'
            ],
            [client_1.UserRole.MAINTENANCE_STAFF]: [
                'maintenance.*', 'systems.view', 'dashboard.view'
            ],
            [client_1.UserRole.CONSTRUCTION_SUPERVISOR]: [
                'employees.view', 'employees.attendance.view', 'systems.view',
                'dashboard.view', 'reports.view'
            ]
        };
        const userPermissions = rolePermissions[user.role] || [];
        res.json({
            success: true,
            data: {
                user: {
                    id: user.id,
                    email: user.email,
                    name: user.name,
                    role: user.role
                },
                permissions: userPermissions,
                hasFullAccess: userPermissions.includes('*')
            }
        });
    }
    catch (error) {
        console.error('Error fetching user permissions:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch user permissions',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
/**
 * @swagger
 * /v1/rbac/user/{userId}/role:
 *   put:
 *     summary: Update user's role
 *     tags: [RBAC]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - role
 *             properties:
 *               role:
 *                 type: string
 *                 enum: [SUPER_ADMIN, PROPERTY_MANAGER, OFFICE_MANAGER, SECURITY_PERSONNEL, MAINTENANCE_STAFF, CONSTRUCTION_SUPERVISOR]
 *     responses:
 *       200:
 *         description: User role updated successfully
 */
router.put('/user/:userId/role', (0, auth_1.enhancedAuthorize)('rbac', 'update'), async (req, res) => {
    try {
        const { userId } = req.params;
        const { role } = req.body;
        const currentUserId = req.user?.id;
        // Validate role
        if (!Object.values(client_1.UserRole).includes(role)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid role specified'
            });
        }
        // Prevent users from changing their own role
        if (userId === currentUserId) {
            return res.status(403).json({
                success: false,
                message: 'Cannot change your own role'
            });
        }
        const user = await prisma.user.findUnique({
            where: { id: userId }
        });
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }
        const updatedUser = await prisma.user.update({
            where: { id: userId },
            data: { role },
            select: { id: true, email: true, name: true, role: true }
        });
        // Log the activity
        await prisma.activity.create({
            data: {
                userId: currentUserId,
                action: 'USER_ROLE_UPDATED',
                description: `Updated user role from ${user.role} to ${role}`,
                metadata: {
                    targetUserId: userId,
                    oldRole: user.role,
                    newRole: role
                }
            }
        });
        res.json({
            success: true,
            data: updatedUser,
            message: 'User role updated successfully'
        });
    }
    catch (error) {
        console.error('Error updating user role:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update user role',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.default = router;
