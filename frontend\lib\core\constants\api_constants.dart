class ApiConstants {
  // Base URLs
  // static const String baseUrl = 'http://13.204.54.130:3000';
  // static const String baseUrl = 'http://192.168.1.220:3000';
  static const String baseUrl = 'http://172.16.4.215:3000';
  static const String apiVersion = 'v1';
  static const String apiBaseUrl = '$baseUrl/v1';
  

  
  // Endpoints
  static const String auth = '/auth';
  static const String properties = '/properties';
  static const String dashboard = '/dashboard';
  static const String offices = '/offices';
  static const String users = '/users';
  static const String reports = '/reports';
  static const String notifications = '/notifications';
  static const String files = '/files';
  static const String health = '/health';
  static const String ott = '/ott';
  
  // Auth endpoints
  static const String login = '$auth/login';
  static const String logout = '$auth/logout';
  static const String refresh = '$auth/refresh';
  static const String me = '$auth/me';
  static const String register = '$auth/register';
  static const String refreshToken = '$auth/refresh';
  static const String forgotPassword = '$auth/forgot-password';
  static const String resetPassword = '$auth/reset-password';
  static const String verifyEmail = '$auth/verify-email';
  static const String resendVerification = '$auth/resend-verification';
  
  // Property endpoints
  static const String propertiesList = properties;
  static const String propertyDetail = '$properties/{propertyId}';
  static const String propertySystems = '$properties/{propertyId}/systems';
  static const String propertySystemDetail = '$properties/{propertyId}/systems/{systemType}';
  static const String propertyUnits = '$properties/{propertyId}/units';
  static const String propertyTenants = '$properties/{propertyId}/tenants';
  static const String propertyDocuments = '$properties/{propertyId}/documents';
  
  // Dashboard endpoints
  static const String dashboardOverview = '$dashboard/overview';
  static const String dashboardAlerts = '$dashboard/alerts';
  
  // Office endpoints
  static const String officesList = offices;
  static const String officeDetail = '$offices/{officeId}';
  static const String officeEmployees = '$offices/{officeId}/employees';
  static const String officeAttendance = '$offices/{officeId}/attendance';

  // OTT endpoints
  static const String ottServices = '$ott/{propertyId}';
  static const String ottServiceDetail = '$ott/{propertyId}/{serviceId}';

  // System endpoints
  static const String systems = '/systems';
  static const String systemStatusList = '$systems/status';
  static const String waterSystems = '$systems/water/{propertyId}';
  static const String electricitySystems = '$systems/electricity/{propertyId}';
  static const String securitySystems = '$systems/security/{propertyId}';
  static const String systemStatus = '$systems/status/{id}';

  // Maintenance endpoints
  static const String maintenance = '/maintenance';
  static const String maintenanceIssues = '$maintenance/issues';
  static const String maintenanceIssue = '$maintenance/issues/{id}';
  static const String maintenanceDepartments = '$maintenance/departments';
  static const String maintenanceFunctions = '$maintenance/functions';
  static const String maintenanceStats = '$maintenance/statistics';

  // Employee endpoints
  static const String employees = '/employees';
  static const String employee = '$employees/{id}';
  static const String employeeStats = '$employees/stats';
  static const String employeeAttendance = '$employees/attendance';

  // Alert endpoints
  static const String alertsList = '/alerts';
  static const String alertDetail = '/alerts/{alertId}';
  static const String alertStatuses = '/alerts/statuses';
  static const String alertCategories = '/alerts/categories';

  // User endpoints
  static const String usersList = users;
  static const String userDetail = '$users/{userId}';
  static const String userProfile = '$users/profile';
  
  // Report endpoints
  static const String reportsProperties = '$reports/properties';
  static const String reportsAttendance = '$reports/attendance';
  static const String reportsSystemHealth = '$reports/system-health';
  
  // Notification endpoints
  static const String notificationsList = notifications;
  static const String notificationRead = '$notifications/{notificationId}/read';
  static const String notificationMarkAllRead = '$notifications/mark-all-read';
  static const String notificationUnreadCount = '$notifications/unread-count';
  
  // File endpoints
  static const String uploadAvatar = '$files/upload/avatar';
  static const String uploadPropertyImages = '$files/upload/property-images';
  static const String uploadDocuments = '$files/upload/documents';
  static const String serveFile = '$files/{category}/{filename}';
  
  // Headers
  static const String authHeader = 'Authorization';
  static const String contentType = 'Content-Type';
  static const String applicationJson = 'application/json';
  static const String multipartFormData = 'multipart/form-data';
  
  // Storage keys
  static const String accessTokenKey = 'access_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userDataKey = 'user_data';
  static const String deviceIdKey = 'device_id';
  
  // Request timeouts
  static const int connectTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
  static const int sendTimeout = 30000; // 30 seconds
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // File upload
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedImageTypes = [
    'image/jpeg',
    'image/png',
    'image/webp',
  ];
  static const List<String> allowedDocumentTypes = [
    'application/pdf',
    'image/jpeg',
    'image/png',
    'image/webp',
  ];
  

  
  // Error codes
  static const String errorUnauthorized = 'UNAUTHORIZED';
  static const String errorForbidden = 'FORBIDDEN';
  static const String errorNotFound = 'NOT_FOUND';
  static const String errorValidation = 'VALIDATION_ERROR';
  static const String errorRateLimit = 'RATE_LIMIT_EXCEEDED';
  static const String errorServerError = 'INTERNAL_SERVER_ERROR';
  static const String errorNetworkError = 'NETWORK_ERROR';
  static const String errorTimeout = 'TIMEOUT_ERROR';
  
  // System types
  static const List<String> systemTypes = [
    'WATER',
    'ELECTRICITY',
    'SECURITY',
    'INTERNET',
    'OTT',
    'MAINTENANCE',
  ];
  
  // System statuses
  static const List<String> systemStatuses = [
    'OPERATIONAL',
    'WARNING',
    'CRITICAL',
    'OFFLINE',
  ];
  
  // Property types
  static const List<String> propertyTypes = [
    'RESIDENTIAL',
    'OFFICE',
    'CONSTRUCTION',
  ];
  
  // User roles
  static const List<String> userRoles = [
    'SUPER_ADMIN',
    'PROPERTY_MANAGER',
    'OFFICE_MANAGER',
    'SECURITY_PERSONNEL',
    'MAINTENANCE_STAFF',
    'CONSTRUCTION_SUPERVISOR',
  ];
  
  // Alert severities
  static const List<String> alertSeverities = [
    'LOW',
    'MEDIUM',
    'HIGH',
    'CRITICAL',
  ];
  
  // Alert statuses list
  static const List<String> alertStatusesList = [
    'OPEN',
    'ACKNOWLEDGED',
    'RESOLVED',
  ];
  
  // Notification types
  static const List<String> notificationTypes = [
    'INFO',
    'WARNING',
    'ERROR',
    'SUCCESS',
  ];
  
  // Notification priorities
  static const List<String> notificationPriorities = [
    'LOW',
    'MEDIUM',
    'HIGH',
    'URGENT',
  ];
  
  // Time ranges
  static const List<String> timeRanges = [
    '24h',
    '7d',
    '30d',
    '90d',
  ];
  
  // Office types
  static const List<String> officeTypes = [
    'OFFICE',
    'CONSTRUCTION_SITE',
  ];
  
  // Attendance statuses
  static const List<String> attendanceStatuses = [
    'PRESENT',
    'ABSENT',
    'LATE',
    'HALF_DAY',
    'LEAVE',
  ];

  // OTT statuses
  static const List<String> ottStatuses = [
    'PENDING',
    'ACTIVE',
    'EXPIRED',
    'CANCELLED',
  ];
}
