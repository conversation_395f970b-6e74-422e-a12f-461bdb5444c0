// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ApiResponse<T> _$ApiResponseFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) =>
    ApiResponse<T>(
      success: json['success'] as bool,
      data: _$nullableGenericFromJson(json['data'], fromJsonT),
      message: json['message'] as String?,
      error: json['error'] as String?,
      timestamp: json['timestamp'] as String,
      path: json['path'] as String?,
    );

Map<String, dynamic> _$ApiResponseToJson<T>(
  ApiResponse<T> instance,
  Object? Function(T value) toJsonT,
) =>
    <String, dynamic>{
      'success': instance.success,
      'data': _$nullableGenericToJson(instance.data, toJsonT),
      'message': instance.message,
      'error': instance.error,
      'timestamp': instance.timestamp,
      'path': instance.path,
    };

T? _$nullableGenericFromJson<T>(
  Object? input,
  T Function(Object? json) fromJson,
) =>
    input == null ? null : fromJson(input);

Object? _$nullableGenericToJson<T>(
  T? input,
  Object? Function(T value) toJson,
) =>
    input == null ? null : toJson(input);

PaginatedResponse<T> _$PaginatedResponseFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) =>
    PaginatedResponse<T>(
      data: (json['data'] as List<dynamic>).map(fromJsonT).toList(),
      pagination:
          Pagination.fromJson(json['pagination'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PaginatedResponseToJson<T>(
  PaginatedResponse<T> instance,
  Object? Function(T value) toJsonT,
) =>
    <String, dynamic>{
      'data': instance.data.map(toJsonT).toList(),
      'pagination': instance.pagination,
    };

Pagination _$PaginationFromJson(Map<String, dynamic> json) => Pagination(
      page: (json['page'] as num).toInt(),
      limit: (json['limit'] as num).toInt(),
      total: (json['total'] as num).toInt(),
      totalPages: (json['totalPages'] as num).toInt(),
      hasNext: json['hasNext'] as bool,
      hasPrev: json['hasPrev'] as bool,
    );

Map<String, dynamic> _$PaginationToJson(Pagination instance) =>
    <String, dynamic>{
      'page': instance.page,
      'limit': instance.limit,
      'total': instance.total,
      'totalPages': instance.totalPages,
      'hasNext': instance.hasNext,
      'hasPrev': instance.hasPrev,
    };

ErrorResponse _$ErrorResponseFromJson(Map<String, dynamic> json) =>
    ErrorResponse(
      error: json['error'] as String,
      message: json['message'] as String,
      timestamp: json['timestamp'] as String,
      path: json['path'] as String?,
      details: json['details'] as Map<String, dynamic>?,
      retryAfter: (json['retryAfter'] as num?)?.toInt(),
    );

Map<String, dynamic> _$ErrorResponseToJson(ErrorResponse instance) =>
    <String, dynamic>{
      'error': instance.error,
      'message': instance.message,
      'timestamp': instance.timestamp,
      'path': instance.path,
      'details': instance.details,
      'retryAfter': instance.retryAfter,
    };

HealthResponse _$HealthResponseFromJson(Map<String, dynamic> json) =>
    HealthResponse(
      status: json['status'] as String,
      timestamp: json['timestamp'] as String,
      uptime: (json['uptime'] as num).toDouble(),
      version: json['version'] as String,
    );

Map<String, dynamic> _$HealthResponseToJson(HealthResponse instance) =>
    <String, dynamic>{
      'status': instance.status,
      'timestamp': instance.timestamp,
      'uptime': instance.uptime,
      'version': instance.version,
    };

DetailedHealthResponse _$DetailedHealthResponseFromJson(
        Map<String, dynamic> json) =>
    DetailedHealthResponse(
      status: json['status'] as String,
      timestamp: json['timestamp'] as String,
      uptime: (json['uptime'] as num).toDouble(),
      version: json['version'] as String,
      services: (json['services'] as Map<String, dynamic>).map(
        (k, e) =>
            MapEntry(k, ServiceHealth.fromJson(e as Map<String, dynamic>)),
      ),
      metrics: HealthMetrics.fromJson(json['metrics'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$DetailedHealthResponseToJson(
        DetailedHealthResponse instance) =>
    <String, dynamic>{
      'status': instance.status,
      'timestamp': instance.timestamp,
      'uptime': instance.uptime,
      'version': instance.version,
      'services': instance.services,
      'metrics': instance.metrics,
    };

ServiceHealth _$ServiceHealthFromJson(Map<String, dynamic> json) =>
    ServiceHealth(
      status: json['status'] as String,
      responseTime: (json['responseTime'] as num).toDouble(),
      lastCheck: json['lastCheck'] as String,
      error: json['error'] as String?,
    );

Map<String, dynamic> _$ServiceHealthToJson(ServiceHealth instance) =>
    <String, dynamic>{
      'status': instance.status,
      'responseTime': instance.responseTime,
      'lastCheck': instance.lastCheck,
      'error': instance.error,
    };

HealthMetrics _$HealthMetricsFromJson(Map<String, dynamic> json) =>
    HealthMetrics(
      memoryUsage: (json['memoryUsage'] as num).toDouble(),
      cpuUsage: (json['cpuUsage'] as num).toDouble(),
      activeConnections: (json['activeConnections'] as num).toInt(),
    );

Map<String, dynamic> _$HealthMetricsToJson(HealthMetrics instance) =>
    <String, dynamic>{
      'memoryUsage': instance.memoryUsage,
      'cpuUsage': instance.cpuUsage,
      'activeConnections': instance.activeConnections,
    };

FileUploadResponse _$FileUploadResponseFromJson(Map<String, dynamic> json) =>
    FileUploadResponse(
      filename: json['filename'] as String,
      originalName: json['originalName'] as String,
      mimetype: json['mimetype'] as String,
      size: (json['size'] as num).toInt(),
      url: json['url'] as String,
      uploadedAt: json['uploadedAt'] as String,
    );

Map<String, dynamic> _$FileUploadResponseToJson(FileUploadResponse instance) =>
    <String, dynamic>{
      'filename': instance.filename,
      'originalName': instance.originalName,
      'mimetype': instance.mimetype,
      'size': instance.size,
      'url': instance.url,
      'uploadedAt': instance.uploadedAt,
    };

ListResponse<T> _$ListResponseFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) =>
    ListResponse<T>(
      data: (json['data'] as List<dynamic>).map(fromJsonT).toList(),
    );

Map<String, dynamic> _$ListResponseToJson<T>(
  ListResponse<T> instance,
  Object? Function(T value) toJsonT,
) =>
    <String, dynamic>{
      'data': instance.data.map(toJsonT).toList(),
    };
