import 'package:json_annotation/json_annotation.dart';

part 'alert.g.dart';

@JsonSerializable()
class Alert {
  final String id;
  final String? propertyId;
  final String title;
  final String message;
  final String severity;
  final String status;
  final String? category;
  final Map<String, dynamic>? metadata;
  final String createdAt;
  final String updatedAt;
  final String? resolvedAt;
  final PropertyInfo? property;

  const Alert({
    required this.id,
    this.propertyId,
    required this.title,
    required this.message,
    required this.severity,
    required this.status,
    this.category,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
    this.resolvedAt,
    this.property,
  });

  factory Alert.fromJson(Map<String, dynamic> json) => _$AlertFromJson(json);
  Map<String, dynamic> toJson() => _$AlertToJson(this);

  Alert copyWith({
    String? id,
    String? propertyId,
    String? title,
    String? message,
    String? severity,
    String? status,
    String? category,
    Map<String, dynamic>? metadata,
    String? createdAt,
    String? updatedAt,
    String? resolvedAt,
    PropertyInfo? property,
  }) {
    return Alert(
      id: id ?? this.id,
      propertyId: propertyId ?? this.propertyId,
      title: title ?? this.title,
      message: message ?? this.message,
      severity: severity ?? this.severity,
      status: status ?? this.status,
      category: category ?? this.category,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      resolvedAt: resolvedAt ?? this.resolvedAt,
      property: property ?? this.property,
    );
  }

  // Helper getters
  String get description => message; // Alias for message

  bool get isOpen => status == 'OPEN';
  bool get isAcknowledged => status == 'ACKNOWLEDGED';
  bool get isResolved => status == 'RESOLVED';

  bool get isLow => severity == 'LOW';
  bool get isMedium => severity == 'MEDIUM';
  bool get isHigh => severity == 'HIGH';
  bool get isCritical => severity == 'CRITICAL';
  
  DateTime get createdDateTime => DateTime.parse(createdAt);
  DateTime get updatedDateTime => DateTime.parse(updatedAt);
  DateTime? get resolvedDateTime => resolvedAt != null ? DateTime.parse(resolvedAt!) : null;
  
  Duration? get resolutionTime {
    if (resolvedAt != null) {
      return resolvedDateTime!.difference(createdDateTime);
    }
    return null;
  }
  
  Duration get age => DateTime.now().difference(createdDateTime);
}

@JsonSerializable()
class PropertyInfo {
  final String id;
  final String name;
  final String type;

  const PropertyInfo({
    required this.id,
    required this.name,
    required this.type,
  });

  factory PropertyInfo.fromJson(Map<String, dynamic> json) => _$PropertyInfoFromJson(json);
  Map<String, dynamic> toJson() => _$PropertyInfoToJson(this);
}

@JsonSerializable()
class CreateAlertRequest {
  final String? propertyId;
  final String title;
  final String message;
  final String severity;
  final String? category;
  final Map<String, dynamic>? metadata;

  const CreateAlertRequest({
    this.propertyId,
    required this.title,
    required this.message,
    required this.severity,
    this.category,
    this.metadata,
  });

  factory CreateAlertRequest.fromJson(Map<String, dynamic> json) => _$CreateAlertRequestFromJson(json);
  Map<String, dynamic> toJson() => _$CreateAlertRequestToJson(this);
}

@JsonSerializable()
class UpdateAlertRequest {
  final String? status;
  final String? resolvedAt;

  const UpdateAlertRequest({
    this.status,
    this.resolvedAt,
  });

  factory UpdateAlertRequest.fromJson(Map<String, dynamic> json) => _$UpdateAlertRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UpdateAlertRequestToJson(this);
}

@JsonSerializable()
class AlertQueryParams {
  final int? page;
  final int? limit;
  final String? severity;
  final String? status;
  final String? propertyId;

  const AlertQueryParams({
    this.page,
    this.limit,
    this.severity,
    this.status,
    this.propertyId,
  });

  factory AlertQueryParams.fromJson(Map<String, dynamic> json) => _$AlertQueryParamsFromJson(json);
  Map<String, dynamic> toJson() => _$AlertQueryParamsToJson(this);

  Map<String, dynamic> toQueryParameters() {
    final params = <String, dynamic>{};
    if (page != null) params['page'] = page.toString();
    if (limit != null) params['limit'] = limit.toString();
    if (severity != null) params['severity'] = severity;
    if (status != null) params['status'] = status;
    if (propertyId != null) params['propertyId'] = propertyId;
    return params;
  }
}

// Alert statistics for dashboard
@JsonSerializable()
class AlertStatistics {
  final int total;
  final int open;
  final int acknowledged;
  final int resolved;
  final int low;
  final int medium;
  final int high;
  final int critical;
  final double averageResolutionTime;

  const AlertStatistics({
    required this.total,
    required this.open,
    required this.acknowledged,
    required this.resolved,
    required this.low,
    required this.medium,
    required this.high,
    required this.critical,
    required this.averageResolutionTime,
  });

  factory AlertStatistics.fromJson(Map<String, dynamic> json) => _$AlertStatisticsFromJson(json);
  Map<String, dynamic> toJson() => _$AlertStatisticsToJson(this);

  factory AlertStatistics.empty() {
    return const AlertStatistics(
      total: 0,
      open: 0,
      acknowledged: 0,
      resolved: 0,
      low: 0,
      medium: 0,
      high: 0,
      critical: 0,
      averageResolutionTime: 0.0,
    );
  }

  // Helper getters
  double get resolutionRate => total > 0 ? (resolved / total) * 100 : 0;
  double get criticalPercentage => total > 0 ? (critical / total) * 100 : 0;
  double get highPercentage => total > 0 ? (high / total) * 100 : 0;
  double get mediumPercentage => total > 0 ? (medium / total) * 100 : 0;
  double get lowPercentage => total > 0 ? (low / total) * 100 : 0;
}
