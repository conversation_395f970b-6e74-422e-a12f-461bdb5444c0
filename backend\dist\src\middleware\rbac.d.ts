import { Request, Response, NextFunction } from 'express';
import { UserRole } from '@prisma/client';
export declare const rbac: (allowedRoles: string[]) => (req: Request, res: Response, next: NextFunction) => void;
export declare const checkPermission: (permission: string) => (req: Request, res: Response, next: NextFunction) => void;
export { checkPermission };
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                email: string;
                role: UserRole;
                assignedProperties: string[];
            };
        }
    }
}
