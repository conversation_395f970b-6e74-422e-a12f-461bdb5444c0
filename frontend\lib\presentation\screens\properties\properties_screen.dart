import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_theme.dart';
import '../../../data/models/property.dart';
import '../../providers/properties_providers.dart';
import '../main/main_navigation_screen.dart';

class PropertiesScreen extends ConsumerStatefulWidget {
  const PropertiesScreen({super.key});

  @override
  ConsumerState<PropertiesScreen> createState() => _PropertiesScreenState();
}

class _PropertiesScreenState extends ConsumerState<PropertiesScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  // TODO: Implement filter functionality
  // String _selectedFilter = 'all';

  final List<String> _filterOptions = ['all', 'residential', 'office', 'construction'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Properties',
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          // Tab Bar
          Container(
            color: Theme.of(context).primaryColor,
            child: TabBar(
              controller: _tabController,
              indicatorColor: Colors.white,
              labelColor: Colors.white,
              unselectedLabelColor: Colors.white70,
              tabs: const [
                Tab(text: 'Residential'),
                Tab(text: 'Office'),
                Tab(text: 'Construction'),
              ],
            ),
          ),
          
          // Tab Bar View
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildResidentialProperties(),
                _buildOfficeProperties(),
                _buildConstructionSites(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // TODO: Add new property
          _showAddPropertyDialog();
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildResidentialProperties() {
    return Consumer(
      builder: (context, ref, child) {
        final propertiesAsyncValue = ref.watch(propertiesByTypeProvider('RESIDENTIAL'));

        return propertiesAsyncValue.when(
          data: (properties) => _buildDynamicPropertyList(properties, 'RESIDENTIAL'),
          loading: () => _buildPropertyLoadingState(),
          error: (error, stack) => _buildPropertyErrorState(error, 'RESIDENTIAL'),
        );
      },
    );
  }

  Widget _buildOfficeProperties() {
    return Consumer(
      builder: (context, ref, child) {
        final propertiesAsyncValue = ref.watch(propertiesByTypeProvider('OFFICE'));

        return propertiesAsyncValue.when(
          data: (properties) => _buildDynamicPropertyList(properties, 'OFFICE'),
          loading: () => _buildPropertyLoadingState(),
          error: (error, stack) => _buildPropertyErrorState(error, 'OFFICE'),
        );
      },
    );
  }

  Widget _buildConstructionSites() {
    return Consumer(
      builder: (context, ref, child) {
        final propertiesAsyncValue = ref.watch(propertiesByTypeProvider('CONSTRUCTION'));

        return propertiesAsyncValue.when(
          data: (properties) => _buildDynamicConstructionList(properties),
          loading: () => _buildPropertyLoadingState(),
          error: (error, stack) => _buildPropertyErrorState(error, 'CONSTRUCTION'),
        );
      },
    );
  }

  // TODO: Uncomment when static property list is needed
  // Widget _buildPropertyList(List<Map<String, dynamic>> properties) {
  //   final filteredProperties = properties.where((property) {
  //     if (_searchQuery.isNotEmpty) {
  //       return property['name']
  //           .toString()
  //           .toLowerCase()
  //           .contains(_searchQuery.toLowerCase());
  //     }
  //     return true;
  //   }).toList();

  //   return RefreshIndicator(
  //     onRefresh: () async {
  //       // TODO: Implement refresh
  //       await Future.delayed(const Duration(seconds: 1));
  //     },
  //     child: ListView.builder(
  //       padding: const EdgeInsets.all(16),
  //       itemCount: filteredProperties.length,
  //       itemBuilder: (context, index) {
  //         final property = filteredProperties[index];
  //         return _buildPropertyCard(property);
  //       },
  //     ),
  //   );
  // }

  Widget _buildDynamicPropertyList(List<Property> properties, String propertyType) {
    final filteredProperties = properties.where((property) {
      if (_searchQuery.isNotEmpty) {
        return property.name
            .toLowerCase()
            .contains(_searchQuery.toLowerCase()) ||
            property.address
            .toLowerCase()
            .contains(_searchQuery.toLowerCase());
      }
      return true;
    }).toList();

    if (filteredProperties.isEmpty) {
      return _buildEmptyPropertiesState(propertyType);
    }

    return RefreshIndicator(
      onRefresh: () async {
        // Invalidate the provider to refresh data
        ref.invalidate(propertiesByTypeProvider(propertyType));
        await Future.delayed(const Duration(milliseconds: 500));
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredProperties.length,
        itemBuilder: (context, index) {
          final property = filteredProperties[index];
          return _buildDynamicPropertyCard(property);
        },
      ),
    );
  }

  Widget _buildDynamicConstructionList(List<Property> properties) {
    final filteredProperties = properties.where((property) {
      if (_searchQuery.isNotEmpty) {
        return property.name
            .toLowerCase()
            .contains(_searchQuery.toLowerCase()) ||
            property.address
            .toLowerCase()
            .contains(_searchQuery.toLowerCase());
      }
      return true;
    }).toList();

    if (filteredProperties.isEmpty) {
      return _buildEmptyPropertiesState('CONSTRUCTION');
    }

    return RefreshIndicator(
      onRefresh: () async {
        ref.invalidate(propertiesByTypeProvider('CONSTRUCTION'));
        await Future.delayed(const Duration(milliseconds: 500));
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredProperties.length,
        itemBuilder: (context, index) {
          final property = filteredProperties[index];
          return _buildDynamicConstructionCard(property);
        },
      ),
    );
  }

  Widget _buildPropertyLoadingState() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 3,
      itemBuilder: (context, index) => _buildPropertyCardSkeleton(),
    );
  }

  Widget _buildPropertyErrorState(Object error, String propertyType) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.withValues(alpha: 0.7),
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load ${propertyType.toLowerCase()} properties',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.red,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Please check your connection and try again',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                ref.invalidate(propertiesByTypeProvider(propertyType));
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyPropertiesState(String propertyType) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _getPropertyTypeIcon(propertyType),
              size: 64,
              color: Colors.grey.withValues(alpha: 0.7),
            ),
            const SizedBox(height: 16),
            Text(
              'No ${propertyType.toLowerCase()} properties found',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              _searchQuery.isNotEmpty
                  ? 'Try adjusting your search criteria'
                  : 'Properties will appear here when added',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  IconData _getPropertyTypeIcon(String type) {
    switch (type.toUpperCase()) {
      case 'RESIDENTIAL':
        return Icons.home;
      case 'OFFICE':
        return Icons.business;
      case 'CONSTRUCTION':
        return Icons.construction;
      default:
        return Icons.business;
    }
  }

  // TODO: Uncomment when static property cards are needed
  // Widget _buildPropertyCard(Map<String, dynamic> property) {
  //   Color statusColor;
  //   switch (property['status']) {
  //     case 'operational':
  //       statusColor = AppTheme.successColor;
  //       break;
  //     case 'warning':
  //       statusColor = AppTheme.warningColor;
  //       break;
  //     case 'critical':
  //       statusColor = AppTheme.errorColor;
  //       break;
  //     default:
  //       statusColor = Colors.grey;
  //   }

  //   return Card(
  //     margin: const EdgeInsets.only(bottom: 16),
  //     elevation: 4,
  //     shape: RoundedRectangleBorder(
  //       borderRadius: BorderRadius.circular(12),
  //     ),
  //     child: InkWell(
  //       borderRadius: BorderRadius.circular(12),
  //       onTap: () {
  //         context.go('/properties/${property['id']}');
  //       },
  //       child: Column(
  //         crossAxisAlignment: CrossAxisAlignment.start,
  //         children: [
  //           // Property Image
  //           Container(
  //             height: 150,
  //             decoration: BoxDecoration(
  //               borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
  //               gradient: LinearGradient(
  //                 begin: Alignment.topLeft,
  //                 end: Alignment.bottomRight,
  //                 colors: [
  //                   Theme.of(context).primaryColor.withValues(alpha: 0.8),
  //                   Theme.of(context).primaryColor,
  //                 ],
  //               ),
  //             ),
  //             child: Stack(
  //               children: [
  //                 // Placeholder for property image
  //                 Center(
  //                   child: Icon(
  //                     Icons.business,
  //                     size: 60,
  //                     color: Colors.white.withValues(alpha: 0.7),
  //                   ),
  //                 ),

  //                 // Status Badge
  //                 Positioned(
  //                   top: 12,
  //                   right: 12,
  //                   child: Container(
  //                     padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
  //                     decoration: BoxDecoration(
  //                       color: statusColor,
  //                       borderRadius: BorderRadius.circular(12),
  //                     ),
  //                     child: Text(
  //                       property['status'].toString().toUpperCase(),
  //                       style: const TextStyle(
  //                         color: Colors.white,
  //                         fontSize: 10,
  //                         fontWeight: FontWeight.bold,
  //                       ),
  //                     ),
  //                   ),
  //                 ),
  //               ],
  //             ),
  //           ),

  //           // Property Details
  //           Padding(
  //             padding: const EdgeInsets.all(16),
  //             child: Column(
  //               crossAxisAlignment: CrossAxisAlignment.start,
  //               children: [
  //                 // Property Name
  //                 Text(
  //                   property['name'],
  //                   style: Theme.of(context).textTheme.titleLarge?.copyWith(
  //                     fontWeight: FontWeight.w600,
  //                   ),
  //                 ),

  //                 const SizedBox(height: 4),

  //                 // Address
  //                 Text(
  //                   property['address'],
  //                   style: Theme.of(context).textTheme.bodyMedium?.copyWith(
  //                     color: Colors.grey[600],
  //                   ),
  //                 ),

  //                 const SizedBox(height: 12),

  //                 // Systems and Last Updated
  //                 Row(
  //                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //                   children: [
  //                     Row(
  //                       children: [
  //                         Icon(
  //                           Icons.settings,
  //                           size: 16,
  //                           color: Colors.grey[600],
  //                         ),
  //                         const SizedBox(width: 4),
  //                         Text(
  //                           '${property['systems']} systems',
  //                           style: Theme.of(context).textTheme.bodySmall,
  //                         ),
  //                       ],
  //                     ),
  //                     Text(
  //                       'Updated ${property['lastUpdated']}',
  //                       style: Theme.of(context).textTheme.bodySmall?.copyWith(
  //                         color: Colors.grey[500],
  //                       ),
  //                     ),
  //                   ],
  //                 ),
  //               ],
  //             ),
  //           ),
  //         ],
  //       ),
  //     ),
  //   );
  // }

  Widget _buildDynamicPropertyCard(Property property) {
    final statusColor = _getPropertyStatusColor(property);
    final statusText = _getPropertyStatusText(property);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          context.go('/properties/${property.id}');
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Property Image
            Container(
              height: 150,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Theme.of(context).primaryColor.withValues(alpha: 0.8),
                    Theme.of(context).primaryColor,
                  ],
                ),
              ),
              child: Stack(
                children: [
                  // Property type icon
                  Center(
                    child: Icon(
                      _getPropertyTypeIcon(property.type),
                      size: 60,
                      color: Colors.white.withValues(alpha: 0.7),
                    ),
                  ),

                  // Status Badge
                  Positioned(
                    top: 12,
                    right: 12,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: statusColor,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        statusText,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),

                  // Property Type Badge
                  Positioned(
                    top: 12,
                    left: 12,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.5),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        property.type,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Property Details
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Property Name
                  Text(
                    property.name,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),

                  const SizedBox(height: 4),

                  // Address
                  Text(
                    property.address,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),

                  if (property.description?.isNotEmpty == true) ...[
                    const SizedBox(height: 8),
                    Text(
                      property.description!,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[500],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],

                  const SizedBox(height: 12),

                  // Status and Last Updated
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Icon(
                            property.isActive ? Icons.check_circle : Icons.pause_circle,
                            size: 16,
                            color: property.isActive ? AppTheme.successColor : Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            property.isActive ? 'Active' : 'Inactive',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: property.isActive ? AppTheme.successColor : Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                      Text(
                        _formatDateTime(property.updatedAt.toString()),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // TODO: Uncomment when static construction list is needed
  // Widget _buildConstructionList(List<Map<String, dynamic>> sites) {
  //   return RefreshIndicator(
  //     onRefresh: () async {
  //       await Future.delayed(const Duration(seconds: 1));
  //     },
  //     child: ListView.builder(
  //       padding: const EdgeInsets.all(16),
  //       itemCount: sites.length,
  //       itemBuilder: (context, index) {
  //         final site = sites[index];
  //         return _buildConstructionCard(site);
  //       },
  //     ),
  //   );
  // }

  Widget _buildDynamicConstructionCard(Property property) {
    // final statusColor = _getPropertyStatusColor(property);
    final statusText = _getPropertyStatusText(property);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          context.go('/properties/${property.id}');
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Site Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          property.name,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          property.address,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  StatusIndicator(status: statusText.toLowerCase(), showLabel: true),
                ],
              ),

              if (property.description?.isNotEmpty == true) ...[
                const SizedBox(height: 12),
                Text(
                  property.description!,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[700],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],

              const SizedBox(height: 16),

              // Status and Last Updated
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.construction,
                        size: 16,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Construction Site',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                  Text(
                    'Updated ${_formatDateTime(property.updatedAt.toString())}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPropertyCardSkeleton() {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image skeleton
          Container(
            height: 150,
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.3),
              borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            ),
          ),

          // Content skeleton
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 20,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  height: 16,
                  width: 200,
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      height: 14,
                      width: 80,
                      decoration: BoxDecoration(
                        color: Colors.grey.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    Container(
                      height: 14,
                      width: 100,
                      decoration: BoxDecoration(
                        color: Colors.grey.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getPropertyStatusColor(Property property) {
    if (!property.isActive) {
      return Colors.grey;
    }
    // For now, return success color for active properties
    // This can be enhanced with actual system health data
    return AppTheme.successColor;
  }

  String _getPropertyStatusText(Property property) {
    if (!property.isActive) {
      return 'INACTIVE';
    }
    // For now, return operational for active properties
    // This can be enhanced with actual system health data
    return 'OPERATIONAL';
  }

  String _formatDateTime(String dateTimeString) {
    try {
      final dateTime = DateTime.parse(dateTimeString);
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inMinutes < 60) {
        return '${difference.inMinutes}m ago';
      } else if (difference.inHours < 24) {
        return '${difference.inHours}h ago';
      } else if (difference.inDays < 7) {
        return '${difference.inDays}d ago';
      } else {
        return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
      }
    } catch (e) {
      return 'Recently';
    }
  }

  // TODO: Uncomment when static construction cards are needed
  // Widget _buildConstructionCard(Map<String, dynamic> site) {
  //   Color statusColor;
  //   switch (site['status']) {
  //     case 'operational':
  //       statusColor = AppTheme.successColor;
  //       break;
  //     case 'warning':
  //       statusColor = AppTheme.warningColor;
  //       break;
  //     case 'critical':
  //       statusColor = AppTheme.errorColor;
  //       break;
  //     default:
  //       statusColor = Colors.grey;
  //   }

  //   return Card(
  //     margin: const EdgeInsets.only(bottom: 16),
  //     child: InkWell(
  //       onTap: () {
  //         // TODO: Navigate to construction site detail
  //       },
  //       child: Padding(
  //         padding: const EdgeInsets.all(16),
  //         child: Column(
  //           crossAxisAlignment: CrossAxisAlignment.start,
  //           children: [
  //             // Site Header
  //             Row(
  //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //               children: [
  //                 Expanded(
  //                   child: Column(
  //                     crossAxisAlignment: CrossAxisAlignment.start,
  //                     children: [
  //                       Text(
  //                         site['name'],
  //                         style: Theme.of(context).textTheme.titleMedium?.copyWith(
  //                           fontWeight: FontWeight.w600,
  //                         ),
  //                       ),
  //                       Text(
  //                         site['address'],
  //                         style: Theme.of(context).textTheme.bodySmall?.copyWith(
  //                           color: Colors.grey[600],
  //                         ),
  //                       ),
  //                     ],
  //                   ),
  //                 ),
  //                 StatusIndicator(status: site['status'], showLabel: true),
  //               ],
  //             ),

  //             const SizedBox(height: 16),

  //             // Progress Bar
  //             Column(
  //               crossAxisAlignment: CrossAxisAlignment.start,
  //               children: [
  //                 Row(
  //                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //                   children: [
  //                     Text(
  //                       'Progress',
  //                       style: Theme.of(context).textTheme.bodyMedium,
  //                     ),
  //                     Text(
  //                       '${site['progress']}%',
  //                       style: Theme.of(context).textTheme.bodyMedium?.copyWith(
  //                         fontWeight: FontWeight.w600,
  //                       ),
  //                     ),
  //                   ],
  //                 ),
  //                 const SizedBox(height: 4),
  //                 LinearProgressIndicator(
  //                   value: site['progress'] / 100,
  //                   backgroundColor: Colors.grey[300],
  //                   valueColor: AlwaysStoppedAnimation<Color>(statusColor),
  //                 ),
  //               ],
  //             ),

  //             const SizedBox(height: 12),

  //             // Workers and Last Updated
  //             Row(
  //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //               children: [
  //                 Row(
  //                   children: [
  //                     Icon(
  //                       Icons.people,
  //                       size: 16,
  //                       color: Colors.grey[600],
  //                     ),
  //                     const SizedBox(width: 4),
  //                     Text(
  //                       '${site['workers']} workers',
  //                       style: Theme.of(context).textTheme.bodySmall,
  //                     ),
  //                   ],
  //                 ),
  //                 Text(
  //                   'Updated ${site['lastUpdated']}',
  //                   style: Theme.of(context).textTheme.bodySmall?.copyWith(
  //                     color: Colors.grey[500],
  //                   ),
  //                 ),
  //               ],
  //             ),
  //           ],
  //         ),
  //       ),
  //     ),
  //   );
  // }

  void _showSearchDialog() {
    final currentQuery = ref.read(searchQueryProvider);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Properties'),
        content: TextField(
          autofocus: true,
          controller: TextEditingController(text: currentQuery),
          decoration: const InputDecoration(
            hintText: 'Enter property name or address...',
            prefixIcon: Icon(Icons.search),
          ),
          onChanged: (value) {
            ref.read(searchQueryProvider.notifier).state = value;
            setState(() {
              _searchQuery = value;
            });
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              ref.read(searchQueryProvider.notifier).state = '';
              setState(() {
                _searchQuery = '';
              });
              Navigator.of(context).pop();
            },
            child: const Text('Clear'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    final currentFilter = ref.read(selectedFilterProvider);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Properties'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: _filterOptions.map((option) {
            return RadioListTile<String>(
              title: Text(option.toUpperCase()),
              value: option,
              groupValue: currentFilter,
              onChanged: (value) {
                ref.read(selectedFilterProvider.notifier).state = value!;
                // setState(() {
                //   _selectedFilter = value;
                // });
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showAddPropertyDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add New Property'),
        content: const Text('This feature will be available soon.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
