{"valid_import": true, "imports": [{"uri": "package:flutter_riverpod/flutter_riverpod.dart", "transitive": false}, {"uri": "package:frontend/data/models/alert.dart", "transitive": false}, {"uri": "package:frontend/data/repositories/alert_repository.dart", "transitive": false}, {"uri": "package:frontend/core/services/service_locator.dart", "transitive": false}, {"uri": "package:frontend/core/providers/cache_provider.dart", "transitive": false}, {"uri": "package:frontend/core/providers/connectivity_provider.dart", "transitive": false}], "elements": []}