import 'package:flutter/material.dart';
import 'core/services/auth_service.dart';
import 'core/services/api_client.dart';

class TestLoginScreen extends StatefulWidget {
  const TestLoginScreen({super.key});

  @override
  State<TestLoginScreen> createState() => _TestLoginScreenState();
}

class _TestLoginScreenState extends State<TestLoginScreen> {
  final _emailController = TextEditingController(text: '<EMAIL>');
  final _passwordController = TextEditingController(text: 'admin123');
  final _authService = AuthService();
  bool _isLoading = false;
  String _result = '';

  @override
  void initState() {
    super.initState();
    // Initialize API client
    _initializeApiClient();
  }

  Future<void> _initializeApiClient() async {
    await ApiClient().initialize();
  }

  Future<void> _testConnection() async {
    setState(() {
      _isLoading = true;
      _result = 'Testing connection...';
    });

    try {
      final response = await _authService.testConnection();
      setState(() {
        _result = 'Connection test: ${response.success ? 'SUCCESS' : 'FAILED'}\n'
            'Message: ${response.message}\n'
            'Error: ${response.error ?? 'None'}';
      });
    } catch (e) {
      setState(() {
        _result = 'Connection test failed: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testLogin() async {
    setState(() {
      _isLoading = true;
      _result = 'Attempting login...';
    });

    try {
      final response = await _authService.login(
        email: _emailController.text,
        password: _passwordController.text,
      );

      setState(() {
        if (response.success) {
          _result = 'LOGIN SUCCESS!\n'
              'User: ${response.data?.user.name}\n'
              'Role: ${response.data?.user.role}\n'
              'Token: ${response.data?.token.accessToken.substring(0, 20)}...';
        } else {
          _result = 'LOGIN FAILED\n'
              'Error: ${response.error}\n'
              'Message: ${response.message}';
        }
      });
    } catch (e) {
      setState(() {
        _result = 'Login exception: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Login Test'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            TextField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'Email',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _passwordController,
              decoration: const InputDecoration(
                labelText: 'Password',
                border: OutlineInputBorder(),
              ),
              obscureText: true,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _isLoading ? null : _testConnection,
              child: const Text('Test Connection'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isLoading ? null : _testLogin,
              child: const Text('Test Login'),
            ),
            const SizedBox(height: 24),
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: SingleChildScrollView(
                    child: Text(
                      _result.isEmpty ? 'Results will appear here...' : _result,
                      style: const TextStyle(fontFamily: 'monospace'),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }
}
