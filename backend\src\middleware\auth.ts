import { Request, Response, NextFunction } from 'express';
import { UserRole } from '@prisma/client';
import { AuthService } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { JWTPayload } from '@/types';

// Extend Express Request type to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        role: UserRole;
        assignedProperties: string[];
      };
    }
  }
}

export interface AuthenticatedRequest extends Request {
  user: {
    id: string;
    email: string;
    role: UserRole;
    assignedProperties: string[];
  };
}

export const authenticate = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        success: false,
        error: 'UNAUTHORIZED',
        message: 'Authentication required',
        timestamp: new Date().toISOString(),
        path: req.path,
      });
      return;
    }

    const token = authHeader.substring(7);
    
    try {
      const payload = AuthService.verifyAccessToken(token) as JWTPayload;
      
      // Verify user still exists and is active
      const user = await prisma.user.findFirst({
        where: {
          id: payload.userId,
          isActive: true,
        },
        include: {
          assignedProperties: {
            select: {
              propertyId: true,
            },
          },
        },
      });

      if (!user) {
        res.status(401).json({
          success: false,
          error: 'UNAUTHORIZED',
          message: 'Invalid or expired token',
          timestamp: new Date().toISOString(),
          path: req.path,
        });
        return;
      }

      // Attach user to request
      req.user = {
        id: user.id,
        email: user.email,
        role: user.role,
        assignedProperties: user.assignedProperties.map(ap => ap.propertyId),
      };

      next();
    } catch (jwtError) {
      res.status(401).json({
        success: false,
        error: 'UNAUTHORIZED',
        message: 'Invalid or expired token',
        timestamp: new Date().toISOString(),
        path: req.path,
      });
      return;
    }
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(500).json({
      success: false,
      error: 'INTERNAL_SERVER_ERROR',
      message: 'Authentication service error',
      timestamp: new Date().toISOString(),
      path: req.path,
    });
  }
};

export const authorize = (
  requiredRoles: UserRole[] = [],
  requiredPermission?: keyof import('@/types').UserPermissions
) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const user = req.user;
    
    if (!user) {
      res.status(401).json({
        success: false,
        error: 'UNAUTHORIZED',
        message: 'Authentication required',
        timestamp: new Date().toISOString(),
        path: req.path,
      });
      return;
    }

    // Check role-based access
    if (requiredRoles.length > 0 && !requiredRoles.includes(user.role)) {
      res.status(403).json({
        success: false,
        error: 'FORBIDDEN',
        message: 'Insufficient permissions for this operation',
        timestamp: new Date().toISOString(),
        path: req.path,
      });
      return;
    }

    // Check permission-based access
    if (requiredPermission) {
      const hasPermission = AuthService.hasPermission(user.role, requiredPermission);
      if (!hasPermission) {
        res.status(403).json({
          success: false,
          error: 'FORBIDDEN',
          message: 'Insufficient permissions for this operation',
          timestamp: new Date().toISOString(),
          path: req.path,
        });
        return;
      }
    }

    next();
  };
};

export const requirePropertyAccess = (propertyIdParam: string = 'propertyId') => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const user = req.user;
    const propertyId = req.params[propertyIdParam];
    
    if (!user) {
      res.status(401).json({
        success: false,
        error: 'UNAUTHORIZED',
        message: 'Authentication required',
        timestamp: new Date().toISOString(),
        path: req.path,
      });
      return;
    }

    // Super admins have access to all properties
    if (user.role === UserRole.SUPER_ADMIN) {
      next();
      return;
    }

    // Check if user has access to the specific property
    if (!user.assignedProperties.includes(propertyId)) {
      res.status(403).json({
        success: false,
        error: 'FORBIDDEN',
        message: 'Access denied to this property',
        timestamp: new Date().toISOString(),
        path: req.path,
      });
      return;
    }

    next();
  };
};

export const requireAction = (action: 'create' | 'read' | 'update' | 'delete' | 'export' | 'import') => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const user = req.user;
    
    if (!user) {
      res.status(401).json({
        success: false,
        error: 'UNAUTHORIZED',
        message: 'Authentication required',
        timestamp: new Date().toISOString(),
        path: req.path,
      });
      return;
    }

    const canPerformAction = AuthService.canPerformAction(user.role, action);
    if (!canPerformAction) {
      res.status(403).json({
        success: false,
        error: 'FORBIDDEN',
        message: `Insufficient permissions to ${action} this resource`,
        timestamp: new Date().toISOString(),
        path: req.path,
      });
      return;
    }

    next();
  };
};

export const optionalAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    next();
    return;
  }

  try {
    const token = authHeader.substring(7);
    const payload = AuthService.verifyAccessToken(token) as JWTPayload;
    
    const user = await prisma.user.findFirst({
      where: {
        id: payload.userId,
        isActive: true,
      },
      include: {
        assignedProperties: {
          select: {
            propertyId: true,
          },
        },
      },
    });

    if (user) {
      req.user = {
        id: user.id,
        email: user.email,
        role: user.role,
        assignedProperties: user.assignedProperties.map(ap => ap.propertyId),
      };
    }
  } catch (error) {
    // Ignore authentication errors for optional auth
    console.warn('Optional authentication failed:', error);
  }

  next();
};

// Alias for backward compatibility
export const authenticateToken = authenticate;
