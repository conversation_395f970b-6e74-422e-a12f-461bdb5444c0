import { Request, Response, NextFunction } from 'express';
import { UserRole } from '@prisma/client';
import { AuthService } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { JWTPayload } from '@/types';

// Permission context interface
export interface PermissionContext {
  userId: string;
  role: UserRole;
  propertyId?: string;
  officeId?: string;
  resourceId?: string;
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
}

// Extend Express Request type to include user and permission context
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        role: UserRole;
        assignedProperties: string[];
      };
      permissionContext?: PermissionContext;
      permissionResult?: any;
      filteredQuery?: any;
      uiPermissions?: any;
    }
  }
}

export interface AuthenticatedRequest extends Request {
  user: {
    id: string;
    email: string;
    role: UserRole;
    assignedProperties: string[];
  };
}

export const authenticate = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        success: false,
        error: 'UNAUTHORIZED',
        message: 'Authentication required',
        timestamp: new Date().toISOString(),
        path: req.path,
      });
      return;
    }

    const token = authHeader.substring(7);
    
    try {
      const payload = AuthService.verifyAccessToken(token) as JWTPayload;
      
      // Verify user still exists and is active
      const user = await prisma.user.findFirst({
        where: {
          id: payload.userId,
          isActive: true,
        },
        include: {
          assignedProperties: {
            select: {
              propertyId: true,
            },
          },
        },
      });

      if (!user) {
        res.status(401).json({
          success: false,
          error: 'UNAUTHORIZED',
          message: 'Invalid or expired token',
          timestamp: new Date().toISOString(),
          path: req.path,
        });
        return;
      }

      // Attach user to request
      req.user = {
        id: user.id,
        email: user.email,
        role: user.role,
        assignedProperties: user.assignedProperties.map(ap => ap.propertyId),
      };

      next();
    } catch (jwtError) {
      res.status(401).json({
        success: false,
        error: 'UNAUTHORIZED',
        message: 'Invalid or expired token',
        timestamp: new Date().toISOString(),
        path: req.path,
      });
      return;
    }
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(500).json({
      success: false,
      error: 'INTERNAL_SERVER_ERROR',
      message: 'Authentication service error',
      timestamp: new Date().toISOString(),
      path: req.path,
    });
  }
};

export const authorize = (
  requiredRoles: UserRole[] = [],
  requiredPermission?: keyof import('@/types').UserPermissions
) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const user = req.user;
    
    if (!user) {
      res.status(401).json({
        success: false,
        error: 'UNAUTHORIZED',
        message: 'Authentication required',
        timestamp: new Date().toISOString(),
        path: req.path,
      });
      return;
    }

    // Check role-based access
    if (requiredRoles.length > 0 && !requiredRoles.includes(user.role)) {
      res.status(403).json({
        success: false,
        error: 'FORBIDDEN',
        message: 'Insufficient permissions for this operation',
        timestamp: new Date().toISOString(),
        path: req.path,
      });
      return;
    }

    // Check permission-based access
    if (requiredPermission) {
      const hasPermission = AuthService.hasPermission(user.role, requiredPermission);
      if (!hasPermission) {
        res.status(403).json({
          success: false,
          error: 'FORBIDDEN',
          message: 'Insufficient permissions for this operation',
          timestamp: new Date().toISOString(),
          path: req.path,
        });
        return;
      }
    }

    next();
  };
};

export const requirePropertyAccess = (propertyIdParam: string = 'propertyId') => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const user = req.user;
    const propertyId = req.params[propertyIdParam];
    
    if (!user) {
      res.status(401).json({
        success: false,
        error: 'UNAUTHORIZED',
        message: 'Authentication required',
        timestamp: new Date().toISOString(),
        path: req.path,
      });
      return;
    }

    // Super admins have access to all properties
    if (user.role === UserRole.SUPER_ADMIN) {
      next();
      return;
    }

    // Check if user has access to the specific property
    if (!user.assignedProperties.includes(propertyId)) {
      res.status(403).json({
        success: false,
        error: 'FORBIDDEN',
        message: 'Access denied to this property',
        timestamp: new Date().toISOString(),
        path: req.path,
      });
      return;
    }

    next();
  };
};

export const requireAction = (action: 'create' | 'read' | 'update' | 'delete' | 'export' | 'import') => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const user = req.user;
    
    if (!user) {
      res.status(401).json({
        success: false,
        error: 'UNAUTHORIZED',
        message: 'Authentication required',
        timestamp: new Date().toISOString(),
        path: req.path,
      });
      return;
    }

    const canPerformAction = AuthService.canPerformAction(user.role, action);
    if (!canPerformAction) {
      res.status(403).json({
        success: false,
        error: 'FORBIDDEN',
        message: `Insufficient permissions to ${action} this resource`,
        timestamp: new Date().toISOString(),
        path: req.path,
      });
      return;
    }

    next();
  };
};

export const optionalAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    next();
    return;
  }

  try {
    const token = authHeader.substring(7);
    const payload = AuthService.verifyAccessToken(token) as JWTPayload;
    
    const user = await prisma.user.findFirst({
      where: {
        id: payload.userId,
        isActive: true,
      },
      include: {
        assignedProperties: {
          select: {
            propertyId: true,
          },
        },
      },
    });

    if (user) {
      req.user = {
        id: user.id,
        email: user.email,
        role: user.role,
        assignedProperties: user.assignedProperties.map(ap => ap.propertyId),
      };
    }
  } catch (error) {
    // Ignore authentication errors for optional auth
    console.warn('Optional authentication failed:', error);
  }

  next();
};

// Enhanced authorization with RBAC/UBAC
export const enhancedAuthorize = (
  resource: string,
  action: string,
  options: {
    requirePropertyAccess?: boolean;
    requireOfficeAccess?: boolean;
    customConditions?: Record<string, any>;
  } = {}
) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const user = req.user;

      if (!user) {
        res.status(401).json({
          success: false,
          error: 'UNAUTHORIZED',
          message: 'Authentication required',
          timestamp: new Date().toISOString(),
          path: req.path,
        });
        return;
      }

      // Build permission context
      const context: PermissionContext = {
        userId: user.id,
        role: user.role,
        propertyId: req.params.propertyId || req.body.propertyId,
        officeId: req.params.officeId || req.body.officeId,
        resourceId: req.params.id || req.params.resourceId,
        timestamp: new Date(),
        ipAddress: req.ip || req.connection.remoteAddress,
        userAgent: req.get('User-Agent')
      };

      // Simple permission check based on role and action
      const hasPermission = checkResourcePermission(user.role, resource, action);

      if (!hasPermission) {
        res.status(403).json({
          success: false,
          error: 'FORBIDDEN',
          message: `Access denied for ${action} on ${resource}`,
          timestamp: new Date().toISOString(),
          path: req.path,
        });
        return;
      }

      // Additional property access check
      if (options.requirePropertyAccess && context.propertyId) {
        if (user.role !== UserRole.SUPER_ADMIN &&
            !user.assignedProperties.includes(context.propertyId)) {
          res.status(403).json({
            success: false,
            error: 'FORBIDDEN',
            message: 'Access denied to this property',
            timestamp: new Date().toISOString(),
            path: req.path,
          });
          return;
        }
      }

      // Store permission context for use in route handlers
      req.permissionContext = context;

      next();
    } catch (error) {
      console.error('Enhanced authorization error:', error);
      res.status(500).json({
        success: false,
        error: 'INTERNAL_SERVER_ERROR',
        message: 'Authorization service error',
        timestamp: new Date().toISOString(),
        path: req.path,
      });
    }
  };
};

// Simple permission checker
function checkResourcePermission(role: UserRole, resource: string, action: string): boolean {
  const permissions: Record<UserRole, string[]> = {
    [UserRole.SUPER_ADMIN]: ['*'], // All permissions
    [UserRole.PROPERTY_MANAGER]: [
      'employees.*', 'maintenance.*', 'systems.*', 'dashboard.*', 'alerts.*', 'properties.*'
    ],
    [UserRole.OFFICE_MANAGER]: [
      'employees.view', 'employees.create', 'employees.update', 'employees.attendance.*', 'dashboard.view'
    ],
    [UserRole.SECURITY_PERSONNEL]: [
      'systems.view', 'security.*', 'alerts.*', 'dashboard.view'
    ],
    [UserRole.MAINTENANCE_STAFF]: [
      'maintenance.*', 'systems.view', 'dashboard.view'
    ],
    [UserRole.CONSTRUCTION_SUPERVISOR]: [
      'employees.view', 'employees.attendance.view', 'systems.view', 'dashboard.view'
    ],
  };

  const userPermissions = permissions[role] || [];
  const permissionKey = `${resource}.${action}`;

  return userPermissions.includes('*') ||
         userPermissions.includes(permissionKey) ||
         userPermissions.includes(`${resource}.*`);
}

// Permission check function for use in routes
export const checkPermission = (permission: string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const user = req.user;

      if (!user) {
        res.status(401).json({
          success: false,
          error: 'UNAUTHORIZED',
          message: 'Authentication required',
          timestamp: new Date().toISOString(),
          path: req.path,
        });
        return;
      }

      const [resource, action] = permission.split('.');
      const hasPermission = checkResourcePermission(user.role, resource, action);

      if (!hasPermission) {
        res.status(403).json({
          success: false,
          error: 'FORBIDDEN',
          message: `Permission denied: ${permission}`,
          timestamp: new Date().toISOString(),
          path: req.path,
        });
        return;
      }

      next();
    } catch (error) {
      console.error('Permission check error:', error);
      res.status(500).json({
        success: false,
        error: 'INTERNAL_SERVER_ERROR',
        message: 'Permission service error',
        timestamp: new Date().toISOString(),
        path: req.path,
      });
    }
  };
};

// Alias for backward compatibility
export const authenticateToken = authenticate;
