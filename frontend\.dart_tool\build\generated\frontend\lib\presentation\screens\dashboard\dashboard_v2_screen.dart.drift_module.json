{"elements": {}, "imports": ["package:flutter/material.dart", "package:flutter_riverpod/flutter_riverpod.dart", "package:go_router/go_router.dart", "package:frontend/core/theme/app_theme.dart", "package:frontend/core/services/service_locator.dart", "package:frontend/presentation/routes/app_router.dart", "package:frontend/presentation/providers/dashboard_providers.dart", "package:frontend/presentation/screens/dashboard/widgets/dashboard_header.dart", "package:frontend/presentation/screens/dashboard/widgets/system_status_card.dart", "package:frontend/presentation/screens/dashboard/widgets/property_grid.dart", "package:frontend/presentation/screens/dashboard/widgets/alerts_feed.dart", "package:frontend/presentation/screens/dashboard/widgets/system_health_chart.dart", "package:frontend/presentation/screens/dashboard/widgets/quick_actions.dart", "package:frontend/presentation/screens/dashboard/widgets/performance_metrics.dart"]}