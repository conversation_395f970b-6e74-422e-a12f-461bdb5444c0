import { Request, Response } from 'express';
export declare const getEmployees: (req: Request, res: Response) => Promise<void>;
export declare const getEmployee: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const createEmployee: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const updateEmployee: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const deleteEmployee: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const getEmployeeStatistics: (req: Request, res: Response) => Promise<void>;
export declare const getEmployeeAttendance: (req: Request, res: Response) => Promise<void>;
export declare const submitAttendance: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
