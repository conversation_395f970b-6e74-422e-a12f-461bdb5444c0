import '../models/maintenance.dart';
import '../models/api_response.dart';
import '../../core/services/api_client.dart';
import '../../core/services/service_locator.dart';
import '../../core/constants/api_constants.dart';

class MaintenanceRepository {
  final ApiClient _apiClient = serviceLocator.apiClient;

  /// Get maintenance issues with filtering and pagination
  Future<ApiResponse<List<MaintenanceIssue>>> getMaintenanceIssues({
    String? propertyId,
    String? status,
    String? priority,
    String? department,
    int page = 1,
    int limit = 20,
    String? assignedTo,
    String? search,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (propertyId != null) queryParams['propertyId'] = propertyId;
      if (status != null) queryParams['status'] = status;
      if (priority != null) queryParams['priority'] = priority;
      if (department != null) queryParams['department'] = department;
      if (assignedTo != null) queryParams['assignedTo'] = assignedTo;
      if (search != null && search.isNotEmpty) queryParams['search'] = search;

      final response = await _apiClient.get(
        ApiConstants.maintenanceIssues,
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        final issues = data.map((json) => MaintenanceIssue.fromJson(json)).toList();
        return ApiResponse.success(data: issues);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch maintenance issues: $e');
    }
  }

  /// Get maintenance issue details by ID
  Future<ApiResponse<MaintenanceIssue>> getMaintenanceIssueDetail(String issueId) async {
    try {
      final endpoint = ApiConstants.maintenanceIssue.replaceAll('{id}', issueId);
      final response = await _apiClient.get(endpoint);

      if (response.isSuccess) {
        final issue = MaintenanceIssue.fromJson(response.data['data']);
        return ApiResponse.success(data: issue);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch maintenance issue: $e');
    }
  }

  /// Get maintenance departments
  Future<ApiResponse<List<Map<String, dynamic>>>> getMaintenanceDepartments() async {
    try {
      final response = await _apiClient.get(ApiConstants.maintenanceDepartments);

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data: data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch maintenance departments: $e');
    }
  }

  /// Get maintenance functions
  Future<ApiResponse<List<Map<String, dynamic>>>> getMaintenanceFunctions({
    String? departmentId,
    String? propertyId,
    String? search,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (departmentId != null) queryParams['departmentId'] = departmentId;
      if (propertyId != null) queryParams['propertyId'] = propertyId;
      if (search != null && search.isNotEmpty) queryParams['search'] = search;

      final response = await _apiClient.get(
        ApiConstants.maintenanceFunctions,
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data: data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch maintenance functions: $e');
    }
  }

  /// Create a new maintenance issue
  Future<ApiResponse<MaintenanceIssue>> createMaintenanceIssue({
    required String propertyId,
    required String title,
    required String description,
    required String priority,
    required String department,
    String? assignedTo,
    List<String>? attachments,
    String? recurrence,
  }) async {
    try {
      final requestBody = {
        'propertyId': propertyId,
        'title': title,
        'description': description,
        'priority': priority,
        'department': department,
        if (assignedTo != null) 'assignedTo': assignedTo,
        if (attachments != null) 'attachments': attachments,
        if (recurrence != null) 'recurrence': recurrence,
      };

      final response = await _apiClient.post(
        ApiConstants.maintenanceIssues,
        data: requestBody,
      );

      if (response.isSuccess) {
        final issue = MaintenanceIssue.fromJson(response.data['data']);
        return ApiResponse.success(data: issue);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to create maintenance issue: $e');
    }
  }

  /// Get maintenance statistics
  Future<ApiResponse<Map<String, dynamic>>> getMaintenanceStatistics({
    String? propertyId,
    String? dateFrom,
    String? dateTo,
  }) async {
    try {
      final queryParams = <String, dynamic>{};

      if (propertyId != null) queryParams['propertyId'] = propertyId;
      if (dateFrom != null) queryParams['dateFrom'] = dateFrom;
      if (dateTo != null) queryParams['dateTo'] = dateTo;

      final response = await _apiClient.get(
        ApiConstants.maintenanceStats,
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data['data']);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch maintenance statistics: $e');
    }
  }
}
