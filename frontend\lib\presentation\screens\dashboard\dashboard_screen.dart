import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../../core/theme/app_theme.dart';
import '../../../core/services/service_locator.dart';
import '../../../data/models/dashboard.dart';
import '../../../data/models/alert.dart';
import '../../routes/app_router.dart';
import '../main/main_navigation_screen.dart';
import '../../providers/dashboard_providers.dart';
import 'dashboard_v2_screen.dart';

class DashboardScreen extends ConsumerStatefulWidget {
  const DashboardScreen({super.key});

  @override
  ConsumerState<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends ConsumerState<DashboardScreen> {
  @override
  Widget build(BuildContext context) {
    final isV2 = ref.watch(dashboardVersionProvider);

    // Debug information
    print('Dashboard build - isV2: $isV2');

    // If V2 is enabled, show the new dashboard
    if (isV2) {
      print('Showing Dashboard V2');
      return const DashboardV2Screen();
    }

    print('Showing Dashboard V1 with toggle button');

    // Otherwise show the original dashboard with toggle
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Dashboard',
        actions: [
          // Version Toggle - Enhanced visibility
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: ElevatedButton.icon(
              onPressed: () {
                final isAuthenticated = serviceLocator.authService.isAuthenticated;
                print('Dashboard V2 toggle pressed! Auth: $isAuthenticated'); // Debug log
                if (isAuthenticated) {
                  ref.read(dashboardVersionProvider.notifier).toggleVersion();
                } else {
                  context.go(AppRoutes.login);
                }
              },
              icon: Icon(
                serviceLocator.authService.isAuthenticated ? Icons.arrow_back : Icons.lock_outline,
                size: 18,
              ),
              label: Text(
                serviceLocator.authService.isAuthenticated ? 'Old' : 'Login',
                style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
              ),
              style: ElevatedButton.styleFrom(
                foregroundColor: Colors.grey[600],
                backgroundColor: Colors.grey[100],
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                minimumSize: const Size(60, 36),
                elevation: 1,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: null, // TODO: Implement notifications
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _handleRefresh,
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _handleRefresh,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // V2 Promotion Banner
              _buildV2PromotionBanner(),

              const SizedBox(height: 16),

              // System Status Overview
              _buildSystemStatusOverview(),
              
              const SizedBox(height: 24),
              
              // Properties Quick View
              _buildPropertiesQuickView(),
              
              const SizedBox(height: 24),
              
              // System Health Summary
              _buildSystemHealthSummary(),
              
              const SizedBox(height: 24),
              
              // Critical Alerts Feed
              _buildCriticalAlerts(),
            ],
          ),
        ),
      ),
      // Alternative floating action button for V2 access
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          final isAuthenticated = serviceLocator.authService.isAuthenticated;
          print('FAB toggle pressed! Auth: $isAuthenticated'); // Debug log
          if (isAuthenticated) {
            ref.read(dashboardVersionProvider.notifier).toggleVersion();
          } else {
            context.go(AppRoutes.login);
          }
        },
        icon: Icon(
          serviceLocator.authService.isAuthenticated ? Icons.arrow_back : Icons.login,
        ),
        label: Text(
          serviceLocator.authService.isAuthenticated ? 'Old Dashboard' : 'Login for V2',
        ),
        backgroundColor: Colors.grey[600],
        foregroundColor: Colors.white,
        tooltip: serviceLocator.authService.isAuthenticated
            ? 'Switch to Old Dashboard'
            : 'Login to access Dashboard V2',
      ),
    );
  }

  Future<void> _handleRefresh() async {
    // Invalidate the dashboard provider to force a refresh
    ref.invalidate(dashboardOverviewProvider);

    // Wait for the new data to load
    await Future.delayed(const Duration(milliseconds: 500));
  }

  Widget _buildV2PromotionBanner() {
    final isAuthenticated = serviceLocator.authService.isAuthenticated;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryColor.withValues(alpha: 0.1),
            AppTheme.primaryColor.withValues(alpha: 0.05),
          ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.primaryColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              isAuthenticated ? Icons.new_releases : Icons.lock_outline,
              color: AppTheme.primaryColor,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isAuthenticated
                      ? 'Old Dashboard Available'
                      : 'Dashboard V2 - Login Required',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  isAuthenticated
                      ? 'Switch back to the classic dashboard view if needed'
                      : 'Please log in to access Dashboard V2 with real-time data and advanced features',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[700],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          ElevatedButton(
            onPressed: () {
              print('Banner button pressed! Auth: $isAuthenticated'); // Debug log
              if (isAuthenticated) {
                ref.read(dashboardVersionProvider.notifier).toggleVersion();
              } else {
                context.go(AppRoutes.login);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: isAuthenticated ? Colors.grey[600] : AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              isAuthenticated ? 'Old Dashboard' : 'Login',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSystemStatusOverview() {
    return Consumer(
      builder: (context, ref, child) {
        final dashboardAsyncValue = ref.watch(dashboardOverviewProvider(
          DashboardParams(timeRange: '24h'),
        ));

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'System Status Overview',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.more_vert),
                      onPressed: () {
                        // TODO: Show more options
                      },
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                dashboardAsyncValue.when(
                  data: (dashboard) => _buildSystemStatusContent(dashboard),
                  loading: () => _buildSystemStatusLoadingState(),
                  error: (error, stack) => _buildSystemStatusErrorState(error),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSystemStatusContent(DashboardOverview dashboard) {
    final systemHealth = dashboard.summary.systemHealth;
    final healthColor = _getHealthColor(systemHealth);

    // Calculate totals from system statuses
    int totalSystems = 0;
    int operationalSystems = 0;
    int warningSystems = 0;
    int criticalSystems = 0;

    for (final systemStatus in dashboard.systemStatuses) {
      totalSystems += systemStatus.total;
      operationalSystems += systemStatus.operational;
      warningSystems += systemStatus.warning;
      criticalSystems += systemStatus.critical;
    }

    return Column(
      children: [
        // Dynamic Progress Bar
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Overall System Health',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                Text(
                  '${systemHealth.toStringAsFixed(1)}%',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: healthColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: systemHealth / 100,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(healthColor),
            ),
          ],
        ),

        const SizedBox(height: 20),

        // Dynamic Quick Stats
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Total Systems',
                totalSystems.toString(),
                Icons.functions,
                AppTheme.infoColor,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'Operational',
                operationalSystems.toString(),
                Icons.check_circle,
                AppTheme.successColor,
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Requires Action',
                warningSystems.toString(),
                Icons.warning,
                AppTheme.warningColor,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'Critical',
                criticalSystems.toString(),
                Icons.error,
                AppTheme.errorColor,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSystemStatusLoadingState() {
    return Column(
      children: [
        // Loading skeleton for progress bar
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  height: 16,
                  width: 120,
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                Container(
                  height: 16,
                  width: 40,
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Container(
              height: 4,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ],
        ),

        const SizedBox(height: 20),

        // Loading skeleton for stat cards
        Row(
          children: [
            Expanded(child: _buildStatCardSkeleton()),
            const SizedBox(width: 12),
            Expanded(child: _buildStatCardSkeleton()),
          ],
        ),

        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(child: _buildStatCardSkeleton()),
            const SizedBox(width: 12),
            Expanded(child: _buildStatCardSkeleton()),
          ],
        ),
      ],
    );
  }

  Widget _buildSystemStatusErrorState(Object error) {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, color: Colors.red, size: 32),
            const SizedBox(height: 8),
            Text(
              'Failed to load system status',
              style: TextStyle(color: Colors.red, fontWeight: FontWeight.w500),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCardSkeleton() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(height: 4),
          Container(
            height: 20,
            width: 30,
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(height: 2),
          Container(
            height: 12,
            width: 60,
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ],
      ),
    );
  }

  Color _getHealthColor(double health) {
    if (health >= 80) {
      return AppTheme.successColor;
    } else if (health >= 60) {
      return AppTheme.warningColor;
    } else {
      return AppTheme.errorColor;
    }
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPropertiesQuickView() {
    return Consumer(
      builder: (context, ref, child) {
        final dashboardAsyncValue = ref.watch(dashboardOverviewProvider(
          DashboardParams(timeRange: '24h'),
        ));

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Properties Quick View',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton(
                  onPressed: () => context.go(AppRoutes.properties),
                  child: const Text('View All'),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Dynamic Properties List
            dashboardAsyncValue.when(
              data: (dashboard) => _buildPropertiesList(dashboard.properties),
              loading: () => _buildPropertiesLoadingState(),
              error: (error, stack) => _buildPropertiesErrorState(error),
            ),
          ],
        );
      },
    );
  }

  Widget _buildPropertiesList(List<PropertySummary> properties) {
    if (properties.isEmpty) {
      return _buildEmptyPropertiesState();
    }

    return Column(
      children: properties.map((property) => _buildDynamicPropertyCard(property)).toList(),
    );
  }

  Widget _buildPropertiesLoadingState() {
    return Column(
      children: List.generate(
        4,
        (index) => _buildPropertyCardSkeleton(),
      ),
    );
  }

  Widget _buildPropertiesErrorState(Object error) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(Icons.error_outline, color: Colors.red, size: 32),
          const SizedBox(height: 8),
          Text(
            'Failed to load properties',
            style: TextStyle(color: Colors.red, fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 4),
          Text(
            'Please check your connection and try again',
            style: TextStyle(color: Colors.red.withValues(alpha: 0.7), fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyPropertiesState() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(Icons.home_work_outlined, color: Colors.grey, size: 32),
          const SizedBox(height: 8),
          Text(
            'No properties available',
            style: TextStyle(color: Colors.grey, fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 4),
          Text(
            'Contact your administrator for access',
            style: TextStyle(color: Colors.grey.withValues(alpha: 0.7), fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // TODO: Uncomment when static property cards are needed
  // Widget _buildPropertyCard(Map<String, dynamic> property) {
  //   Color statusColor;
  //   switch (property['status']) {
  //     case 'operational':
  //       statusColor = AppTheme.successColor;
  //       break;
  //     case 'warning':
  //       statusColor = AppTheme.warningColor;
  //       break;
  //     case 'critical':
  //       statusColor = AppTheme.errorColor;
  //       break;
  //     default:
  //       statusColor = Colors.grey;
  //   }

  //   return Card(
  //     margin: const EdgeInsets.only(bottom: 8),
  //     child: ListTile(
  //       leading: Container(
  //         width: 40,
  //         height: 40,
  //         decoration: BoxDecoration(
  //           color: statusColor.withValues(alpha: 0.1),
  //           borderRadius: BorderRadius.circular(8),
  //         ),
  //         child: Icon(
  //           Icons.business,
  //           color: statusColor,
  //         ),
  //       ),
  //       title: Text(
  //         property['name'],
  //         style: Theme.of(context).textTheme.titleMedium?.copyWith(
  //           fontWeight: FontWeight.w600,
  //         ),
  //       ),
  //       subtitle: Text('${property['systems']} systems'),
  //       trailing: Row(
  //         mainAxisSize: MainAxisSize.min,
  //         children: [
  //           StatusIndicator(status: property['status'], showLabel: true),
  //           const SizedBox(width: 8),
  //           const Icon(Icons.chevron_right),
  //         ],
  //       ),
  //       onTap: () {
  //         // TODO: Navigate to property detail
  //         context.go('/properties/property-${property['name'].toLowerCase().replaceAll(' ', '-')}');
  //       },
  //     ),
  //   );
  // }

  Widget _buildDynamicPropertyCard(PropertySummary property) {
    Color statusColor = _getStatusColor(property.status);

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: statusColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getPropertyTypeIcon(property.type),
            color: statusColor,
          ),
        ),
        title: Text(
          property.name,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Health Score: ${property.healthScore.toStringAsFixed(1)}%'),
            if (property.hasAlerts)
              Text(
                '${property.alertCount} alert${property.alertCount > 1 ? 's' : ''}',
                style: TextStyle(color: AppTheme.warningColor, fontSize: 12),
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            StatusIndicator(status: property.status.toLowerCase(), showLabel: true),
            const SizedBox(width: 8),
            const Icon(Icons.chevron_right),
          ],
        ),
        onTap: () {
          context.go('/properties/${property.id}');
        },
      ),
    );
  }

  Widget _buildPropertyCardSkeleton() {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        title: Container(
          height: 16,
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        subtitle: Container(
          height: 12,
          width: 100,
          margin: const EdgeInsets.only(top: 4),
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        trailing: Container(
          width: 60,
          height: 20,
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(4),
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'OPERATIONAL':
        return AppTheme.successColor;
      case 'WARNING':
        return AppTheme.warningColor;
      case 'CRITICAL':
        return AppTheme.errorColor;
      case 'OFFLINE':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  // TODO: Uncomment when status icons are needed
  // IconData _getStatusIcon(String status) {
  //   switch (status.toUpperCase()) {
  //     case 'OPERATIONAL':
  //       return Icons.check_circle;
  //     case 'WARNING':
  //       return Icons.warning;
  //     case 'CRITICAL':
  //       return Icons.error;
  //     case 'OFFLINE':
  //       return Icons.offline_bolt;
  //     default:
  //       return Icons.help;
  //   }
  // }

  IconData _getPropertyTypeIcon(String type) {
    switch (type.toUpperCase()) {
      case 'RESIDENTIAL':
        return Icons.home;
      case 'OFFICE':
        return Icons.business;
      case 'CONSTRUCTION':
        return Icons.construction;
      default:
        return Icons.business;
    }
  }

  Widget _buildSystemHealthSummary() {
    return Consumer(
      builder: (context, ref, child) {
        final dashboardAsyncValue = ref.watch(dashboardOverviewProvider(
          DashboardParams(timeRange: '24h'),
        ));

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'System Health Summary',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),

                const SizedBox(height: 16),

                dashboardAsyncValue.when(
                  data: (dashboard) => _buildSystemHealthContent(dashboard),
                  loading: () => _buildSystemHealthLoadingState(),
                  error: (error, stack) => _buildSystemHealthErrorState(error),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSystemHealthContent(DashboardOverview dashboard) {
    return Column(
      children: [
        // Dynamic Pie Chart
        SizedBox(
          height: 200,
          child: _buildDynamicPieChart(dashboard.systemStatuses),
        ),

        const SizedBox(height: 16),

        // Dynamic System Categories
        ...dashboard.systemStatuses.map((systemStatus) =>
          _buildDynamicSystemCategory(systemStatus)
        ),
      ],
    );
  }

  Widget _buildSystemHealthLoadingState() {
    return Column(
      children: [
        // Loading skeleton for pie chart
        Container(
          height: 200,
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(100),
          ),
        ),

        const SizedBox(height: 16),

        // Loading skeleton for system categories
        ...List.generate(6, (index) =>
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Row(
              children: [
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Container(
                    height: 16,
                    decoration: BoxDecoration(
                      color: Colors.grey.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
                Container(
                  width: 60,
                  height: 20,
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSystemHealthErrorState(Object error) {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, color: Colors.red, size: 32),
            const SizedBox(height: 8),
            Text(
              'Failed to load system health',
              style: TextStyle(color: Colors.red, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 4),
            Text(
              'Please try refreshing',
              style: TextStyle(color: Colors.red.withValues(alpha: 0.7), fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // TODO: Uncomment when static system categories are needed
  // Widget _buildSystemCategory(String title, String status, IconData icon) {
  //   return Padding(
  //     padding: const EdgeInsets.symmetric(vertical: 4),
  //     child: Row(
  //       children: [
  //         Icon(icon, size: 20, color: Colors.grey[600]),
  //         const SizedBox(width: 12),
  //         Expanded(
  //           child: Text(
  //             title,
  //             style: Theme.of(context).textTheme.bodyMedium,
  //           ),
  //         ),
  //         StatusIndicator(status: status, showLabel: true),
  //       ],
  //     ),
  //   );
  // }

  Widget _buildDynamicPieChart(List<SystemStatusOverview> systemStatuses) {
    if (systemStatuses.isEmpty) {
      return Container(
        height: 200,
        decoration: BoxDecoration(
          color: Colors.grey.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(100),
        ),
        child: Center(
          child: Text(
            'No system data',
            style: TextStyle(color: Colors.grey),
          ),
        ),
      );
    }

    // Calculate totals across all systems
    int totalOperational = 0;
    int totalWarning = 0;
    int totalCritical = 0;
    int totalOffline = 0;

    for (final systemStatus in systemStatuses) {
      totalOperational += systemStatus.operational;
      totalWarning += systemStatus.warning;
      totalCritical += systemStatus.critical;
      totalOffline += systemStatus.offline;
    }

    final sections = <PieChartSectionData>[];

    if (totalOperational > 0) {
      sections.add(
        PieChartSectionData(
          value: totalOperational.toDouble(),
          title: 'Operational\n$totalOperational',
          color: AppTheme.successColor,
          radius: 60,
          titleStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );
    }

    if (totalWarning > 0) {
      sections.add(
        PieChartSectionData(
          value: totalWarning.toDouble(),
          title: 'Warning\n$totalWarning',
          color: AppTheme.warningColor,
          radius: 60,
          titleStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );
    }

    if (totalCritical > 0) {
      sections.add(
        PieChartSectionData(
          value: totalCritical.toDouble(),
          title: 'Critical\n$totalCritical',
          color: AppTheme.errorColor,
          radius: 60,
          titleStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );
    }

    if (totalOffline > 0) {
      sections.add(
        PieChartSectionData(
          value: totalOffline.toDouble(),
          title: 'Offline\n$totalOffline',
          color: Colors.grey,
          radius: 60,
          titleStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );
    }

    return PieChart(
      PieChartData(
        sections: sections,
        centerSpaceRadius: 40,
        sectionsSpace: 2,
      ),
    );
  }

  Widget _buildDynamicSystemCategory(SystemStatusOverview systemStatus) {
    final icon = _getSystemTypeIcon(systemStatus.systemType);
    final title = _getSystemTypeDisplayName(systemStatus.systemType);

    // Determine overall status for this system type
    String overallStatus;
    if (systemStatus.critical > 0 || systemStatus.offline > 0) {
      overallStatus = 'critical';
    } else if (systemStatus.warning > 0) {
      overallStatus = 'warning';
    } else {
      overallStatus = 'operational';
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                Text(
                  '${systemStatus.operational}/${systemStatus.total} operational',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          StatusIndicator(status: overallStatus, showLabel: true),
        ],
      ),
    );
  }

  IconData _getSystemTypeIcon(String systemType) {
    switch (systemType.toUpperCase()) {
      case 'WATER':
        return Icons.water_drop;
      case 'ELECTRICITY':
        return Icons.electrical_services;
      case 'SECURITY':
        return Icons.security;
      case 'INTERNET':
        return Icons.wifi;
      case 'OTT':
        return Icons.tv;
      case 'MAINTENANCE':
        return Icons.build;
      default:
        return Icons.settings;
    }
  }

  String _getSystemTypeDisplayName(String systemType) {
    switch (systemType.toUpperCase()) {
      case 'WATER':
        return 'Water Systems';
      case 'ELECTRICITY':
        return 'Electricity Status';
      case 'SECURITY':
        return 'Security Status';
      case 'INTERNET':
        return 'Internet Connectivity';
      case 'OTT':
        return 'OTT Services';
      case 'MAINTENANCE':
        return 'Maintenance Issues';
      default:
        return systemType;
    }
  }

  Widget _buildCriticalAlerts() {
    return Consumer(
      builder: (context, ref, child) {
        final dashboardAsyncValue = ref.watch(dashboardOverviewProvider(
          DashboardParams(timeRange: '24h'),
        ));

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Critical Alerts',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    // TODO: Navigate to alerts screen
                  },
                  child: const Text('View All'),
                ),
              ],
            ),

            const SizedBox(height: 12),

            dashboardAsyncValue.when(
              data: (dashboard) => _buildAlertsList(dashboard.recentAlerts),
              loading: () => _buildAlertsLoadingState(),
              error: (error, stack) => _buildAlertsErrorState(error),
            ),
          ],
        );
      },
    );
  }

  Widget _buildAlertsList(List<Alert> alerts) {
    if (alerts.isEmpty) {
      return _buildEmptyAlertsState();
    }

    return Column(
      children: alerts.take(5).map((alert) => _buildDynamicAlertCard(alert)).toList(),
    );
  }

  Widget _buildAlertsLoadingState() {
    return Column(
      children: List.generate(
        3,
        (index) => _buildAlertCardSkeleton(),
      ),
    );
  }

  Widget _buildAlertsErrorState(Object error) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(Icons.error_outline, color: Colors.red, size: 32),
          const SizedBox(height: 8),
          Text(
            'Failed to load alerts',
            style: TextStyle(color: Colors.red, fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 4),
          Text(
            'Please check your connection and try again',
            style: TextStyle(color: Colors.red.withValues(alpha: 0.7), fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyAlertsState() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.successColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppTheme.successColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(Icons.check_circle_outline, color: AppTheme.successColor, size: 32),
          const SizedBox(height: 8),
          Text(
            'No critical alerts',
            style: TextStyle(color: AppTheme.successColor, fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 4),
          Text(
            'All systems are running smoothly',
            style: TextStyle(color: AppTheme.successColor.withValues(alpha: 0.7), fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // TODO: Uncomment when static alert cards are needed
  // Widget _buildAlertCard(Map<String, dynamic> alert) {
  //   Color severityColor = alert['severity'] == 'critical'
  //       ? AppTheme.errorColor
  //       : AppTheme.warningColor;

  //   return Card(
  //     margin: const EdgeInsets.only(bottom: 8),
  //     child: ListTile(
  //       leading: Container(
  //         width: 40,
  //         height: 40,
  //         decoration: BoxDecoration(
  //           color: severityColor.withValues(alpha: 0.1),
  //           borderRadius: BorderRadius.circular(8),
  //         ),
  //         child: Icon(
  //           alert['severity'] == 'critical' ? Icons.error : Icons.warning,
  //           color: severityColor,
  //         ),
  //       ),
  //       title: Text(
  //         alert['title'],
  //         style: Theme.of(context).textTheme.titleMedium?.copyWith(
  //           fontWeight: FontWeight.w600,
  //         ),
  //       ),
  //       subtitle: Column(
  //         crossAxisAlignment: CrossAxisAlignment.start,
  //         children: [
  //           Text(alert['description']),
  //           const SizedBox(height: 4),
  //           Text(
  //             alert['time'],
  //             style: Theme.of(context).textTheme.bodySmall?.copyWith(
  //               color: Colors.grey[600],
  //             ),
  //           ),
  //         ],
  //       ),
  //       trailing: Icon(
  //         Icons.chevron_right,
  //         color: Colors.grey[400],
  //       ),
  //       onTap: () {
  //         // TODO: Navigate to alert detail
  //       },
  //     ),
  //   );
  // }

  Widget _buildDynamicAlertCard(Alert alert) {
    Color severityColor = _getAlertSeverityColor(alert.severity);
    IconData severityIcon = _getAlertSeverityIcon(alert.severity);

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: severityColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            severityIcon,
            color: severityColor,
          ),
        ),
        title: Text(
          alert.title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(alert.description),
            const SizedBox(height: 4),
            Text(
              _formatAlertTime(alert.createdAt),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: severityColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                alert.severity.toUpperCase(),
                style: TextStyle(
                  color: severityColor,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              Icons.chevron_right,
              color: Colors.grey[400],
            ),
          ],
        ),
        onTap: () {
          // TODO: Navigate to alert detail
        },
      ),
    );
  }

  Widget _buildAlertCardSkeleton() {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        title: Container(
          height: 16,
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Container(
              height: 12,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            const SizedBox(height: 4),
            Container(
              height: 10,
              width: 80,
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ],
        ),
        trailing: Container(
          width: 60,
          height: 20,
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(4),
          ),
        ),
      ),
    );
  }

  Color _getAlertSeverityColor(String severity) {
    switch (severity.toUpperCase()) {
      case 'CRITICAL':
        return AppTheme.errorColor;
      case 'HIGH':
        return Colors.deepOrange;
      case 'MEDIUM':
        return AppTheme.warningColor;
      case 'LOW':
        return AppTheme.infoColor;
      default:
        return Colors.grey;
    }
  }

  IconData _getAlertSeverityIcon(String severity) {
    switch (severity.toUpperCase()) {
      case 'CRITICAL':
        return Icons.error;
      case 'HIGH':
        return Icons.warning;
      case 'MEDIUM':
        return Icons.info;
      case 'LOW':
        return Icons.info_outline;
      default:
        return Icons.help;
    }
  }

  String _formatAlertTime(String createdAt) {
    try {
      final alertTime = DateTime.parse(createdAt);
      final now = DateTime.now();
      final difference = now.difference(alertTime);

      if (difference.inMinutes < 60) {
        return '${difference.inMinutes} minutes ago';
      } else if (difference.inHours < 24) {
        return '${difference.inHours} hours ago';
      } else {
        return '${difference.inDays} days ago';
      }
    } catch (e) {
      return createdAt;
    }
  }
}
