"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.filterResponseContent = exports.attachUIPermissions = exports.applyDataFiltering = exports.enhancedAuthorize = void 0;
const client_1 = require("@prisma/client");
const PermissionService_1 = require("@/services/PermissionService");
// Enhanced authentication middleware with RBAC/UBAC
const enhancedAuthorize = (resource, action, options = {}) => {
    return async (req, res, next) => {
        try {
            const user = req.user;
            if (!user) {
                res.status(401).json({
                    success: false,
                    error: 'UNAUTHORIZED',
                    message: 'Authentication required',
                    timestamp: new Date().toISOString(),
                    path: req.path,
                });
                return;
            }
            // Build permission context
            const context = {
                userId: user.id,
                role: user.role,
                propertyId: req.params.propertyId || req.body.propertyId,
                officeId: req.params.officeId || req.body.officeId,
                resourceId: req.params.id || req.params.resourceId,
                timestamp: new Date(),
                ipAddress: req.ip || req.connection.remoteAddress,
                userAgent: req.get('User-Agent')
            };
            // Check permission
            const permissionResult = await PermissionService_1.PermissionService.checkPermission(context, resource, action);
            if (!permissionResult.granted) {
                res.status(403).json({
                    success: false,
                    error: 'FORBIDDEN',
                    message: permissionResult.reason || 'Access denied',
                    timestamp: new Date().toISOString(),
                    path: req.path,
                });
                return;
            }
            // Additional property access check
            if (options.requirePropertyAccess && context.propertyId) {
                if (user.role !== client_1.UserRole.SUPER_ADMIN &&
                    !user.assignedProperties.includes(context.propertyId)) {
                    res.status(403).json({
                        success: false,
                        error: 'FORBIDDEN',
                        message: 'Access denied to this property',
                        timestamp: new Date().toISOString(),
                        path: req.path,
                    });
                    return;
                }
            }
            // Store permission context for use in route handlers
            req.permissionContext = context;
            req.permissionResult = permissionResult;
            next();
        }
        catch (error) {
            console.error('Enhanced authorization error:', error);
            res.status(500).json({
                success: false,
                error: 'INTERNAL_SERVER_ERROR',
                message: 'Authorization service error',
                timestamp: new Date().toISOString(),
                path: req.path,
            });
        }
    };
};
exports.enhancedAuthorize = enhancedAuthorize;
// Middleware to apply data filtering
const applyDataFiltering = (resource) => {
    return async (req, res, next) => {
        try {
            const context = req.permissionContext;
            if (!context) {
                next();
                return;
            }
            // Apply data filters to query parameters
            const baseQuery = req.query;
            const filteredQuery = await PermissionService_1.PermissionService.applyDataFilters(context, resource, baseQuery);
            req.filteredQuery = filteredQuery;
            next();
        }
        catch (error) {
            console.error('Data filtering error:', error);
            next(); // Continue without filtering on error
        }
    };
};
exports.applyDataFiltering = applyDataFiltering;
// Middleware to get UI permissions
const attachUIPermissions = async (req, res, next) => {
    try {
        const context = req.permissionContext;
        if (!context) {
            next();
            return;
        }
        const uiPermissions = await PermissionService_1.PermissionService.getUserUIPermissions(context);
        req.uiPermissions = uiPermissions;
        next();
    }
    catch (error) {
        console.error('UI permissions error:', error);
        next(); // Continue without UI permissions on error
    }
};
exports.attachUIPermissions = attachUIPermissions;
// Content filtering middleware
const filterResponseContent = (allowedFields = [], sensitiveFields = []) => {
    return (req, res, next) => {
        const originalJson = res.json;
        res.json = function (data) {
            const context = req.permissionContext;
            const uiPermissions = req.uiPermissions;
            if (context && data) {
                // Filter sensitive fields based on permissions
                const filteredData = filterSensitiveData(data, context, uiPermissions, sensitiveFields);
                return originalJson.call(this, filteredData);
            }
            return originalJson.call(this, data);
        };
        next();
    };
};
exports.filterResponseContent = filterResponseContent;
// Helper function to filter sensitive data
function filterSensitiveData(data, context, uiPermissions, sensitiveFields) {
    if (!data || typeof data !== 'object') {
        return data;
    }
    if (Array.isArray(data)) {
        return data.map(item => filterSensitiveData(item, context, uiPermissions, sensitiveFields));
    }
    const filtered = { ...data };
    // Remove sensitive fields based on permissions
    sensitiveFields.forEach(field => {
        if (field in filtered) {
            const hasPermission = uiPermissions?.widgets?.[`field_${field}`]?.visible !== false;
            if (!hasPermission) {
                delete filtered[field];
            }
        }
    });
    // Recursively filter nested objects
    Object.keys(filtered).forEach(key => {
        if (typeof filtered[key] === 'object' && filtered[key] !== null) {
            filtered[key] = filterSensitiveData(filtered[key], context, uiPermissions, sensitiveFields);
        }
    });
    return filtered;
}
