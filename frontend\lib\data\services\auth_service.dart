import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../core/constants/api_constants.dart';
import '../../core/utils/api_response.dart';
import '../models/user_model.dart';

class AuthService {
  static const String _baseUrl = '${ApiConstants.apiBaseUrl}/auth';

  /// Login with email and password
  Future<ApiResponse<LoginResponse>> login(LoginRequest request) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/login'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode(request.toJson()),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final loginResponse = LoginResponse.fromJson(data);
          return ApiResponse.success(loginResponse);
        } else {
          return ApiResponse.error(data['message'] ?? 'Login failed');
        }
      } else {
        final error = json.decode(response.body);
        return ApiResponse.error(error['message'] ?? '<PERSON>gin failed');
      }
    } catch (e) {
      return ApiResponse.error('Network error: $e');
    }
  }

  /// Logout
  Future<ApiResponse<void>> logout() async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/logout'),
        headers: ApiConstants.getHeaders(),
      );

      if (response.statusCode == 200) {
        return ApiResponse.success(null);
      } else {
        final error = json.decode(response.body);
        return ApiResponse.error(error['message'] ?? 'Logout failed');
      }
    } catch (e) {
      return ApiResponse.error('Network error: $e');
    }
  }

  /// Refresh token
  Future<ApiResponse<LoginResponse>> refreshToken(String refreshToken) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/refresh'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode({'refreshToken': refreshToken}),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final loginResponse = LoginResponse.fromJson(data);
          return ApiResponse.success(loginResponse);
        } else {
          return ApiResponse.error(data['message'] ?? 'Token refresh failed');
        }
      } else {
        final error = json.decode(response.body);
        return ApiResponse.error(error['message'] ?? 'Token refresh failed');
      }
    } catch (e) {
      return ApiResponse.error('Network error: $e');
    }
  }

  /// Get current user
  Future<ApiResponse<UserModel>> getCurrentUser() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/me'),
        headers: ApiConstants.getHeaders(),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final user = UserModel.fromJson(data['data']);
          return ApiResponse.success(user);
        } else {
          return ApiResponse.error(data['message'] ?? 'Failed to get user');
        }
      } else {
        final error = json.decode(response.body);
        return ApiResponse.error(error['message'] ?? 'Failed to get user');
      }
    } catch (e) {
      return ApiResponse.error('Network error: $e');
    }
  }

  /// Update user
  Future<ApiResponse<UserModel>> updateUser(String userId, UpdateUserRequest request) async {
    try {
      final response = await http.put(
        Uri.parse('${ApiConstants.apiBaseUrl}/users/$userId'),
        headers: ApiConstants.getHeaders(),
        body: json.encode(request.toJson()),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final user = UserModel.fromJson(data['data']);
          return ApiResponse.success(user);
        } else {
          return ApiResponse.error(data['message'] ?? 'Failed to update user');
        }
      } else {
        final error = json.decode(response.body);
        return ApiResponse.error(error['message'] ?? 'Failed to update user');
      }
    } catch (e) {
      return ApiResponse.error('Network error: $e');
    }
  }

  /// Create user
  Future<ApiResponse<UserModel>> createUser(CreateUserRequest request) async {
    try {
      final response = await http.post(
        Uri.parse('${ApiConstants.apiBaseUrl}/users'),
        headers: ApiConstants.getHeaders(),
        body: json.encode(request.toJson()),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final user = UserModel.fromJson(data['data']);
          return ApiResponse.success(user);
        } else {
          return ApiResponse.error(data['message'] ?? 'Failed to create user');
        }
      } else {
        final error = json.decode(response.body);
        return ApiResponse.error(error['message'] ?? 'Failed to create user');
      }
    } catch (e) {
      return ApiResponse.error('Network error: $e');
    }
  }

  /// Get all users
  Future<ApiResponse<List<UserModel>>> getUsers({
    int page = 1,
    int limit = 20,
    String? search,
    UserRole? role,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
      };

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }

      if (role != null) {
        queryParams['role'] = role.toString().split('.').last.toUpperCase();
      }

      final uri = Uri.parse('${ApiConstants.apiBaseUrl}/users').replace(
        queryParameters: queryParams,
      );

      final response = await http.get(
        uri,
        headers: ApiConstants.getHeaders(),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final List<dynamic> usersJson = data['data']['users'] ?? data['data'];
          final users = usersJson.map((json) => UserModel.fromJson(json)).toList();
          return ApiResponse.success(users);
        } else {
          return ApiResponse.error(data['message'] ?? 'Failed to get users');
        }
      } else {
        final error = json.decode(response.body);
        return ApiResponse.error(error['message'] ?? 'Failed to get users');
      }
    } catch (e) {
      return ApiResponse.error('Network error: $e');
    }
  }

  /// Delete user
  Future<ApiResponse<void>> deleteUser(String userId) async {
    try {
      final response = await http.delete(
        Uri.parse('${ApiConstants.apiBaseUrl}/users/$userId'),
        headers: ApiConstants.getHeaders(),
      );

      if (response.statusCode == 200) {
        return ApiResponse.success(null);
      } else {
        final error = json.decode(response.body);
        return ApiResponse.error(error['message'] ?? 'Failed to delete user');
      }
    } catch (e) {
      return ApiResponse.error('Network error: $e');
    }
  }

  /// Change password
  Future<ApiResponse<void>> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/change-password'),
        headers: ApiConstants.getHeaders(),
        body: json.encode({
          'currentPassword': currentPassword,
          'newPassword': newPassword,
        }),
      );

      if (response.statusCode == 200) {
        return ApiResponse.success(null);
      } else {
        final error = json.decode(response.body);
        return ApiResponse.error(error['message'] ?? 'Failed to change password');
      }
    } catch (e) {
      return ApiResponse.error('Network error: $e');
    }
  }

  /// Reset password
  Future<ApiResponse<void>> resetPassword(String email) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/reset-password'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode({'email': email}),
      );

      if (response.statusCode == 200) {
        return ApiResponse.success(null);
      } else {
        final error = json.decode(response.body);
        return ApiResponse.error(error['message'] ?? 'Failed to reset password');
      }
    } catch (e) {
      return ApiResponse.error('Network error: $e');
    }
  }

  /// Verify email
  Future<ApiResponse<void>> verifyEmail(String token) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/verify-email'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode({'token': token}),
      );

      if (response.statusCode == 200) {
        return ApiResponse.success(null);
      } else {
        final error = json.decode(response.body);
        return ApiResponse.error(error['message'] ?? 'Failed to verify email');
      }
    } catch (e) {
      return ApiResponse.error('Network error: $e');
    }
  }
}
