export interface TabDefinition {
    tabId: string;
    name: string;
    description?: string;
    order: number;
    icon?: string;
    components: TabComponentDefinition[];
    metadata?: Record<string, any>;
}
export interface TabComponentDefinition {
    componentId: string;
    name: string;
    type: 'widget' | 'button' | 'form' | 'table' | 'card' | 'chart' | 'input' | 'display';
    section?: string;
    permissions: string[];
    metadata?: Record<string, any>;
}
export interface ScreenTabConfiguration {
    screenPath: string;
    screenName: string;
    tabs: TabDefinition[];
}
export declare const SCREEN_TAB_CONFIGURATIONS: ScreenTabConfiguration[];
export declare function getScreenTabConfiguration(screenPath: string): ScreenTabConfiguration | undefined;
export declare function getTabDefinition(screenPath: string, tabId: string): TabDefinition | undefined;
export declare function getTabComponent(screenPath: string, tabId: string, componentId: string): TabComponentDefinition | undefined;
