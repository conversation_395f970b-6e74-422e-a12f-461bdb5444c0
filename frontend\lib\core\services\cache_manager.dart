import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class CacheManager {
  static const String _cachePrefix = 'cache_';
  static const Duration _defaultCacheDuration = Duration(minutes: 30);

  Future<void> cacheData(String key, dynamic data, {Duration? duration}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = _cachePrefix + key;
      final expiryTime = DateTime.now().add(duration ?? _defaultCacheDuration);
      
      final cacheEntry = {
        'data': data,
        'expiryTime': expiryTime.millisecondsSinceEpoch,
      };
      
      await prefs.setString(cacheKey, jsonEncode(cacheEntry));
    } catch (e) {
      // Silently fail cache operations
    }
  }

  Future<T?> getCachedData<T>(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = _cachePrefix + key;
      final cachedString = prefs.getString(cacheKey);
      
      if (cachedString == null) return null;
      
      final cacheEntry = jsonDecode(cachedString);
      final expiryTime = DateTime.fromMillisecondsSinceEpoch(cacheEntry['expiryTime']);
      
      if (DateTime.now().isAfter(expiryTime)) {
        // Cache expired, remove it
        await prefs.remove(cacheKey);
        return null;
      }
      
      return cacheEntry['data'] as T;
    } catch (e) {
      return null;
    }
  }

  Future<bool> isCached(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = _cachePrefix + key;
      final cachedString = prefs.getString(cacheKey);
      
      if (cachedString == null) return false;
      
      final cacheEntry = jsonDecode(cachedString);
      final expiryTime = DateTime.fromMillisecondsSinceEpoch(cacheEntry['expiryTime']);
      
      if (DateTime.now().isAfter(expiryTime)) {
        // Cache expired, remove it
        await prefs.remove(cacheKey);
        return false;
      }
      
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith(_cachePrefix));
      
      for (final key in keys) {
        await prefs.remove(key);
      }
    } catch (e) {
      // Silently fail cache operations
    }
  }

  Future<void> removeCachedData(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = _cachePrefix + key;
      await prefs.remove(cacheKey);
    } catch (e) {
      // Silently fail cache operations
    }
  }

  Future<List<String>> getCachedKeys() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getKeys()
          .where((key) => key.startsWith(_cachePrefix))
          .map((key) => key.substring(_cachePrefix.length))
          .toList();
    } catch (e) {
      return [];
    }
  }

  Future<void> cleanExpiredCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith(_cachePrefix));
      final now = DateTime.now();
      
      for (final key in keys) {
        final cachedString = prefs.getString(key);
        if (cachedString != null) {
          try {
            final cacheEntry = jsonDecode(cachedString);
            final expiryTime = DateTime.fromMillisecondsSinceEpoch(cacheEntry['expiryTime']);
            
            if (now.isAfter(expiryTime)) {
              await prefs.remove(key);
            }
          } catch (e) {
            // Remove corrupted cache entries
            await prefs.remove(key);
          }
        }
      }
    } catch (e) {
      // Silently fail cache operations
    }
  }

  Future<int> getCacheSize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith(_cachePrefix));
      int totalSize = 0;
      
      for (final key in keys) {
        final value = prefs.getString(key);
        if (value != null) {
          totalSize += value.length;
        }
      }
      
      return totalSize;
    } catch (e) {
      return 0;
    }
  }

  String generateCacheKey(String endpoint, Map<String, dynamic>? params) {
    if (params == null || params.isEmpty) {
      return endpoint;
    }
    
    final sortedParams = Map.fromEntries(
      params.entries.toList()..sort((a, b) => a.key.compareTo(b.key))
    );
    
    final paramString = sortedParams.entries
        .map((e) => '${e.key}=${e.value}')
        .join('&');
    
    return '$endpoint?$paramString';
  }
}
