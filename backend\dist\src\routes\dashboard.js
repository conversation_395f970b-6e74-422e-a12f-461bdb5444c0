"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const client_1 = require("@prisma/client");
const prisma_1 = require("@/lib/prisma");
const validation_1 = require("@/lib/validation");
const errorHandler_1 = require("@/middleware/errorHandler");
const auth_1 = require("@/middleware/auth");
const rateLimiter_1 = require("@/middleware/rateLimiter");
const router = (0, express_1.Router)();
// Apply authentication and rate limiting
router.use(auth_1.authenticate);
router.use(rateLimiter_1.generalRateLimit);
/**
 * @swagger
 * /dashboard/overview:
 *   get:
 *     tags: [Dashboard]
 *     summary: Get dashboard overview
 *     description: Retrieve comprehensive dashboard data with system health and statistics
 *     parameters:
 *       - in: query
 *         name: propertyIds
 *         description: Filter by specific properties (comma-separated)
 *         schema:
 *           type: string
 *       - in: query
 *         name: timeRange
 *         description: Time range for statistics
 *         schema:
 *           type: string
 *           enum: [24h, 7d, 30d, 90d]
 *           default: 24h
 *     responses:
 *       200:
 *         description: Dashboard overview retrieved successfully
 */
router.get('/overview', (0, auth_1.authorize)([], 'canViewDashboard'), (0, errorHandler_1.validateRequest)(validation_1.dashboardQuerySchema, 'query'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { propertyIds, timeRange } = req.query;
    const user = req.user;
    // Determine time range for statistics
    const now = new Date();
    let startDate;
    switch (timeRange) {
        case '7d':
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
        case '30d':
            startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            break;
        case '90d':
            startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
            break;
        default: // 24h
            startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }
    // Build property filter based on RBAC
    let propertyFilter = {};
    if (user.role !== client_1.UserRole.SUPER_ADMIN) {
        propertyFilter.id = { in: user.assignedProperties };
    }
    // Apply specific property filter if provided
    if (propertyIds) {
        const requestedPropertyIds = propertyIds.split(',');
        if (user.role !== client_1.UserRole.SUPER_ADMIN) {
            // Filter to only include properties user has access to
            const allowedPropertyIds = requestedPropertyIds.filter(id => user.assignedProperties.includes(id));
            propertyFilter.id = { in: allowedPropertyIds };
        }
        else {
            propertyFilter.id = { in: requestedPropertyIds };
        }
    }
    // Get properties with system statuses and alerts
    const properties = await prisma_1.prisma.property.findMany({
        where: propertyFilter,
        include: {
            systemStatuses: true,
            alerts: {
                where: {
                    status: 'OPEN',
                },
            },
            _count: {
                select: {
                    alerts: {
                        where: {
                            status: 'OPEN',
                        },
                    },
                },
            },
        },
    });
    // Calculate summary statistics
    const totalProperties = properties.length;
    const activeProperties = properties.filter(p => p.isActive).length;
    const allAlerts = properties.flatMap(p => p.alerts);
    const totalAlerts = allAlerts.length;
    const criticalAlerts = allAlerts.filter(a => a.severity === 'CRITICAL').length;
    // Calculate system health
    const allSystemStatuses = properties.flatMap(p => p.systemStatuses);
    const totalSystems = allSystemStatuses.length;
    const operationalSystems = allSystemStatuses.filter(s => s.status === 'OPERATIONAL').length;
    const systemHealth = totalSystems > 0 ? Math.round((operationalSystems / totalSystems) * 100) : 100;
    // Transform properties to summary format
    const propertySummaries = properties.map(property => {
        const avgHealthScore = property.systemStatuses.length > 0
            ? property.systemStatuses.reduce((sum, status) => sum + (status.healthScore || 0), 0) / property.systemStatuses.length
            : 100;
        const criticalSystems = property.systemStatuses.filter(s => s.status === 'CRITICAL').length;
        const warningSystems = property.systemStatuses.filter(s => s.status === 'WARNING').length;
        let overallStatus = 'OPERATIONAL';
        if (criticalSystems > 0)
            overallStatus = 'CRITICAL';
        else if (warningSystems > 0)
            overallStatus = 'WARNING';
        return {
            id: property.id,
            name: property.name,
            type: property.type,
            status: overallStatus,
            healthScore: Math.round(avgHealthScore),
            alertCount: property._count.alerts,
            lastUpdate: property.updatedAt.toISOString(),
        };
    });
    // Calculate system status overview
    const systemStatusOverview = [
        'WATER', 'ELECTRICITY', 'SECURITY', 'INTERNET', 'OTT', 'MAINTENANCE'
    ].map(systemType => {
        const systemStatuses = allSystemStatuses.filter(s => s.systemType === systemType);
        const total = systemStatuses.length;
        const operational = systemStatuses.filter(s => s.status === 'OPERATIONAL').length;
        const warning = systemStatuses.filter(s => s.status === 'WARNING').length;
        const critical = systemStatuses.filter(s => s.status === 'CRITICAL').length;
        const offline = systemStatuses.filter(s => s.status === 'OFFLINE').length;
        return {
            systemType,
            operational,
            warning,
            critical,
            offline,
            total,
        };
    });
    // Get recent activities
    const recentActivities = await prisma_1.prisma.activity.findMany({
        where: {
            createdAt: { gte: startDate },
            ...(user.role !== client_1.UserRole.SUPER_ADMIN && {
                OR: [
                    { userId: user.id },
                    { propertyId: { in: user.assignedProperties } },
                ],
            }),
        },
        include: {
            user: {
                select: {
                    id: true,
                    name: true,
                    email: true,
                },
            },
            property: {
                select: {
                    id: true,
                    name: true,
                },
            },
        },
        orderBy: { createdAt: 'desc' },
        take: 20,
    });
    // Get recent alerts
    const recentAlerts = await prisma_1.prisma.alert.findMany({
        where: {
            createdAt: { gte: startDate },
            ...(user.role !== client_1.UserRole.SUPER_ADMIN && {
                propertyId: { in: user.assignedProperties },
            }),
        },
        include: {
            property: {
                select: {
                    id: true,
                    name: true,
                },
            },
        },
        orderBy: { createdAt: 'desc' },
        take: 10,
    });
    // Calculate statistics and trends
    const incidents = await prisma_1.prisma.alert.count({
        where: {
            createdAt: { gte: startDate },
            ...(user.role !== client_1.UserRole.SUPER_ADMIN && {
                propertyId: { in: user.assignedProperties },
            }),
        },
    });
    const resolved = await prisma_1.prisma.alert.count({
        where: {
            resolvedAt: { gte: startDate },
            ...(user.role !== client_1.UserRole.SUPER_ADMIN && {
                propertyId: { in: user.assignedProperties },
            }),
        },
    });
    const dashboardOverview = {
        summary: {
            totalProperties,
            activeProperties,
            totalAlerts,
            criticalAlerts,
            systemHealth,
        },
        properties: propertySummaries,
        recentAlerts,
        systemStatuses: systemStatusOverview,
        activities: recentActivities,
        statistics: {
            timeRange,
            metrics: {
                uptime: systemHealth,
                incidents,
                resolved,
                avgResponseTime: 0, // This would need to be calculated based on alert resolution times
            },
            trends: [], // This would need historical data to generate trends
        },
    };
    res.json({
        success: true,
        data: dashboardOverview,
        timestamp: new Date().toISOString(),
    });
}));
/**
 * @swagger
 * /dashboard/alerts:
 *   get:
 *     tags: [Dashboard]
 *     summary: Get critical alerts
 *     description: Retrieve critical alerts and notifications
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *       - in: query
 *         name: severity
 *         schema:
 *           type: string
 *           enum: [LOW, MEDIUM, HIGH, CRITICAL]
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [OPEN, ACKNOWLEDGED, RESOLVED]
 *     responses:
 *       200:
 *         description: Alerts retrieved successfully
 */
router.get('/alerts', (0, auth_1.authorize)([], 'canViewDashboard'), (0, errorHandler_1.validateRequest)(validation_1.alertQuerySchema, 'query'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { page, limit, severity, status, propertyId } = req.query;
    const user = req.user;
    // Build where clause
    let whereClause = {};
    // Apply RBAC filtering
    if (user.role !== client_1.UserRole.SUPER_ADMIN) {
        whereClause.propertyId = { in: user.assignedProperties };
    }
    // Apply filters
    if (severity)
        whereClause.severity = severity;
    if (status)
        whereClause.status = status;
    if (propertyId) {
        // Check if user has access to this property
        if (user.role !== client_1.UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
            whereClause.propertyId = { in: [] }; // No results
        }
        else {
            whereClause.propertyId = propertyId;
        }
    }
    // Get total count
    const total = await prisma_1.prisma.alert.count({ where: whereClause });
    // Get alerts with pagination
    const alerts = await prisma_1.prisma.alert.findMany({
        where: whereClause,
        include: {
            property: {
                select: {
                    id: true,
                    name: true,
                    type: true,
                },
            },
        },
        orderBy: [
            { severity: 'desc' },
            { createdAt: 'desc' },
        ],
        skip: (page - 1) * limit,
        take: limit,
    });
    const totalPages = Math.ceil(total / limit);
    const response = {
        data: alerts,
        pagination: {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1,
        },
    };
    res.json({
        success: true,
        data: response,
        timestamp: new Date().toISOString(),
    });
}));
exports.default = router;
