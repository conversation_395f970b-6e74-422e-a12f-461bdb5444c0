"use strict";
// Parameter Resolver Configurations
Object.defineProperty(exports, "__esModule", { value: true });
exports.BREADCRUMB_TEMPLATES = exports.PARAMETER_RESOLVERS = void 0;
exports.getParameterResolver = getParameterResolver;
exports.getBreadcrumbTemplate = getBreadcrumbTemplate;
exports.PARAMETER_RESOLVERS = [
    // Property ID resolver
    {
        parameterName: 'propertyId',
        sourceTable: 'properties',
        sourceField: 'name',
        keyField: 'id',
        cacheMinutes: 10, // Properties don't change names often
        metadata: {
            description: 'Resolves property UUID to property name',
            example: '9cd0eb21-8689-46a5-9e52-8d6c672e7775 → Jubilee Hills Residence'
        }
    },
    // User ID resolver
    {
        parameterName: 'userId',
        sourceTable: 'users',
        sourceField: 'name',
        keyField: 'id',
        cacheMinutes: 5, // User names might change
        metadata: {
            description: 'Resolves user UUID to user name',
            example: 'e37e561b-1f77-4bcb-903f-2ca8159c1d48 → <PERSON>'
        }
    },
    // Office ID resolver
    {
        parameterName: 'officeId',
        sourceTable: 'offices',
        sourceField: 'name',
        keyField: 'id',
        cacheMinutes: 10,
        metadata: {
            description: 'Resolves office UUID to office name',
            example: 'a1c9bff1-80bb-44be-a94d-d9dca5937d0f → Banjara Hills Office'
        }
    },
    // Maintenance Issue ID resolver
    {
        parameterName: 'issueId',
        sourceTable: 'maintenance_issues',
        sourceField: 'title',
        keyField: 'id',
        cacheMinutes: 2, // Issues change frequently
        metadata: {
            description: 'Resolves maintenance issue UUID to issue title',
            example: 'issue-uuid → Plumbing Repair - Kitchen Sink'
        }
    },
    // Employee ID resolver
    {
        parameterName: 'employeeId',
        sourceTable: 'employees',
        sourceField: 'name',
        keyField: 'id',
        cacheMinutes: 5,
        metadata: {
            description: 'Resolves employee UUID to employee name',
            example: 'emp-uuid → Rajesh Kumar'
        }
    },
    // Camera ID resolver (for CCTV systems)
    {
        parameterName: 'cameraId',
        sourceTable: 'security_cameras',
        sourceField: 'name',
        keyField: 'id',
        cacheMinutes: 15, // Camera names rarely change
        metadata: {
            description: 'Resolves camera UUID to camera name/location',
            example: 'cam-uuid → Main Gate Camera'
        }
    },
    // System Type resolver (for dynamic system types)
    {
        parameterName: 'systemType',
        sourceTable: 'system_types',
        sourceField: 'display_name',
        keyField: 'code',
        cacheMinutes: 30, // System types are static
        metadata: {
            description: 'Resolves system type code to display name',
            example: 'elec → Electricity Management'
        }
    },
    // OTT Service ID resolver
    {
        parameterName: 'ottServiceId',
        sourceTable: 'ott_services',
        sourceField: 'service_name',
        keyField: 'id',
        cacheMinutes: 5,
        metadata: {
            description: 'Resolves OTT service UUID to service name',
            example: 'ott-uuid → Netflix Premium'
        }
    },
    // Department ID resolver
    {
        parameterName: 'departmentId',
        sourceTable: 'departments',
        sourceField: 'name',
        keyField: 'id',
        cacheMinutes: 15,
        metadata: {
            description: 'Resolves department UUID to department name',
            example: 'dept-uuid → Maintenance Department'
        }
    },
    // Vendor ID resolver
    {
        parameterName: 'vendorId',
        sourceTable: 'vendors',
        sourceField: 'company_name',
        keyField: 'id',
        cacheMinutes: 10,
        metadata: {
            description: 'Resolves vendor UUID to company name',
            example: 'vendor-uuid → ABC Security Systems'
        }
    }
];
exports.BREADCRUMB_TEMPLATES = [
    {
        pathPattern: '/dashboard',
        displayTemplate: 'Dashboard',
        description: 'Main dashboard',
        examples: ['Dashboard']
    },
    {
        pathPattern: '/properties',
        displayTemplate: 'Properties',
        description: 'Property listing',
        examples: ['Properties']
    },
    {
        pathPattern: '/properties/{propertyId}',
        displayTemplate: '{propertyName}',
        description: 'Individual property view',
        examples: ['Jubilee Hills Residence', 'Banjara Hills Office Complex']
    },
    {
        pathPattern: '/properties/{propertyId}/systems',
        displayTemplate: '{propertyName} > Systems',
        description: 'Property systems overview',
        examples: ['Jubilee Hills Residence > Systems', 'Banjara Hills Office > Systems']
    },
    {
        pathPattern: '/properties/{propertyId}/systems/security',
        displayTemplate: '{propertyName} > Security',
        description: 'Security system management',
        examples: ['Jubilee Hills Residence > Security', 'Office Complex > Security']
    },
    {
        pathPattern: '/properties/{propertyId}/systems/security/cctv',
        displayTemplate: '{propertyName} > Security > CCTV',
        description: 'CCTV management',
        examples: ['Jubilee Hills Residence > Security > CCTV']
    },
    {
        pathPattern: '/properties/{propertyId}/systems/security/cctv/camera/{cameraId}',
        displayTemplate: '{propertyName} > Security > CCTV > {cameraName}',
        description: 'Individual camera management',
        examples: ['Jubilee Hills Residence > Security > CCTV > Main Gate Camera']
    },
    {
        pathPattern: '/properties/{propertyId}/systems/security/access-control',
        displayTemplate: '{propertyName} > Security > Access Control',
        description: 'Access control system',
        examples: ['Jubilee Hills Residence > Security > Access Control']
    },
    {
        pathPattern: '/properties/{propertyId}/systems/security/access-control/users',
        displayTemplate: '{propertyName} > Security > Access Control > Users',
        description: 'Access control user management',
        examples: ['Jubilee Hills Residence > Security > Access Control > Users']
    },
    {
        pathPattern: '/properties/{propertyId}/systems/electricity',
        displayTemplate: '{propertyName} > Electricity',
        description: 'Electricity system management',
        examples: ['Jubilee Hills Residence > Electricity']
    },
    {
        pathPattern: '/properties/{propertyId}/systems/electricity/generator',
        displayTemplate: '{propertyName} > Electricity > Generator',
        description: 'Generator management',
        examples: ['Jubilee Hills Residence > Electricity > Generator']
    },
    {
        pathPattern: '/properties/{propertyId}/systems/electricity/ups',
        displayTemplate: '{propertyName} > Electricity > UPS',
        description: 'UPS management',
        examples: ['Jubilee Hills Residence > Electricity > UPS']
    },
    {
        pathPattern: '/properties/{propertyId}/systems/water',
        displayTemplate: '{propertyName} > Water',
        description: 'Water system management',
        examples: ['Jubilee Hills Residence > Water']
    },
    {
        pathPattern: '/properties/{propertyId}/systems/water/tanks',
        displayTemplate: '{propertyName} > Water > Tanks',
        description: 'Water tank monitoring',
        examples: ['Jubilee Hills Residence > Water > Tanks']
    },
    {
        pathPattern: '/properties/{propertyId}/systems/water/pumps',
        displayTemplate: '{propertyName} > Water > Pumps',
        description: 'Water pump control',
        examples: ['Jubilee Hills Residence > Water > Pumps']
    },
    {
        pathPattern: '/properties/{propertyId}/systems/ott',
        displayTemplate: '{propertyName} > OTT Services',
        description: 'OTT platform management',
        examples: ['Jubilee Hills Residence > OTT Services']
    },
    {
        pathPattern: '/office',
        displayTemplate: 'Office Management',
        description: 'Office operations',
        examples: ['Office Management']
    },
    {
        pathPattern: '/office/attendance',
        displayTemplate: 'Office > Attendance',
        description: 'Employee attendance',
        examples: ['Office > Attendance']
    },
    {
        pathPattern: '/office/employees',
        displayTemplate: 'Office > Employees',
        description: 'Employee management',
        examples: ['Office > Employees']
    },
    {
        pathPattern: '/office/employees/{employeeId}',
        displayTemplate: 'Office > Employees > {employeeName}',
        description: 'Individual employee details',
        examples: ['Office > Employees > Rajesh Kumar']
    },
    {
        pathPattern: '/maintenance',
        displayTemplate: 'Maintenance',
        description: 'Maintenance management',
        examples: ['Maintenance']
    },
    {
        pathPattern: '/maintenance/issues',
        displayTemplate: 'Maintenance > Issues',
        description: 'Maintenance issues',
        examples: ['Maintenance > Issues']
    },
    {
        pathPattern: '/maintenance/issues/{issueId}',
        displayTemplate: 'Maintenance > Issues > {issueTitle}',
        description: 'Individual maintenance issue',
        examples: ['Maintenance > Issues > Plumbing Repair - Kitchen Sink']
    },
    {
        pathPattern: '/maintenance/schedule',
        displayTemplate: 'Maintenance > Schedule',
        description: 'Maintenance scheduling',
        examples: ['Maintenance > Schedule']
    },
    {
        pathPattern: '/users',
        displayTemplate: 'User Management',
        description: 'User administration',
        examples: ['User Management']
    },
    {
        pathPattern: '/users/{userId}',
        displayTemplate: 'Users > {userName}',
        description: 'Individual user profile',
        examples: ['Users > John Doe']
    }
];
// Helper function to get resolver by parameter name
function getParameterResolver(parameterName) {
    return exports.PARAMETER_RESOLVERS.find(resolver => resolver.parameterName === parameterName);
}
// Helper function to get breadcrumb template by path
function getBreadcrumbTemplate(pathPattern) {
    return exports.BREADCRUMB_TEMPLATES.find(template => template.pathPattern === pathPattern);
}
