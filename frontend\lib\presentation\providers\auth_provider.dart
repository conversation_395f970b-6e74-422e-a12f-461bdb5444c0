import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../data/models/user_model.dart';
import '../../data/services/auth_service.dart';

class AuthProvider extends ChangeNotifier {
  UserModel? _currentUser;
  String? _token;
  String? _refreshToken;
  bool _isLoading = false;
  String? _error;

  // Getters
  UserModel? get currentUser => _currentUser;
  String? get token => _token;
  String? get refreshToken => _refreshToken;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _currentUser != null && _token != null;

  // Auth service instance
  final AuthService _authService = AuthService();

  // Storage keys
  static const String _tokenKey = 'auth_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _userKey = 'current_user';

  AuthProvider() {
    _loadStoredAuth();
  }

  /// Load stored authentication data
  Future<void> _loadStoredAuth() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _token = prefs.getString(_tokenKey);
      _refreshToken = prefs.getString(_refreshTokenKey);
      
      final userJson = prefs.getString(_userKey);
      if (userJson != null) {
        // Parse user from JSON string
        // Note: This would need proper JSON parsing in real implementation
        // For now, we'll create a mock user for testing
        _currentUser = UserModel(
          id: '1',
          name: 'Test User',
          email: '<EMAIL>',
          role: UserRole.superAdmin,
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      }
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading stored auth: $e');
    }
  }

  /// Store authentication data
  Future<void> _storeAuth() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      if (_token != null) {
        await prefs.setString(_tokenKey, _token!);
      } else {
        await prefs.remove(_tokenKey);
      }
      
      if (_refreshToken != null) {
        await prefs.setString(_refreshTokenKey, _refreshToken!);
      } else {
        await prefs.remove(_refreshTokenKey);
      }
      
      if (_currentUser != null) {
        // Store user as JSON string
        await prefs.setString(_userKey, _currentUser!.toJson().toString());
      } else {
        await prefs.remove(_userKey);
      }
    } catch (e) {
      debugPrint('Error storing auth: $e');
    }
  }

  /// Login with email and password
  Future<bool> login(String email, String password) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _authService.login(LoginRequest(
        email: email,
        password: password,
      ));

      if (response.isSuccess && response.data != null) {
        final loginResponse = response.data!;
        _token = loginResponse.token;
        _refreshToken = loginResponse.refreshToken;
        _currentUser = loginResponse.user;
        
        await _storeAuth();
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _setError(response.getErrorMessage('Login failed'));
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Network error: $e');
      _setLoading(false);
      return false;
    }
  }

  /// Logout
  Future<void> logout() async {
    _setLoading(true);

    try {
      // Call logout API if token exists
      if (_token != null) {
        await _authService.logout();
      }
    } catch (e) {
      debugPrint('Error during logout API call: $e');
    } finally {
      // Clear local data regardless of API call result
      _currentUser = null;
      _token = null;
      _refreshToken = null;
      
      await _clearStoredAuth();
      _setLoading(false);
      notifyListeners();
    }
  }

  /// Clear stored authentication data
  Future<void> _clearStoredAuth() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_tokenKey);
      await prefs.remove(_refreshTokenKey);
      await prefs.remove(_userKey);
    } catch (e) {
      debugPrint('Error clearing stored auth: $e');
    }
  }

  /// Refresh token
  Future<bool> refreshAuthToken() async {
    if (_refreshToken == null) return false;

    try {
      final response = await _authService.refreshToken(_refreshToken!);
      
      if (response.isSuccess && response.data != null) {
        final loginResponse = response.data!;
        _token = loginResponse.token;
        _refreshToken = loginResponse.refreshToken;
        _currentUser = loginResponse.user;
        
        await _storeAuth();
        notifyListeners();
        return true;
      } else {
        // Refresh failed, logout user
        await logout();
        return false;
      }
    } catch (e) {
      debugPrint('Error refreshing token: $e');
      await logout();
      return false;
    }
  }

  /// Get current user info
  Future<void> getCurrentUser() async {
    if (_token == null) return;

    try {
      final response = await _authService.getCurrentUser();
      
      if (response.isSuccess && response.data != null) {
        _currentUser = response.data!;
        await _storeAuth();
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error getting current user: $e');
    }
  }

  /// Update user profile
  Future<bool> updateProfile(UpdateUserRequest request) async {
    if (_currentUser == null) return false;

    _setLoading(true);
    _clearError();

    try {
      final response = await _authService.updateUser(_currentUser!.id, request);
      
      if (response.isSuccess && response.data != null) {
        _currentUser = response.data!;
        await _storeAuth();
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _setError(response.getErrorMessage('Update failed'));
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Network error: $e');
      _setLoading(false);
      return false;
    }
  }

  /// Check if user has permission
  bool hasPermission(String permission) {
    if (_currentUser == null) return false;
    
    // Super admin has all permissions
    if (_currentUser!.role == UserRole.superAdmin) return true;
    
    // Add more permission logic here based on roles
    return false;
  }

  /// Check if user can access screen
  bool canAccessScreen(String screenName) {
    if (_currentUser == null) return false;
    
    // Super admin can access all screens
    if (_currentUser!.role == UserRole.superAdmin) return true;
    
    // Add more screen access logic here based on roles
    return true; // For now, allow access to most screens
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Set error message
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// Clear error message
  void _clearError() {
    _error = null;
    notifyListeners();
  }

  /// Mock login for testing
  Future<bool> mockLogin({UserRole role = UserRole.superAdmin}) async {
    _setLoading(true);
    
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));
    
    _currentUser = UserModel(
      id: '1',
      name: 'Test User',
      email: '<EMAIL>',
      role: role,
      isActive: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      assignedProperties: ['prop1', 'prop2'],
    );
    
    _token = 'mock_token_${DateTime.now().millisecondsSinceEpoch}';
    _refreshToken = 'mock_refresh_token_${DateTime.now().millisecondsSinceEpoch}';
    
    await _storeAuth();
    _setLoading(false);
    notifyListeners();
    return true;
  }
}
