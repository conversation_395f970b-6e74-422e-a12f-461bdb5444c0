export interface RoleTabPermission {
    tabId: string;
    accessLevel: 'full' | 'read' | 'restricted' | 'none';
    isVisible: boolean;
    isEnabled: boolean;
    restrictions?: {
        hideComponents?: string[];
        disableComponents?: string[];
        hideFields?: string[];
        customConditions?: Record<string, any>;
    };
}
export interface ScreenTabPermissions {
    screenPath: string;
    role: string;
    tabs: RoleTabPermission[];
}
export declare const ROLE_TAB_PERMISSIONS: ScreenTabPermissions[];
export declare function getRoleTabPermissions(screenPath: string, role: string): ScreenTabPermissions | undefined;
export declare function getTabPermission(screenPath: string, role: string, tabId: string): RoleTabPermission | undefined;
export declare function isTabVisible(screenPath: string, role: string, tabId: string): boolean;
export declare function isTabEnabled(screenPath: string, role: string, tabId: string): boolean;
export declare function getVisibleTabs(screenPath: string, role: string): string[];
