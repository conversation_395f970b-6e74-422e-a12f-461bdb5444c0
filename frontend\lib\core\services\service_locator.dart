import 'package:flutter/foundation.dart';
import 'api_client.dart';
import 'auth_service.dart';
import 'property_service.dart';
import 'dashboard_service.dart';
import 'office_service.dart';
import 'notification_service.dart';
import 'file_service.dart';
import 'cache_manager.dart';
import 'connectivity_manager.dart';
import '../../data/repositories/alert_repository.dart';
import '../../data/repositories/auth_repository.dart';
import '../../data/repositories/property_repository.dart';
import '../../data/repositories/office_repository.dart';
import '../../data/repositories/system_repository.dart';
import '../../data/repositories/maintenance_repository.dart';


class ServiceLocator {
  static final ServiceLocator _instance = ServiceLocator._internal();
  factory ServiceLocator() => _instance;
  ServiceLocator._internal();

  bool _isInitialized = false;

  // Service instances
  late final ApiClient _apiClient;
  late final AuthService _authService;
  late final PropertyService _propertyService;
  late final DashboardService _dashboardService;
  late final OfficeService _officeService;
  late final NotificationService _notificationService;
  late final FileService _fileService;
  late final CacheManager _cacheManager;
  late final ConnectivityManager _connectivityManager;

  // Repository instances
  late final AlertRepository _alertRepository;
  late final AuthRepository _authRepository;
  late final PropertyRepository _propertyRepository;
  late final OfficeRepository _officeRepository;
  late final SystemRepository _systemRepository;
  late final MaintenanceRepository _maintenanceRepository;


  // Getters for services
  ApiClient get apiClient => _apiClient;
  AuthService get authService => _authService;
  PropertyService get propertyService => _propertyService;
  DashboardService get dashboardService => _dashboardService;
  OfficeService get officeService => _officeService;
  NotificationService get notificationService => _notificationService;
  FileService get fileService => _fileService;
  CacheManager get cacheManager => _cacheManager;
  ConnectivityManager get connectivityManager => _connectivityManager;

  // Getters for repositories
  AlertRepository get alertRepository => _alertRepository;
  AuthRepository get authRepository => _authRepository;
  PropertyRepository get propertyRepository => _propertyRepository;
  OfficeRepository get officeRepository => _officeRepository;
  SystemRepository get systemRepository => _systemRepository;
  MaintenanceRepository get maintenanceRepository => _maintenanceRepository;


  bool get isInitialized => _isInitialized;

  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      if (kDebugMode) {
        print('Initializing services...');
      }

      // Initialize API client first
      _apiClient = ApiClient();
      await _apiClient.initialize();

      // Initialize auth service
      _authService = AuthService();
      await _authService.initialize();

      // Initialize other services
      _propertyService = PropertyService();
      _dashboardService = DashboardService();
      _officeService = OfficeService();
      _notificationService = NotificationService();
      _fileService = FileService();
      _cacheManager = CacheManager();
      _connectivityManager = ConnectivityManager();
      await _connectivityManager.initialize();

      // Initialize repositories
      _alertRepository = AlertRepository();
      _authRepository = AuthRepository();
      _propertyRepository = PropertyRepository();
      _officeRepository = OfficeRepository();
      _systemRepository = SystemRepository();
      _maintenanceRepository = MaintenanceRepository();



      _isInitialized = true;

      if (kDebugMode) {
        print('Services initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing services: $e');
      }
      rethrow;
    }
  }

  Future<void> dispose() async {
    if (!_isInitialized) return;

    try {
      if (kDebugMode) {
        print('Disposing services...');
      }



      // Clear auth data
      await _authService.logout();

      _isInitialized = false;

      if (kDebugMode) {
        print('Services disposed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error disposing services: $e');
      }
    }
  }

  // Authentication helpers
  Future<void> onLoginSuccess() async {
    // Authentication successful - services are ready
  }

  Future<void> onLogout() async {
    // Clear auth data
    await _authService.logout();
  }



  // Service status
  Map<String, dynamic> getServiceStatus() {
    return {
      'isInitialized': _isInitialized,
      'isAuthenticated': _authService.isAuthenticated,
      'currentUser': _authService.currentUser?.toJson(),
    };
  }

  // Error handling
  void handleApiError(dynamic error) {
    if (kDebugMode) {
      print('API Error: $error');
    }
    
    // Handle specific error types
    if (error is Map<String, dynamic>) {
      final errorCode = error['error'];
      
      switch (errorCode) {
        case 'UNAUTHORIZED':
          // Token expired or invalid
          onLogout();
          break;
        case 'RATE_LIMIT_EXCEEDED':
          // Show rate limit message
          break;
        case 'NETWORK_ERROR':
          // Show network error message
          break;
        default:
          // Show generic error message
          break;
      }
    }
  }

  // Connectivity handling
  void onConnectivityChanged(bool isConnected) {
    if (isConnected) {
      // Handle online mode
      if (kDebugMode) {
        print('App is online');
      }
    } else {
      // Handle offline mode
      if (kDebugMode) {
        print('App is offline');
      }
    }
  }

  // Background task handling
  void onAppResumed() {
    // Refresh user data
    if (_authService.isAuthenticated) {
      _authService.getCurrentUser();
    }
  }

  void onAppPaused() {
    // Handle app pause
  }

  // Notification handling
  void setupNotificationListeners() {
    // Setup local notification listeners
    // This can be used for scheduled notifications or other local events
  }

  // Development helpers
  void enableDebugMode() {
    if (kDebugMode) {
      // Enable additional logging
      print('Debug mode enabled');
    }
  }
}

// Global service locator instance
final serviceLocator = ServiceLocator();
