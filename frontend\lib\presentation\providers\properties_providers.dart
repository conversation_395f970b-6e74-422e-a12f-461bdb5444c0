import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/models/property.dart';
import '../../data/repositories/property_repository.dart';
import '../../core/services/service_locator.dart';

// Property Repository Provider
final propertyRepositoryProvider = Provider<PropertyRepository>((ref) {
  return serviceLocator.propertyRepository;
});

// Properties List Provider with filtering and search
final propertiesProvider = FutureProvider.family<List<Property>, PropertiesParams>((ref, params) async {
  try {
    final repository = ref.read(propertyRepositoryProvider);
    final response = await repository.getProperties(
      page: params.page,
      limit: params.limit,
      type: params.type,
      search: params.search,
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      // Return empty list if API fails
      return [];
    }
  } catch (e) {
    // Return empty list on error for graceful degradation
    return [];
  }
});

// Property Detail Provider
final propertyDetailProvider = FutureProvider.family<PropertyDetail?, String>((ref, propertyId) async {
  try {
    final repository = ref.read(propertyRepositoryProvider);
    final response = await repository.getPropertyDetail(propertyId);

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      return null;
    }
  } catch (e) {
    return null;
  }
});

// Properties by Type Provider
final propertiesByTypeProvider = FutureProvider.family<List<Property>, String>((ref, type) async {
  try {
    final repository = ref.read(propertyRepositoryProvider);
    final response = await repository.getProperties(
      page: 1,
      limit: 100,
      type: type,
    );

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      return [];
    }
  } catch (e) {
    return [];
  }
});

// Search State Provider
final searchQueryProvider = StateProvider<String>((ref) => '');

// Filter State Provider
final selectedFilterProvider = StateProvider<String>((ref) => 'all');

// Properties Parameters Class
class PropertiesParams {
  final int page;
  final int limit;
  final String? type;
  final String? search;

  const PropertiesParams({
    this.page = 1,
    this.limit = 20,
    this.type,
    this.search,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PropertiesParams &&
          runtimeType == other.runtimeType &&
          page == other.page &&
          limit == other.limit &&
          type == other.type &&
          search == other.search;

  @override
  int get hashCode => page.hashCode ^ limit.hashCode ^ type.hashCode ^ search.hashCode;
}

// Filtered Properties Provider that combines search and filter
final filteredPropertiesProvider = FutureProvider<List<Property>>((ref) async {
  final searchQuery = ref.watch(searchQueryProvider);
  final selectedFilter = ref.watch(selectedFilterProvider);

  final params = PropertiesParams(
    page: 1,
    limit: 100,
    type: selectedFilter == 'all' ? null : selectedFilter,
    search: searchQuery.isEmpty ? null : searchQuery,
  );

  return ref.watch(propertiesProvider(params).future);
});

// Properties Loading State Provider
final propertiesLoadingProvider = Provider<bool>((ref) {
  final searchQuery = ref.watch(searchQueryProvider);
  final selectedFilter = ref.watch(selectedFilterProvider);

  final params = PropertiesParams(
    page: 1,
    limit: 100,
    type: selectedFilter == 'all' ? null : selectedFilter,
    search: searchQuery.isEmpty ? null : searchQuery,
  );

  return ref.watch(propertiesProvider(params)).isLoading;
});

// Properties Error Provider
final propertiesErrorProvider = Provider<String?>((ref) {
  final searchQuery = ref.watch(searchQueryProvider);
  final selectedFilter = ref.watch(selectedFilterProvider);

  final params = PropertiesParams(
    page: 1,
    limit: 100,
    type: selectedFilter == 'all' ? null : selectedFilter,
    search: searchQuery.isEmpty ? null : searchQuery,
  );

  final asyncValue = ref.watch(propertiesProvider(params));
  return asyncValue.hasError ? asyncValue.error.toString() : null;
});

// Property Statistics Provider
final propertyStatisticsProvider = FutureProvider<PropertyStatistics>((ref) async {
  try {
    final allProperties = await ref.watch(propertiesProvider(const PropertiesParams(page: 1, limit: 1000)).future);
    
    int totalProperties = allProperties.length;
    int activeProperties = allProperties.where((p) => p.isActive).length;
    int residentialCount = allProperties.where((p) => p.type == 'RESIDENTIAL').length;
    int officeCount = allProperties.where((p) => p.type == 'OFFICE').length;
    int constructionCount = allProperties.where((p) => p.type == 'CONSTRUCTION').length;

    return PropertyStatistics(
      totalProperties: totalProperties,
      activeProperties: activeProperties,
      residentialCount: residentialCount,
      officeCount: officeCount,
      constructionCount: constructionCount,
    );
  } catch (e) {
    return const PropertyStatistics(
      totalProperties: 0,
      activeProperties: 0,
      residentialCount: 0,
      officeCount: 0,
      constructionCount: 0,
    );
  }
});

// Property Statistics Model
class PropertyStatistics {
  final int totalProperties;
  final int activeProperties;
  final int residentialCount;
  final int officeCount;
  final int constructionCount;

  const PropertyStatistics({
    required this.totalProperties,
    required this.activeProperties,
    required this.residentialCount,
    required this.officeCount,
    required this.constructionCount,
  });

  double get activePercentage => totalProperties > 0 ? (activeProperties / totalProperties) * 100 : 0;
}
