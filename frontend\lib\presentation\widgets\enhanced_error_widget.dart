import 'package:flutter/material.dart';

class EnhancedErrorWidget extends StatelessWidget {
  final Object error;
  final StackTrace? stackTrace;
  final VoidCallback? onRetry;
  final String? title;
  final String? message;
  final IconData? icon;
  final bool showDetails;

  const EnhancedErrorWidget({
    super.key,
    required this.error,
    this.stackTrace,
    this.onRetry,
    this.title,
    this.message,
    this.icon,
    this.showDetails = false,
  });

  @override
  Widget build(BuildContext context) {
    final errorInfo = _getErrorInfo(error);
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Error Icon
            Icon(
              icon ?? errorInfo.icon,
              size: 64,
              color: errorInfo.color,
            ),
            
            const SizedBox(height: 16),
            
            // Error Title
            Text(
              title ?? errorInfo.title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: errorInfo.color,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 8),
            
            // Error Message
            Text(
              message ?? errorInfo.message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            
            if (showDetails) ...[
              const SizedBox(height: 16),
              
              // Error Details (Expandable)
              ExpansionTile(
                title: const Text('Error Details'),
                children: [
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      error.toString(),
                      style: const TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ],
            
            const SizedBox(height: 24),
            
            // Action Buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (onRetry != null) ...[
                  ElevatedButton.icon(
                    onPressed: onRetry,
                    icon: const Icon(Icons.refresh),
                    label: const Text('Retry'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: errorInfo.color,
                      foregroundColor: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 12),
                ],
                
                OutlinedButton.icon(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.arrow_back),
                  label: const Text('Go Back'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  ErrorInfo _getErrorInfo(Object error) {
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('network') || 
        errorString.contains('connection') ||
        errorString.contains('timeout')) {
      return ErrorInfo(
        title: 'Connection Error',
        message: 'Please check your internet connection and try again.',
        icon: Icons.wifi_off,
        color: Colors.orange,
      );
    }
    
    if (errorString.contains('unauthorized') || 
        errorString.contains('permission') ||
        errorString.contains('access denied')) {
      return ErrorInfo(
        title: 'Access Denied',
        message: 'You don\'t have permission to access this resource.',
        icon: Icons.lock_outline,
        color: Colors.red,
      );
    }
    
    if (errorString.contains('offline')) {
      return ErrorInfo(
        title: 'Offline',
        message: 'You\'re currently offline. Some features may not be available.',
        icon: Icons.cloud_off,
        color: Colors.grey,
      );
    }
    
    if (errorString.contains('not found') || errorString.contains('404')) {
      return ErrorInfo(
        title: 'Not Found',
        message: 'The requested resource could not be found.',
        icon: Icons.search_off,
        color: Colors.blue,
      );
    }
    
    if (errorString.contains('server') || errorString.contains('500')) {
      return ErrorInfo(
        title: 'Server Error',
        message: 'Something went wrong on our end. Please try again later.',
        icon: Icons.error_outline,
        color: Colors.red,
      );
    }
    
    // Default error
    return ErrorInfo(
      title: 'Something went wrong',
      message: 'An unexpected error occurred. Please try again.',
      icon: Icons.error_outline,
      color: Colors.red,
    );
  }
}

class ErrorInfo {
  final String title;
  final String message;
  final IconData icon;
  final Color color;

  ErrorInfo({
    required this.title,
    required this.message,
    required this.icon,
    required this.color,
  });
}

/// Specific error widgets for common scenarios
class NetworkErrorWidget extends StatelessWidget {
  final VoidCallback? onRetry;
  final String? customMessage;

  const NetworkErrorWidget({
    super.key,
    this.onRetry,
    this.customMessage,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedErrorWidget(
      error: 'Network Error',
      title: 'Connection Problem',
      message: customMessage ?? 'Please check your internet connection and try again.',
      icon: Icons.wifi_off,
      onRetry: onRetry,
    );
  }
}

class UnauthorizedErrorWidget extends StatelessWidget {
  final VoidCallback? onRetry;
  final String? customMessage;

  const UnauthorizedErrorWidget({
    super.key,
    this.onRetry,
    this.customMessage,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedErrorWidget(
      error: 'Unauthorized',
      title: 'Access Denied',
      message: customMessage ?? 'You don\'t have permission to access this resource.',
      icon: Icons.lock_outline,
      onRetry: onRetry,
    );
  }
}

class OfflineErrorWidget extends StatelessWidget {
  final VoidCallback? onRetry;
  final String? customMessage;

  const OfflineErrorWidget({
    super.key,
    this.onRetry,
    this.customMessage,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedErrorWidget(
      error: 'Offline',
      title: 'You\'re Offline',
      message: customMessage ?? 'Some features may not be available while offline.',
      icon: Icons.cloud_off,
      onRetry: onRetry,
    );
  }
}

class ServerErrorWidget extends StatelessWidget {
  final VoidCallback? onRetry;
  final String? customMessage;

  const ServerErrorWidget({
    super.key,
    this.onRetry,
    this.customMessage,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedErrorWidget(
      error: 'Server Error',
      title: 'Server Problem',
      message: customMessage ?? 'Something went wrong on our end. Please try again later.',
      icon: Icons.error_outline,
      onRetry: onRetry,
    );
  }
}

/// Error boundary widget for catching and displaying errors
class ErrorBoundary extends StatefulWidget {
  final Widget child;
  final Widget Function(Object error, StackTrace? stackTrace)? errorBuilder;
  final void Function(Object error, StackTrace? stackTrace)? onError;

  const ErrorBoundary({
    super.key,
    required this.child,
    this.errorBuilder,
    this.onError,
  });

  @override
  State<ErrorBoundary> createState() => _ErrorBoundaryState();
}

class _ErrorBoundaryState extends State<ErrorBoundary> {
  Object? _error;
  StackTrace? _stackTrace;

  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      if (widget.errorBuilder != null) {
        return widget.errorBuilder!(_error!, _stackTrace);
      }
      
      return EnhancedErrorWidget(
        error: _error!,
        stackTrace: _stackTrace,
        onRetry: () {
          setState(() {
            _error = null;
            _stackTrace = null;
          });
        },
        showDetails: true,
      );
    }

    return widget.child;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    
    // Reset error state when dependencies change
    if (_error != null) {
      setState(() {
        _error = null;
        _stackTrace = null;
      });
    }
  }

  // TODO: Uncomment when error handling is needed
  // void _handleError(Object error, StackTrace? stackTrace) {
  //   setState(() {
  //     _error = error;
  //     _stackTrace = stackTrace;
  //   });

  //   widget.onError?.call(error, stackTrace);
  // }
}
