import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import '../../data/models/user_settings.dart';
import '../../core/services/service_locator.dart';

// User Settings Provider
final userSettingsProvider = StateNotifierProvider<UserSettingsNotifier, AsyncValue<UserSettings>>((ref) {
  return UserSettingsNotifier();
});

class UserSettingsNotifier extends StateNotifier<AsyncValue<UserSettings>> {
  UserSettingsNotifier() : super(const AsyncValue.loading()) {
    _loadSettings();
  }

  static const String _settingsKey = 'user_settings';

  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_settingsKey);
      
      if (settingsJson != null) {
        final settingsMap = json.decode(settingsJson) as Map<String, dynamic>;
        final settings = UserSettings.fromJson(settingsMap);
        state = AsyncValue.data(settings);
      } else {
        // Create default settings for current user
        final currentUser = serviceLocator.authService.currentUser;
        if (currentUser != null) {
          final defaultSettings = UserSettings.defaultSettings(currentUser.id);
          await _saveSettings(defaultSettings);
          state = AsyncValue.data(defaultSettings);
        } else {
          state = AsyncValue.error('No authenticated user', StackTrace.current);
        }
      }
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> _saveSettings(UserSettings settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = json.encode(settings.toJson());
      await prefs.setString(_settingsKey, settingsJson);
    } catch (error) {
      // Handle save error
      print('Error saving settings: $error');
    }
  }

  Future<void> updateNotificationSettings(NotificationSettings notifications) async {
    final currentSettings = state.value;
    if (currentSettings != null) {
      final updatedSettings = currentSettings.copyWith(
        notifications: notifications,
        updatedAt: DateTime.now().toIso8601String(),
      );
      state = AsyncValue.data(updatedSettings);
      await _saveSettings(updatedSettings);
    }
  }

  Future<void> updateAppearanceSettings(AppearanceSettings appearance) async {
    final currentSettings = state.value;
    if (currentSettings != null) {
      final updatedSettings = currentSettings.copyWith(
        appearance: appearance,
        updatedAt: DateTime.now().toIso8601String(),
      );
      state = AsyncValue.data(updatedSettings);
      await _saveSettings(updatedSettings);
    }
  }

  Future<void> updateSecuritySettings(SecuritySettings security) async {
    final currentSettings = state.value;
    if (currentSettings != null) {
      final updatedSettings = currentSettings.copyWith(
        security: security,
        updatedAt: DateTime.now().toIso8601String(),
      );
      state = AsyncValue.data(updatedSettings);
      await _saveSettings(updatedSettings);
    }
  }

  Future<void> updatePrivacySettings(PrivacySettings privacy) async {
    final currentSettings = state.value;
    if (currentSettings != null) {
      final updatedSettings = currentSettings.copyWith(
        privacy: privacy,
        updatedAt: DateTime.now().toIso8601String(),
      );
      state = AsyncValue.data(updatedSettings);
      await _saveSettings(updatedSettings);
    }
  }

  Future<void> updateAccessibilitySettings(AccessibilitySettings accessibility) async {
    final currentSettings = state.value;
    if (currentSettings != null) {
      final updatedSettings = currentSettings.copyWith(
        accessibility: accessibility,
        updatedAt: DateTime.now().toIso8601String(),
      );
      state = AsyncValue.data(updatedSettings);
      await _saveSettings(updatedSettings);
    }
  }

  Future<void> resetToDefaults() async {
    final currentUser = serviceLocator.authService.currentUser;
    if (currentUser != null) {
      final defaultSettings = UserSettings.defaultSettings(currentUser.id);
      state = AsyncValue.data(defaultSettings);
      await _saveSettings(defaultSettings);
    }
  }

  Future<void> exportSettings() async {
    final currentSettings = state.value;
    if (currentSettings != null) {
      final settingsJson = json.encode(currentSettings.toJson());
      // TODO: Implement file export functionality
      print('Settings exported: $settingsJson');
    }
  }

  Future<void> importSettings(String settingsJson) async {
    try {
      final settingsMap = json.decode(settingsJson) as Map<String, dynamic>;
      final settings = UserSettings.fromJson(settingsMap);
      state = AsyncValue.data(settings);
      await _saveSettings(settings);
    } catch (error, stackTrace) {
      state = AsyncValue.error('Invalid settings format', stackTrace);
    }
  }
}

// Individual setting providers for easier access
final notificationSettingsProvider = Provider<NotificationSettings?>((ref) {
  final userSettings = ref.watch(userSettingsProvider);
  return userSettings.value?.notifications;
});

final appearanceSettingsProvider = Provider<AppearanceSettings?>((ref) {
  final userSettings = ref.watch(userSettingsProvider);
  return userSettings.value?.appearance;
});

final securitySettingsProvider = Provider<SecuritySettings?>((ref) {
  final userSettings = ref.watch(userSettingsProvider);
  return userSettings.value?.security;
});

final privacySettingsProvider = Provider<PrivacySettings?>((ref) {
  final userSettings = ref.watch(userSettingsProvider);
  return userSettings.value?.privacy;
});

final accessibilitySettingsProvider = Provider<AccessibilitySettings?>((ref) {
  final userSettings = ref.watch(userSettingsProvider);
  return userSettings.value?.accessibility;
});

// Theme provider based on appearance settings
final themeProvider = Provider<String>((ref) {
  final appearance = ref.watch(appearanceSettingsProvider);
  return appearance?.theme ?? 'system';
});

// Language provider based on appearance settings
final languageProvider = Provider<String>((ref) {
  final appearance = ref.watch(appearanceSettingsProvider);
  return appearance?.language ?? 'en';
});

// Timezone provider based on appearance settings
final timezoneProvider = Provider<String>((ref) {
  final appearance = ref.watch(appearanceSettingsProvider);
  return appearance?.timezone ?? 'Asia/Kolkata';
});

// Font size provider based on appearance settings
final fontSizeProvider = Provider<double>((ref) {
  final appearance = ref.watch(appearanceSettingsProvider);
  final accessibility = ref.watch(accessibilitySettingsProvider);
  
  double baseFontSize = appearance?.fontSize ?? 14.0;
  double scaling = accessibility?.textScaling ?? 1.0;
  
  return baseFontSize * scaling;
});

// Notification enabled provider
final notificationsEnabledProvider = Provider<bool>((ref) {
  final notifications = ref.watch(notificationSettingsProvider);
  return notifications?.pushNotifications ?? true;
});

// Biometric login provider
final biometricLoginProvider = Provider<bool>((ref) {
  final security = ref.watch(securitySettingsProvider);
  return security?.biometricLogin ?? false;
});

// Auto lock provider
final autoLockProvider = Provider<bool>((ref) {
  final security = ref.watch(securitySettingsProvider);
  return security?.autoLock ?? true;
});

// High contrast provider
final highContrastProvider = Provider<bool>((ref) {
  final accessibility = ref.watch(accessibilitySettingsProvider);
  return accessibility?.highContrast ?? false;
});

// Reduce motion provider
final reduceMotionProvider = Provider<bool>((ref) {
  final accessibility = ref.watch(accessibilitySettingsProvider);
  return accessibility?.reduceMotion ?? false;
});
