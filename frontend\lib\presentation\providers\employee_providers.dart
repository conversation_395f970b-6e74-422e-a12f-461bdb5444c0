import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/repositories/employee_repository.dart';
import '../../data/models/employee.dart';
import 'auth_providers.dart';
import '../../core/exceptions/app_exceptions.dart';

// Employee Repository Provider
final employeeRepositoryProvider = Provider<EmployeeRepository>((ref) {
  return EmployeeRepository();
});

// Employees Provider
final employeesProvider = FutureProvider.family<List<Employee>, EmployeeParams>((ref, params) async {
  final repository = ref.read(employeeRepositoryProvider);
  final userPermissions = ref.read(userPermissionsProvider);

  // Check RBAC permissions
  if (!userPermissions.canViewEmployees) {
    throw UnauthorizedException('Insufficient permissions to view employees');
  }

  // Check property access
  if (params.propertyId != null && 
      userPermissions.assignedPropertyIds.isNotEmpty &&
      !userPermissions.assignedPropertyIds.contains(params.propertyId!)) {
    throw UnauthorizedException('No access to view employees for this property');
  }

  try {
    final response = await repository.getEmployees(
      propertyId: params.propertyId,
      search: params.search,
      department: params.department,
      status: params.status,
      page: params.page,
      limit: params.limit,
    );

    if (response.isSuccess) {
      return response.data ?? [];
    } else {
      throw ApiException(response.message ?? 'Failed to fetch employees');
    }
  } catch (e) {
    if (e is UnauthorizedException || e is ApiException) {
      rethrow;
    }
    throw ApiException('Failed to fetch employees: ${e.toString()}');
  }
});

// Create Employee Provider
final createEmployeeProvider = FutureProvider.family<bool, CreateEmployeeParams>((ref, params) async {
  final repository = ref.read(employeeRepositoryProvider);
  final userPermissions = ref.read(userPermissionsProvider);

  // Check RBAC permissions
  if (!userPermissions.canCreateEmployees) {
    throw UnauthorizedException('Insufficient permissions to create employees');
  }

  // Check property access
  if (params.propertyId != null && 
      userPermissions.assignedPropertyIds.isNotEmpty &&
      !userPermissions.assignedPropertyIds.contains(params.propertyId!)) {
    throw UnauthorizedException('No access to create employees for this property');
  }

  try {
    final response = await repository.createEmployee(
      propertyId: params.propertyId,
      name: params.name,
      email: params.email,
      phone: params.phone,
      position: params.position,
      department: params.department,
      status: params.status,
      hireDate: params.hireDate,
      salary: params.salary,
    );

    if (response.isSuccess) {
      // Invalidate cache to refresh the list
      ref.invalidate(employeesProvider);
      return true;
    } else {
      throw ApiException(response.message ?? 'Failed to create employee');
    }
  } catch (e) {
    if (e is UnauthorizedException || e is ApiException) {
      rethrow;
    }
    throw ApiException('Failed to create employee: ${e.toString()}');
  }
});

// Update Employee Provider
final updateEmployeeProvider = FutureProvider.family<bool, UpdateEmployeeParams>((ref, params) async {
  final repository = ref.read(employeeRepositoryProvider);
  final userPermissions = ref.read(userPermissionsProvider);

  // Check RBAC permissions
  if (!userPermissions.canUpdateEmployees) {
    throw UnauthorizedException('Insufficient permissions to update employees');
  }

  // Check property access
  if (params.propertyId != null && 
      userPermissions.assignedPropertyIds.isNotEmpty &&
      !userPermissions.assignedPropertyIds.contains(params.propertyId!)) {
    throw UnauthorizedException('No access to update employees for this property');
  }

  try {
    final response = await repository.updateEmployee(
      id: params.id,
      name: params.name,
      email: params.email,
      phone: params.phone,
      position: params.position,
      department: params.department,
      status: params.status,
      salary: params.salary,
    );

    if (response.isSuccess) {
      // Invalidate cache to refresh the list
      ref.invalidate(employeesProvider);
      return true;
    } else {
      throw ApiException(response.message ?? 'Failed to update employee');
    }
  } catch (e) {
    if (e is UnauthorizedException || e is ApiException) {
      rethrow;
    }
    throw ApiException('Failed to update employee: ${e.toString()}');
  }
});

// Delete Employee Provider
final deleteEmployeeProvider = FutureProvider.family<bool, DeleteEmployeeParams>((ref, params) async {
  final repository = ref.read(employeeRepositoryProvider);
  final userPermissions = ref.read(userPermissionsProvider);

  // Check RBAC permissions - only ADMIN and MANAGER can delete
  if (!userPermissions.canDeleteEmployees) {
    throw UnauthorizedException('Insufficient permissions to delete employees');
  }

  // Check property access
  if (params.propertyId != null && 
      userPermissions.assignedPropertyIds.isNotEmpty &&
      !userPermissions.assignedPropertyIds.contains(params.propertyId!)) {
    throw UnauthorizedException('No access to delete employees for this property');
  }

  try {
    final response = await repository.deleteEmployee(params.id);

    if (response.isSuccess) {
      // Invalidate cache to refresh the list
      ref.invalidate(employeesProvider);
      return true;
    } else {
      throw ApiException(response.message ?? 'Failed to delete employee');
    }
  } catch (e) {
    if (e is UnauthorizedException || e is ApiException) {
      rethrow;
    }
    throw ApiException('Failed to delete employee: ${e.toString()}');
  }
});

// Employee Parameters
class EmployeeParams {
  final String? propertyId;
  final String? search;
  final String? department;
  final String? status;
  final int page;
  final int limit;

  const EmployeeParams({
    this.propertyId,
    this.search,
    this.department,
    this.status,
    this.page = 1,
    this.limit = 50,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is EmployeeParams &&
          runtimeType == other.runtimeType &&
          propertyId == other.propertyId &&
          search == other.search &&
          department == other.department &&
          status == other.status &&
          page == other.page &&
          limit == other.limit;

  @override
  int get hashCode =>
      propertyId.hashCode ^
      search.hashCode ^
      department.hashCode ^
      status.hashCode ^
      page.hashCode ^
      limit.hashCode;
}

// Create Employee Parameters
class CreateEmployeeParams {
  final String? propertyId;
  final String name;
  final String email;
  final String? phone;
  final String? position;
  final String department;
  final String status;
  final DateTime? hireDate;
  final double? salary;

  const CreateEmployeeParams({
    this.propertyId,
    required this.name,
    required this.email,
    this.phone,
    this.position,
    required this.department,
    required this.status,
    this.hireDate,
    this.salary,
  });
}

// Update Employee Parameters
class UpdateEmployeeParams {
  final String id;
  final String? propertyId;
  final String? name;
  final String? email;
  final String? phone;
  final String? position;
  final String? department;
  final String? status;
  final double? salary;

  const UpdateEmployeeParams({
    required this.id,
    this.propertyId,
    this.name,
    this.email,
    this.phone,
    this.position,
    this.department,
    this.status,
    this.salary,
  });
}

// Delete Employee Parameters
class DeleteEmployeeParams {
  final String id;
  final String? propertyId;

  const DeleteEmployeeParams({
    required this.id,
    this.propertyId,
  });
}
