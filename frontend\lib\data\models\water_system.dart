import 'package:json_annotation/json_annotation.dart';

part 'water_system.g.dart';

@JsonSerializable()
class WaterSystem {
  final String id;
  final String name;
  final String propertyId;
  final String type; // tank, pump, valve, meter
  final String status; // active, inactive, maintenance, error
  final Map<String, dynamic> specifications;
  final Map<String, dynamic> currentReadings;
  final String location;
  final String? description;
  final bool isActive;
  final String createdAt;
  final String updatedAt;

  const WaterSystem({
    required this.id,
    required this.name,
    required this.propertyId,
    required this.type,
    required this.status,
    required this.specifications,
    required this.currentReadings,
    required this.location,
    this.description,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory WaterSystem.fromJson(Map<String, dynamic> json) => _$WaterSystemFromJson(json);
  Map<String, dynamic> toJson() => _$WaterSystemToJson(this);

  // Factory method to create from backend API response
  factory WaterSystem.fromBackendJson(Map<String, dynamic> json) {
    return WaterSystem(
      id: json['id'] ?? '',
      name: json['tankName'] ?? json['name'] ?? '',
      propertyId: json['propertyId'] ?? '',
      type: 'tank', // Backend uses tankName, so it's a tank
      status: _mapBackendStatus(json['status']),
      specifications: {
        'capacity': json['capacity']?.toDouble() ?? 0.0,
        'pump_type': 'centrifugal',
        'max_pressure': 3.5,
        'max_flow_rate': 100.0,
      },
      currentReadings: {
        'tank_level': json['currentLevel']?.toDouble() ?? 0.0,
        'flow_rate': json['flowRate']?.toDouble() ?? 0.0,
        'pressure': json['pressure']?.toDouble() ?? 0.0,
        'pump_status': json['pumpStatus'] == 'ON',
        'temperature': json['temperature']?.toDouble() ?? 20.0,
        'ph': json['ph']?.toDouble() ?? 7.0,
        'tds': json['tds']?.toDouble() ?? 150.0,
      },
      location: json['location'] ?? 'Unknown',
      description: json['description'],
      isActive: json['isActive'] ?? true,
      createdAt: json['createdAt'] ?? DateTime.now().toIso8601String(),
      updatedAt: json['updatedAt'] ?? DateTime.now().toIso8601String(),
    );
  }

  static String _mapBackendStatus(String? backendStatus) {
    switch (backendStatus?.toUpperCase()) {
      case 'OPERATIONAL':
        return 'active';
      case 'MAINTENANCE':
        return 'maintenance';
      case 'ERROR':
      case 'FAULT':
        return 'error';
      case 'OFFLINE':
        return 'inactive';
      default:
        return 'active';
    }
  }

  WaterSystem copyWith({
    String? id,
    String? name,
    String? propertyId,
    String? type,
    String? status,
    Map<String, dynamic>? specifications,
    Map<String, dynamic>? currentReadings,
    String? location,
    String? description,
    bool? isActive,
    String? createdAt,
    String? updatedAt,
  }) {
    return WaterSystem(
      id: id ?? this.id,
      name: name ?? this.name,
      propertyId: propertyId ?? this.propertyId,
      type: type ?? this.type,
      status: status ?? this.status,
      specifications: specifications ?? this.specifications,
      currentReadings: currentReadings ?? this.currentReadings,
      location: location ?? this.location,
      description: description ?? this.description,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper getters
  DateTime get createdDateTime => DateTime.parse(createdAt);
  DateTime get updatedDateTime => DateTime.parse(updatedAt);

  // Water system specific getters
  double? get tankLevel => currentReadings['tank_level']?.toDouble();
  double? get flowRate => currentReadings['flow_rate']?.toDouble();
  double? get pressure => currentReadings['pressure']?.toDouble();
  bool? get pumpStatus => currentReadings['pump_status'];
  double? get temperature => currentReadings['temperature']?.toDouble();
  double? get ph => currentReadings['ph']?.toDouble();
  double? get tds => currentReadings['tds']?.toDouble(); // Total Dissolved Solids

  // Specification getters
  double? get capacity => specifications['capacity']?.toDouble();
  String? get pumpType => specifications['pump_type'];
  double? get maxPressure => specifications['max_pressure']?.toDouble();
  double? get maxFlowRate => specifications['max_flow_rate']?.toDouble();

  // Status helpers
  bool get isOperational => status == 'active';
  bool get isInMaintenance => status == 'maintenance';
  bool get hasError => status == 'error';
  bool get isInactive => status == 'inactive';

  // Alert conditions
  bool get isLowLevel => tankLevel != null && tankLevel! < 20.0;
  bool get isHighLevel => tankLevel != null && tankLevel! > 95.0;
  bool get isLowPressure => pressure != null && pressure! < 1.0;
  bool get isHighPressure => pressure != null && maxPressure != null && pressure! > maxPressure! * 0.9;
  bool get isPumpRunning => pumpStatus == true;

  // Display helpers
  String get statusDisplay {
    switch (status) {
      case 'active':
        return 'Active';
      case 'inactive':
        return 'Inactive';
      case 'maintenance':
        return 'Maintenance';
      case 'error':
        return 'Error';
      default:
        return status.toUpperCase();
    }
  }

  String get typeDisplay {
    switch (type) {
      case 'tank':
        return 'Water Tank';
      case 'pump':
        return 'Water Pump';
      case 'valve':
        return 'Water Valve';
      case 'meter':
        return 'Water Meter';
      default:
        return type.toUpperCase();
    }
  }

  String get tankLevelDisplay {
    if (tankLevel == null) return 'N/A';
    return '${tankLevel!.toStringAsFixed(1)}%';
  }

  String get flowRateDisplay {
    if (flowRate == null) return 'N/A';
    return '${flowRate!.toStringAsFixed(1)} L/min';
  }

  String get pressureDisplay {
    if (pressure == null) return 'N/A';
    return '${pressure!.toStringAsFixed(1)} bar';
  }

  String get pumpStatusDisplay {
    if (pumpStatus == null) return 'N/A';
    return pumpStatus! ? 'Running' : 'Stopped';
  }

  String get temperatureDisplay {
    if (temperature == null) return 'N/A';
    return '${temperature!.toStringAsFixed(1)}°C';
  }

  String get phDisplay {
    if (ph == null) return 'N/A';
    return ph!.toStringAsFixed(1);
  }

  String get tdsDisplay {
    if (tds == null) return 'N/A';
    return '${tds!.toStringAsFixed(0)} ppm';
  }

  String get capacityDisplay {
    if (capacity == null) return 'N/A';
    return '${capacity!.toStringAsFixed(0)} L';
  }

  // Alert messages
  List<String> get alerts {
    final alerts = <String>[];
    
    if (hasError) {
      alerts.add('System Error');
    }
    
    if (isLowLevel) {
      alerts.add('Low Water Level');
    }
    
    if (isHighLevel) {
      alerts.add('High Water Level');
    }
    
    if (isLowPressure) {
      alerts.add('Low Pressure');
    }
    
    if (isHighPressure) {
      alerts.add('High Pressure');
    }
    
    if (isInMaintenance) {
      alerts.add('Under Maintenance');
    }
    
    return alerts;
  }

  bool get hasAlerts => alerts.isNotEmpty;

  // Factory for creating test/demo data
  factory WaterSystem.demo({
    required String id,
    required String name,
    required String propertyId,
    String type = 'tank',
    String status = 'active',
  }) {
    final now = DateTime.now().toIso8601String();
    return WaterSystem(
      id: id,
      name: name,
      propertyId: propertyId,
      type: type,
      status: status,
      specifications: {
        'capacity': 5000.0,
        'pump_type': 'centrifugal',
        'max_pressure': 3.5,
        'max_flow_rate': 100.0,
      },
      currentReadings: {
        'tank_level': 75.5,
        'flow_rate': 45.2,
        'pressure': 2.1,
        'pump_status': true,
        'temperature': 22.5,
        'ph': 7.2,
        'tds': 150.0,
      },
      location: 'Ground Floor',
      description: 'Main water supply system',
      isActive: true,
      createdAt: now,
      updatedAt: now,
    );
  }
}
