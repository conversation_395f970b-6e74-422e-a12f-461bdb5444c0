{"valid_import": true, "imports": [{"uri": "package:flutter/material.dart", "transitive": false}, {"uri": "package:go_router/go_router.dart", "transitive": false}, {"uri": "package:provider/provider.dart", "transitive": false}, {"uri": "package:frontend/core/constants/app_constants.dart", "transitive": false}, {"uri": "package:frontend/core/theme/app_theme.dart", "transitive": false}, {"uri": "package:frontend/data/models/user_model.dart", "transitive": false}, {"uri": "package:frontend/presentation/providers/auth_provider.dart", "transitive": false}, {"uri": "package:frontend/presentation/routes/app_router.dart", "transitive": false}], "elements": []}