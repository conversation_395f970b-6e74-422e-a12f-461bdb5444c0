/// Base exception class for the application
abstract class AppException implements Exception {
  final String message;
  final String? code;
  final dynamic details;

  const AppException(this.message, {this.code, this.details});

  @override
  String toString() => message;
}

/// Exception thrown when user is not authorized
class UnauthorizedException extends AppException {
  const UnauthorizedException(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);
}

/// Exception thrown when user is forbidden from accessing a resource
class ForbiddenException extends AppException {
  const ForbiddenException(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);
}

/// Exception thrown when a resource is not found
class NotFoundException extends AppException {
  const NotFoundException(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);
}

/// Exception thrown when there's a validation error
class ValidationException extends AppException {
  const ValidationException(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);
}

/// Exception thrown when there's an API error
class ApiException extends AppException {
  const ApiException(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);
}

/// Exception thrown when there's a network error
class NetworkException extends AppException {
  const NetworkException(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);
}

/// Exception thrown when there's a timeout
class TimeoutException extends AppException {
  const TimeoutException(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);
}

/// Exception thrown when there's a server error
class ServerException extends AppException {
  const ServerException(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);
}

/// Exception thrown when there's a cache error
class CacheException extends AppException {
  const CacheException(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);
}

/// Exception thrown when there's a storage error
class StorageException extends AppException {
  const StorageException(String message, {String? code, dynamic details})
      : super(message, code: code, details: details);
}
