"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.corsErrorHandler = exports.rateLimitHandler = exports.validateRequest = exports.asyncHandler = exports.notFoundHandler = exports.errorHandler = exports.AppError = void 0;
const zod_1 = require("zod");
const client_1 = require("@prisma/client");
class AppError extends Error {
    constructor(message, statusCode = 500, code) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = true;
        this.code = code;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.AppError = AppError;
const errorHandler = (error, req, res, next) => {
    let statusCode = 500;
    let errorCode = 'INTERNAL_SERVER_ERROR';
    let message = 'An unexpected error occurred';
    let details = undefined;
    // Handle different types of errors
    if (error instanceof AppError) {
        statusCode = error.statusCode;
        errorCode = error.code || 'APPLICATION_ERROR';
        message = error.message;
    }
    else if (error instanceof zod_1.ZodError) {
        statusCode = 400;
        errorCode = 'VALIDATION_ERROR';
        message = 'Invalid input data';
        details = {
            issues: error.issues.map(issue => ({
                field: issue.path.join('.'),
                message: issue.message,
                code: issue.code,
            })),
        };
    }
    else if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
        statusCode = 400;
        errorCode = 'DATABASE_ERROR';
        switch (error.code) {
            case 'P2002':
                message = 'A record with this information already exists';
                details = { field: error.meta?.target };
                break;
            case 'P2025':
                statusCode = 404;
                message = 'Record not found';
                break;
            case 'P2003':
                message = 'Invalid reference to related record';
                break;
            case 'P2014':
                message = 'Invalid ID provided';
                break;
            default:
                message = 'Database operation failed';
        }
    }
    else if (error instanceof client_1.Prisma.PrismaClientUnknownRequestError) {
        statusCode = 500;
        errorCode = 'DATABASE_ERROR';
        message = 'Unknown database error occurred';
    }
    else if (error instanceof client_1.Prisma.PrismaClientRustPanicError) {
        statusCode = 500;
        errorCode = 'DATABASE_ERROR';
        message = 'Database engine error';
    }
    else if (error instanceof client_1.Prisma.PrismaClientInitializationError) {
        statusCode = 500;
        errorCode = 'DATABASE_CONNECTION_ERROR';
        message = 'Failed to connect to database';
    }
    else if (error instanceof client_1.Prisma.PrismaClientValidationError) {
        statusCode = 400;
        errorCode = 'DATABASE_VALIDATION_ERROR';
        message = 'Invalid data provided to database';
    }
    else if (error.name === 'JsonWebTokenError') {
        statusCode = 401;
        errorCode = 'INVALID_TOKEN';
        message = 'Invalid authentication token';
    }
    else if (error.name === 'TokenExpiredError') {
        statusCode = 401;
        errorCode = 'TOKEN_EXPIRED';
        message = 'Authentication token has expired';
    }
    else if (error.name === 'NotBeforeError') {
        statusCode = 401;
        errorCode = 'TOKEN_NOT_ACTIVE';
        message = 'Authentication token is not active yet';
    }
    else if (error.name === 'MulterError') {
        statusCode = 400;
        errorCode = 'FILE_UPLOAD_ERROR';
        switch (error.code) {
            case 'LIMIT_FILE_SIZE':
                message = 'File size too large';
                break;
            case 'LIMIT_FILE_COUNT':
                message = 'Too many files uploaded';
                break;
            case 'LIMIT_UNEXPECTED_FILE':
                message = 'Unexpected file field';
                break;
            default:
                message = 'File upload failed';
        }
    }
    // Log error for debugging (exclude validation errors in production)
    if (process.env.NODE_ENV !== 'production' || statusCode >= 500) {
        console.error('Error occurred:', {
            message: error.message,
            stack: error.stack,
            statusCode,
            errorCode,
            path: req.path,
            method: req.method,
            timestamp: new Date().toISOString(),
        });
    }
    const errorResponse = {
        error: errorCode,
        message,
        timestamp: new Date().toISOString(),
        path: req.path,
        ...(details && { details }),
    };
    res.status(statusCode).json(errorResponse);
};
exports.errorHandler = errorHandler;
const notFoundHandler = (req, res, next) => {
    const error = new AppError(`Route ${req.method} ${req.path} not found`, 404, 'NOT_FOUND');
    next(error);
};
exports.notFoundHandler = notFoundHandler;
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
// Validation middleware
const validateRequest = (schema, source = 'body') => {
    return (req, res, next) => {
        try {
            const data = req[source];
            const validatedData = schema.parse(data);
            req[source] = validatedData;
            next();
        }
        catch (error) {
            next(error);
        }
    };
};
exports.validateRequest = validateRequest;
// Rate limit error handler
const rateLimitHandler = (req, res, next, retryAfter = 60) => {
    const errorResponse = {
        error: 'RATE_LIMIT_EXCEEDED',
        message: 'Too many requests. Please try again later.',
        timestamp: new Date().toISOString(),
        path: req.path,
        retryAfter,
    };
    res.status(429).json(errorResponse);
};
exports.rateLimitHandler = rateLimitHandler;
// CORS error handler
const corsErrorHandler = (req, res, next) => {
    const errorResponse = {
        error: 'CORS_ERROR',
        message: 'Cross-origin request blocked',
        timestamp: new Date().toISOString(),
        path: req.path,
    };
    res.status(403).json(errorResponse);
};
exports.corsErrorHandler = corsErrorHandler;
