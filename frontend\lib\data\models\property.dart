import 'package:json_annotation/json_annotation.dart';

part 'property.g.dart';

@JsonSerializable()
class Property {
  final String id;
  final String name;
  final String type; // residential, office, construction
  final String address;
  final String city;
  final String state;
  final String zipCode;
  final String country;
  final String? description;
  final int? totalUnits;
  final int? occupiedUnits;
  final double? monthlyRent;
  final double? propertyValue;
  final String? managerId;
  final String status;
  final List<String>? amenities;
  final List<String>? images;
  final Map<String, double>? coordinates;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<SystemStatus> systemStatuses;
  final PropertySettings? settings;

  const Property({
    required this.id,
    required this.name,
    required this.type,
    required this.address,
    required this.city,
    required this.state,
    required this.zipCode,
    required this.country,
    this.description,
    this.totalUnits,
    this.occupiedUnits,
    this.monthlyRent,
    this.propertyValue,
    this.managerId,
    required this.status,
    this.amenities,
    this.images,
    this.coordinates,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    required this.systemStatuses,
    this.settings,
  });

  factory Property.fromJson(Map<String, dynamic> json) => _$PropertyFromJson(json);
  Map<String, dynamic> toJson() => _$PropertyToJson(this);

  Property copyWith({
    String? id,
    String? name,
    String? type,
    String? address,
    String? city,
    String? state,
    String? zipCode,
    String? country,
    String? description,
    int? totalUnits,
    int? occupiedUnits,
    double? monthlyRent,
    double? propertyValue,
    String? managerId,
    String? status,
    List<String>? amenities,
    List<String>? images,
    Map<String, double>? coordinates,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<SystemStatus>? systemStatuses,
    PropertySettings? settings,
  }) {
    return Property(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      zipCode: zipCode ?? this.zipCode,
      country: country ?? this.country,
      description: description ?? this.description,
      totalUnits: totalUnits ?? this.totalUnits,
      occupiedUnits: occupiedUnits ?? this.occupiedUnits,
      monthlyRent: monthlyRent ?? this.monthlyRent,
      propertyValue: propertyValue ?? this.propertyValue,
      managerId: managerId ?? this.managerId,
      status: status ?? this.status,
      amenities: amenities ?? this.amenities,
      images: images ?? this.images,
      coordinates: coordinates ?? this.coordinates,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      systemStatuses: systemStatuses ?? this.systemStatuses,
      settings: settings ?? this.settings,
    );
  }
}

@JsonSerializable()
class SystemStatus {
  final String id;
  final String propertyId;
  final String systemType; // water, electricity, security, internet, ott, maintenance
  final String status; // operational, warning, critical, offline
  final String? description;
  final DateTime lastUpdated;
  final Map<String, dynamic>? metadata;

  const SystemStatus({
    required this.id,
    required this.propertyId,
    required this.systemType,
    required this.status,
    this.description,
    required this.lastUpdated,
    this.metadata,
  });

  factory SystemStatus.fromJson(Map<String, dynamic> json) => _$SystemStatusFromJson(json);
  Map<String, dynamic> toJson() => _$SystemStatusToJson(this);

  SystemStatus copyWith({
    String? id,
    String? propertyId,
    String? systemType,
    String? status,
    String? description,
    DateTime? lastUpdated,
    Map<String, dynamic>? metadata,
  }) {
    return SystemStatus(
      id: id ?? this.id,
      propertyId: propertyId ?? this.propertyId,
      systemType: systemType ?? this.systemType,
      status: status ?? this.status,
      description: description ?? this.description,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      metadata: metadata ?? this.metadata,
    );
  }
}

@JsonSerializable()
class PropertySettings {
  final String propertyId;
  final Map<String, dynamic> waterSettings;
  final Map<String, dynamic> electricitySettings;
  final Map<String, dynamic> securitySettings;
  final Map<String, dynamic> internetSettings;
  final Map<String, dynamic> ottSettings;
  final Map<String, dynamic> maintenanceSettings;

  const PropertySettings({
    required this.propertyId,
    required this.waterSettings,
    required this.electricitySettings,
    required this.securitySettings,
    required this.internetSettings,
    required this.ottSettings,
    required this.maintenanceSettings,
  });

  factory PropertySettings.fromJson(Map<String, dynamic> json) => _$PropertySettingsFromJson(json);
  Map<String, dynamic> toJson() => _$PropertySettingsToJson(this);

  PropertySettings copyWith({
    String? propertyId,
    Map<String, dynamic>? waterSettings,
    Map<String, dynamic>? electricitySettings,
    Map<String, dynamic>? securitySettings,
    Map<String, dynamic>? internetSettings,
    Map<String, dynamic>? ottSettings,
    Map<String, dynamic>? maintenanceSettings,
  }) {
    return PropertySettings(
      propertyId: propertyId ?? this.propertyId,
      waterSettings: waterSettings ?? this.waterSettings,
      electricitySettings: electricitySettings ?? this.electricitySettings,
      securitySettings: securitySettings ?? this.securitySettings,
      internetSettings: internetSettings ?? this.internetSettings,
      ottSettings: ottSettings ?? this.ottSettings,
      maintenanceSettings: maintenanceSettings ?? this.maintenanceSettings,
    );
  }
}

@JsonSerializable()
class WaterSystem {
  final String id;
  final String propertyId;
  final List<String> municipalConnections;
  final BorewellInfo? borewellInfo;
  final WaterTankInfo tankInfo;
  final AutomationSystem? automationSystem;
  final List<ContactInfo> contacts;

  const WaterSystem({
    required this.id,
    required this.propertyId,
    required this.municipalConnections,
    this.borewellInfo,
    required this.tankInfo,
    this.automationSystem,
    required this.contacts,
  });

  factory WaterSystem.fromJson(Map<String, dynamic> json) => _$WaterSystemFromJson(json);
  Map<String, dynamic> toJson() => _$WaterSystemToJson(this);
}

@JsonSerializable()
class BorewellInfo {
  final String id;
  final double depth;
  final double waterLevel;
  final String quality;
  final DateTime lastTested;

  const BorewellInfo({
    required this.id,
    required this.depth,
    required this.waterLevel,
    required this.quality,
    required this.lastTested,
  });

  factory BorewellInfo.fromJson(Map<String, dynamic> json) => _$BorewellInfoFromJson(json);
  Map<String, dynamic> toJson() => _$BorewellInfoToJson(this);
}

@JsonSerializable()
class WaterTankInfo {
  final String id;
  final double capacity;
  final double currentLevel;
  final String material;
  final DateTime lastCleaned;

  const WaterTankInfo({
    required this.id,
    required this.capacity,
    required this.currentLevel,
    required this.material,
    required this.lastCleaned,
  });

  factory WaterTankInfo.fromJson(Map<String, dynamic> json) => _$WaterTankInfoFromJson(json);
  Map<String, dynamic> toJson() => _$WaterTankInfoToJson(this);
}

@JsonSerializable()
class AutomationSystem {
  final String id;
  final String brand;
  final String model;
  final bool isActive;
  final DateTime lastMaintenance;

  const AutomationSystem({
    required this.id,
    required this.brand,
    required this.model,
    required this.isActive,
    required this.lastMaintenance,
  });

  factory AutomationSystem.fromJson(Map<String, dynamic> json) => _$AutomationSystemFromJson(json);
  Map<String, dynamic> toJson() => _$AutomationSystemToJson(this);
}

@JsonSerializable()
class ContactInfo {
  final String id;
  final String name;
  final String phone;
  final String? email;
  final String role;
  final String category;

  const ContactInfo({
    required this.id,
    required this.name,
    required this.phone,
    this.email,
    required this.role,
    required this.category,
  });

  factory ContactInfo.fromJson(Map<String, dynamic> json) => _$ContactInfoFromJson(json);
  Map<String, dynamic> toJson() => _$ContactInfoToJson(this);
}
