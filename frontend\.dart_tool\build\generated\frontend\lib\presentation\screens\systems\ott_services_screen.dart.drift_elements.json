{"valid_import": true, "imports": [{"uri": "package:flutter/material.dart", "transitive": false}, {"uri": "package:flutter_riverpod/flutter_riverpod.dart", "transitive": false}, {"uri": "package:frontend/core/services/ott_service.dart", "transitive": false}, {"uri": "package:frontend/data/models/ott_service.dart", "transitive": false}, {"uri": "package:frontend/core/constants/api_constants.dart", "transitive": false}, {"uri": "package:frontend/presentation/screens/main/main_navigation_screen.dart", "transitive": false}], "elements": []}