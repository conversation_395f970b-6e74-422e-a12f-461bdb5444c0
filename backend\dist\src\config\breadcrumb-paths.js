"use strict";
// Comprehensive Breadcrumb Path Definitions
Object.defineProperty(exports, "__esModule", { value: true });
exports.BREADCRUMB_PATHS = void 0;
exports.BREADCRUMB_PATHS = [
    // Root Level
    {
        path: '/dashboard',
        name: 'Dashboard',
        description: 'Main dashboard overview',
        level: 0,
        components: [
            { componentId: 'dashboard.overview_cards', name: 'Overview Cards', type: 'card', permissions: ['view'] },
            { componentId: 'dashboard.property_summary', name: 'Property Summary', type: 'widget', permissions: ['view'] },
            { componentId: 'dashboard.recent_activities', name: 'Recent Activities', type: 'table', permissions: ['view'] },
            { componentId: 'dashboard.financial_overview', name: 'Financial Overview', type: 'chart', permissions: ['view_financial'] },
        ]
    },
    // Properties Level
    {
        path: '/properties',
        name: 'Properties',
        description: 'Property management overview',
        level: 0,
        components: [
            { componentId: 'properties.list', name: 'Property List', type: 'table', permissions: ['view'] },
            { componentId: 'properties.add_button', name: 'Add Property', type: 'button', permissions: ['create'] },
            { componentId: 'properties.filters', name: 'Property Filters', type: 'form', permissions: ['view'] },
        ]
    },
    {
        path: '/properties/{propertyId}',
        name: 'Property Details',
        description: 'Individual property management',
        level: 1,
        parentPath: '/properties',
        components: [
            { componentId: 'property.overview', name: 'Property Overview', type: 'card', permissions: ['view'] },
            { componentId: 'property.edit_button', name: 'Edit Property', type: 'button', permissions: ['update'] },
            { componentId: 'property.financial_summary', name: 'Financial Summary', type: 'widget', permissions: ['view_financial'] },
            { componentId: 'property.system_status', name: 'System Status', type: 'widget', permissions: ['view'] },
        ]
    },
    // Property Systems Level
    {
        path: '/properties/{propertyId}/systems',
        name: 'Systems Management',
        description: 'Property systems overview',
        level: 2,
        parentPath: '/properties/{propertyId}',
        components: [
            { componentId: 'systems.overview_grid', name: 'Systems Grid', type: 'widget', permissions: ['view'] },
            { componentId: 'systems.status_indicators', name: 'Status Indicators', type: 'display', permissions: ['view'] },
        ]
    },
    // Security System Paths
    {
        path: '/properties/{propertyId}/systems/security',
        name: 'Security Management',
        description: 'Security system management',
        level: 3,
        parentPath: '/properties/{propertyId}/systems',
        components: [
            { componentId: 'security.overview', name: 'Security Overview', type: 'card', permissions: ['view'] },
            { componentId: 'security.system_status', name: 'System Status', type: 'widget', permissions: ['view'] },
            { componentId: 'security.recent_alerts', name: 'Recent Alerts', type: 'table', permissions: ['view'] },
        ]
    },
    {
        path: '/properties/{propertyId}/systems/security/cctv',
        name: 'CCTV Management',
        description: 'CCTV camera management and monitoring',
        level: 4,
        parentPath: '/properties/{propertyId}/systems/security',
        components: [
            { componentId: 'cctv.camera_grid', name: 'Camera Grid', type: 'widget', permissions: ['view'] },
            { componentId: 'cctv.live_feeds', name: 'Live Feeds', type: 'widget', permissions: ['view_live'] },
            { componentId: 'cctv.recordings', name: 'Recordings', type: 'table', permissions: ['view_recordings'] },
            { componentId: 'cctv.camera_controls', name: 'Camera Controls', type: 'form', permissions: ['control'] },
            { componentId: 'cctv.add_camera', name: 'Add Camera', type: 'button', permissions: ['create'] },
            { componentId: 'cctv.settings', name: 'CCTV Settings', type: 'form', permissions: ['configure'] },
        ]
    },
    {
        path: '/properties/{propertyId}/systems/security/cctv/camera/{cameraId}',
        name: 'Camera Details',
        description: 'Individual camera management',
        level: 5,
        parentPath: '/properties/{propertyId}/systems/security/cctv',
        components: [
            { componentId: 'camera.live_view', name: 'Live View', type: 'widget', permissions: ['view_live'] },
            { componentId: 'camera.controls', name: 'Camera Controls', type: 'form', permissions: ['control'] },
            { componentId: 'camera.settings', name: 'Camera Settings', type: 'form', permissions: ['configure'] },
            { componentId: 'camera.recordings', name: 'Camera Recordings', type: 'table', permissions: ['view_recordings'] },
        ]
    },
    {
        path: '/properties/{propertyId}/systems/security/access-control',
        name: 'Access Control',
        description: 'Access control system management',
        level: 4,
        parentPath: '/properties/{propertyId}/systems/security',
        components: [
            { componentId: 'access.system_status', name: 'System Status', type: 'widget', permissions: ['view'] },
            { componentId: 'access.user_management', name: 'User Management', type: 'table', permissions: ['manage_users'] },
            { componentId: 'access.access_logs', name: 'Access Logs', type: 'table', permissions: ['view_logs'] },
            { componentId: 'access.emergency_override', name: 'Emergency Override', type: 'button', permissions: ['emergency_override'] },
            { componentId: 'access.add_user', name: 'Add User', type: 'button', permissions: ['create_user'] },
        ]
    },
    {
        path: '/properties/{propertyId}/systems/security/access-control/users',
        name: 'Access Users',
        description: 'Access control user management',
        level: 5,
        parentPath: '/properties/{propertyId}/systems/security/access-control',
        components: [
            { componentId: 'access_users.list', name: 'User List', type: 'table', permissions: ['view'] },
            { componentId: 'access_users.add_form', name: 'Add User Form', type: 'form', permissions: ['create'] },
            { componentId: 'access_users.bulk_actions', name: 'Bulk Actions', type: 'button', permissions: ['bulk_manage'] },
        ]
    },
    {
        path: '/properties/{propertyId}/systems/security/maintenance',
        name: 'Security Maintenance',
        description: 'Security system maintenance',
        level: 4,
        parentPath: '/properties/{propertyId}/systems/security',
        components: [
            { componentId: 'security_maintenance.schedule', name: 'Maintenance Schedule', type: 'table', permissions: ['view'] },
            { componentId: 'security_maintenance.history', name: 'Maintenance History', type: 'table', permissions: ['view'] },
            { componentId: 'security_maintenance.create_task', name: 'Create Task', type: 'button', permissions: ['create'] },
            { componentId: 'security_maintenance.vendor_contacts', name: 'Vendor Contacts', type: 'table', permissions: ['view_vendors'] },
        ]
    },
    // Electricity System Paths
    {
        path: '/properties/{propertyId}/systems/electricity',
        name: 'Electricity Management',
        description: 'Electrical system management',
        level: 3,
        parentPath: '/properties/{propertyId}/systems',
        components: [
            { componentId: 'electricity.overview', name: 'Electricity Overview', type: 'card', permissions: ['view'] },
            { componentId: 'electricity.power_status', name: 'Power Status', type: 'widget', permissions: ['view'] },
            { componentId: 'electricity.consumption', name: 'Power Consumption', type: 'chart', permissions: ['view'] },
        ]
    },
    {
        path: '/properties/{propertyId}/systems/electricity/generator',
        name: 'Generator Management',
        description: 'Generator monitoring and control',
        level: 4,
        parentPath: '/properties/{propertyId}/systems/electricity',
        components: [
            { componentId: 'generator.status', name: 'Generator Status', type: 'widget', permissions: ['view'] },
            { componentId: 'generator.fuel_level', name: 'Fuel Level', type: 'display', permissions: ['view'] },
            { componentId: 'generator.update_fuel', name: 'Update Fuel', type: 'form', permissions: ['update_fuel'] },
            { componentId: 'generator.start_stop', name: 'Start/Stop Generator', type: 'button', permissions: ['control'] },
            { componentId: 'generator.maintenance', name: 'Generator Maintenance', type: 'table', permissions: ['view_maintenance'] },
        ]
    },
    {
        path: '/properties/{propertyId}/systems/electricity/ups',
        name: 'UPS Management',
        description: 'UPS monitoring and management',
        level: 4,
        parentPath: '/properties/{propertyId}/systems/electricity',
        components: [
            { componentId: 'ups.status', name: 'UPS Status', type: 'widget', permissions: ['view'] },
            { componentId: 'ups.battery_status', name: 'Battery Status', type: 'display', permissions: ['view'] },
            { componentId: 'ups.diagnostics', name: 'Run Diagnostics', type: 'button', permissions: ['diagnostics'] },
            { componentId: 'ups.settings', name: 'UPS Settings', type: 'form', permissions: ['configure'] },
        ]
    },
    // Water System Paths
    {
        path: '/properties/{propertyId}/systems/water',
        name: 'Water Management',
        description: 'Water system management',
        level: 3,
        parentPath: '/properties/{propertyId}/systems',
        components: [
            { componentId: 'water.overview', name: 'Water Overview', type: 'card', permissions: ['view'] },
            { componentId: 'water.tank_levels', name: 'Tank Levels', type: 'widget', permissions: ['view'] },
            { componentId: 'water.pump_status', name: 'Pump Status', type: 'widget', permissions: ['view'] },
        ]
    },
    {
        path: '/properties/{propertyId}/systems/water/tanks',
        name: 'Tank Monitoring',
        description: 'Water tank monitoring',
        level: 4,
        parentPath: '/properties/{propertyId}/systems/water',
        components: [
            { componentId: 'tanks.level_indicators', name: 'Level Indicators', type: 'widget', permissions: ['view'] },
            { componentId: 'tanks.cleaning_schedule', name: 'Cleaning Schedule', type: 'table', permissions: ['view'] },
            { componentId: 'tanks.quality_reports', name: 'Quality Reports', type: 'table', permissions: ['view_quality'] },
        ]
    },
    {
        path: '/properties/{propertyId}/systems/water/pumps',
        name: 'Pump Control',
        description: 'Water pump control and monitoring',
        level: 4,
        parentPath: '/properties/{propertyId}/systems/water',
        components: [
            { componentId: 'pumps.status_grid', name: 'Pump Status Grid', type: 'widget', permissions: ['view'] },
            { componentId: 'pumps.controls', name: 'Pump Controls', type: 'form', permissions: ['control'] },
            { componentId: 'pumps.maintenance', name: 'Pump Maintenance', type: 'table', permissions: ['view_maintenance'] },
        ]
    },
    // Office Management Paths
    {
        path: '/office',
        name: 'Office Management',
        description: 'Office operations management',
        level: 0,
        components: [
            { componentId: 'office.overview', name: 'Office Overview', type: 'card', permissions: ['view'] },
            { componentId: 'office.locations', name: 'Office Locations', type: 'table', permissions: ['view'] },
        ]
    },
    {
        path: '/office/attendance',
        name: 'Attendance Management',
        description: 'Employee attendance tracking',
        level: 1,
        parentPath: '/office',
        components: [
            { componentId: 'attendance.daily_summary', name: 'Daily Summary', type: 'widget', permissions: ['view'] },
            { componentId: 'attendance.employee_list', name: 'Employee List', type: 'table', permissions: ['view'] },
            { componentId: 'attendance.mark_attendance', name: 'Mark Attendance', type: 'button', permissions: ['mark'] },
            { componentId: 'attendance.reports', name: 'Attendance Reports', type: 'table', permissions: ['view_reports'] },
        ]
    },
    {
        path: '/office/employees',
        name: 'Employee Management',
        description: 'Employee information management',
        level: 1,
        parentPath: '/office',
        components: [
            { componentId: 'employees.list', name: 'Employee List', type: 'table', permissions: ['view'] },
            { componentId: 'employees.add_employee', name: 'Add Employee', type: 'button', permissions: ['create'] },
            { componentId: 'employees.employee_details', name: 'Employee Details', type: 'form', permissions: ['view_details'] },
            { componentId: 'employees.salary_info', name: 'Salary Information', type: 'display', permissions: ['view_salary'] },
        ]
    },
    // Maintenance Paths
    {
        path: '/maintenance',
        name: 'Maintenance Management',
        description: 'Overall maintenance management',
        level: 0,
        components: [
            { componentId: 'maintenance.overview', name: 'Maintenance Overview', type: 'card', permissions: ['view'] },
            { componentId: 'maintenance.pending_issues', name: 'Pending Issues', type: 'table', permissions: ['view'] },
        ]
    },
    {
        path: '/maintenance/issues',
        name: 'Maintenance Issues',
        description: 'Maintenance issue tracking',
        level: 1,
        parentPath: '/maintenance',
        components: [
            { componentId: 'issues.list', name: 'Issue List', type: 'table', permissions: ['view'] },
            { componentId: 'issues.create_issue', name: 'Create Issue', type: 'button', permissions: ['create'] },
            { componentId: 'issues.assign_technician', name: 'Assign Technician', type: 'form', permissions: ['assign'] },
            { componentId: 'issues.cost_tracking', name: 'Cost Tracking', type: 'display', permissions: ['view_costs'] },
        ]
    },
    {
        path: '/maintenance/schedule',
        name: 'Maintenance Schedule',
        description: 'Preventive maintenance scheduling',
        level: 1,
        parentPath: '/maintenance',
        components: [
            { componentId: 'schedule.calendar', name: 'Maintenance Calendar', type: 'widget', permissions: ['view'] },
            { componentId: 'schedule.create_task', name: 'Create Task', type: 'button', permissions: ['create'] },
            { componentId: 'schedule.recurring_tasks', name: 'Recurring Tasks', type: 'table', permissions: ['view'] },
        ]
    },
    // OTT Services Paths
    {
        path: '/properties/{propertyId}/systems/ott',
        name: 'OTT Services',
        description: 'OTT platform management',
        level: 3,
        parentPath: '/properties/{propertyId}/systems',
        components: [
            { componentId: 'ott.service_list', name: 'Service List', type: 'table', permissions: ['view'] },
            { componentId: 'ott.add_service', name: 'Add Service', type: 'button', permissions: ['create'] },
            { componentId: 'ott.subscription_status', name: 'Subscription Status', type: 'widget', permissions: ['view'] },
            { componentId: 'ott.billing_info', name: 'Billing Information', type: 'display', permissions: ['view_billing'] },
        ]
    },
];
