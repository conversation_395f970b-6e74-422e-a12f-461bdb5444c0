import { Response } from 'express';
import { AuthenticatedRequest } from '../middleware/auth';
export declare const getDashboardOverview: (req: AuthenticatedRequest, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const getDashboardMetrics: (req: AuthenticatedRequest, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const updateDashboardMetrics: (req: AuthenticatedRequest, res: Response) => Promise<void>;
