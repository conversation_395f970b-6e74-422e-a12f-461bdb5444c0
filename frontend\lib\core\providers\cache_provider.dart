import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/service_locator.dart';
import '../services/cache_manager.dart';

// Cache Manager Provider
final cacheManagerProvider = Provider<CacheManager>((ref) {
  return serviceLocator.cacheManager;
});

// Cache operations providers
final cacheDataProvider = FutureProvider.family<dynamic, String>((ref, key) async {
  final cacheManager = ref.read(cacheManagerProvider);
  return await cacheManager.getCachedData(key);
});

final isCachedProvider = FutureProvider.family<bool, String>((ref, key) async {
  final cacheManager = ref.read(cacheManagerProvider);
  return await cacheManager.isCached(key);
});

final cacheSizeProvider = FutureProvider<int>((ref) async {
  final cacheManager = ref.read(cacheManagerProvider);
  return await cacheManager.getCacheSize();
});

final cachedKeysProvider = FutureProvider<List<String>>((ref) async {
  final cacheManager = ref.read(cacheManagerProvider);
  return await cacheManager.getCachedKeys();
});

// Cache management providers
class CacheNotifier extends StateNotifier<AsyncValue<void>> {
  final CacheManager _cacheManager;

  CacheNotifier(this._cacheManager) : super(const AsyncValue.data(null));

  Future<void> cacheData(String key, dynamic data, {Duration? duration}) async {
    state = const AsyncValue.loading();
    try {
      await _cacheManager.cacheData(key, data, duration: duration);
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> removeCachedData(String key) async {
    state = const AsyncValue.loading();
    try {
      await _cacheManager.removeCachedData(key);
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> clearCache() async {
    state = const AsyncValue.loading();
    try {
      await _cacheManager.clearCache();
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> cleanExpiredCache() async {
    state = const AsyncValue.loading();
    try {
      await _cacheManager.cleanExpiredCache();
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

final cacheNotifierProvider = StateNotifierProvider<CacheNotifier, AsyncValue<void>>((ref) {
  final cacheManager = ref.read(cacheManagerProvider);
  return CacheNotifier(cacheManager);
});

// Utility providers for common cache operations
final generateCacheKeyProvider = Provider.family<String, Map<String, dynamic>>((ref, params) {
  final cacheManager = ref.read(cacheManagerProvider);
  final endpoint = params['endpoint'] as String;
  final queryParams = params['params'] as Map<String, dynamic>?;
  return cacheManager.generateCacheKey(endpoint, queryParams);
});
