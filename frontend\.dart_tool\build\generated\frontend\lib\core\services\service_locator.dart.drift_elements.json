{"valid_import": true, "imports": [{"uri": "package:flutter/foundation.dart", "transitive": false}, {"uri": "package:frontend/core/services/api_client.dart", "transitive": false}, {"uri": "package:frontend/core/services/auth_service.dart", "transitive": false}, {"uri": "package:frontend/core/services/property_service.dart", "transitive": false}, {"uri": "package:frontend/core/services/dashboard_service.dart", "transitive": false}, {"uri": "package:frontend/core/services/office_service.dart", "transitive": false}, {"uri": "package:frontend/core/services/notification_service.dart", "transitive": false}, {"uri": "package:frontend/core/services/file_service.dart", "transitive": false}, {"uri": "package:frontend/core/services/cache_manager.dart", "transitive": false}, {"uri": "package:frontend/core/services/connectivity_manager.dart", "transitive": false}, {"uri": "package:frontend/data/repositories/alert_repository.dart", "transitive": false}, {"uri": "package:frontend/data/repositories/auth_repository.dart", "transitive": false}, {"uri": "package:frontend/data/repositories/property_repository.dart", "transitive": false}, {"uri": "package:frontend/data/repositories/office_repository.dart", "transitive": false}, {"uri": "package:frontend/data/repositories/system_repository.dart", "transitive": false}, {"uri": "package:frontend/data/repositories/maintenance_repository.dart", "transitive": false}], "elements": []}