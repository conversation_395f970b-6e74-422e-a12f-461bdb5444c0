import 'package:json_annotation/json_annotation.dart';

part 'office.g.dart';

@JsonSerializable()
class Office {
  final String id;
  final String name;
  final String type; // OFFICE, CONSTRUCTION_SITE
  final String address;
  final String city;
  final String state;
  final String zipCode;
  final String country;
  final String? description;
  final int? capacity;
  final int? currentOccupancy;
  final String? managerId;
  final String status;
  final List<String>? amenities;
  final Map<String, dynamic>? workingHours;
  final Map<String, dynamic>? contactInfo;
  final Map<String, double>? coordinates;
  final double? latitude;
  final double? longitude;
  final bool isActive;
  final String createdAt;
  final String updatedAt;

  const Office({
    required this.id,
    required this.name,
    required this.type,
    required this.address,
    required this.city,
    required this.state,
    required this.zipCode,
    required this.country,
    this.description,
    this.capacity,
    this.currentOccupancy,
    this.managerId,
    required this.status,
    this.amenities,
    this.workingHours,
    this.contactInfo,
    this.coordinates,
    this.latitude,
    this.longitude,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Office.fromJson(Map<String, dynamic> json) => _$OfficeFromJson(json);
  Map<String, dynamic> toJson() => _$OfficeToJson(this);

  Office copyWith({
    String? id,
    String? name,
    String? type,
    String? address,
    String? city,
    String? state,
    String? zipCode,
    String? country,
    String? description,
    int? capacity,
    int? currentOccupancy,
    String? managerId,
    String? status,
    List<String>? amenities,
    Map<String, dynamic>? workingHours,
    Map<String, dynamic>? contactInfo,
    Map<String, double>? coordinates,
    double? latitude,
    double? longitude,
    bool? isActive,
    String? createdAt,
    String? updatedAt,
  }) {
    return Office(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      zipCode: zipCode ?? this.zipCode,
      country: country ?? this.country,
      description: description ?? this.description,
      capacity: capacity ?? this.capacity,
      currentOccupancy: currentOccupancy ?? this.currentOccupancy,
      managerId: managerId ?? this.managerId,
      status: status ?? this.status,
      amenities: amenities ?? this.amenities,
      workingHours: workingHours ?? this.workingHours,
      contactInfo: contactInfo ?? this.contactInfo,
      coordinates: coordinates ?? this.coordinates,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper getters
  bool get isOffice => type == 'OFFICE';
  bool get isConstructionSite => type == 'CONSTRUCTION_SITE';

  DateTime get createdDateTime => DateTime.parse(createdAt);
  DateTime get updatedDateTime => DateTime.parse(updatedAt);
}

@JsonSerializable()
class OfficeWithStats extends Office {
  final int employeeCount;
  final int presentToday;
  final double attendanceRate;

  const OfficeWithStats({
    required super.id,
    required super.name,
    required super.type,
    required super.address,
    required super.city,
    required super.state,
    required super.zipCode,
    required super.country,
    super.description,
    super.capacity,
    super.currentOccupancy,
    super.managerId,
    required super.status,
    super.amenities,
    super.workingHours,
    super.contactInfo,
    super.coordinates,
    super.latitude,
    super.longitude,
    required super.isActive,
    required super.createdAt,
    required super.updatedAt,
    required this.employeeCount,
    required this.presentToday,
    required this.attendanceRate,
  });

  factory OfficeWithStats.fromJson(Map<String, dynamic> json) => _$OfficeWithStatsFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$OfficeWithStatsToJson(this);

  // Helper getters
  int get absentToday => employeeCount - presentToday;
  bool get hasGoodAttendance => attendanceRate >= 80.0;
  bool get hasLowAttendance => attendanceRate < 60.0;
}

@JsonSerializable()
class Employee {
  final String id;
  final String officeId;
  final String name;
  final String? email;
  final String? phone;
  final String employeeId;
  final String designation;
  final String? department;
  final bool isActive;
  final String joinDate;
  final String createdAt;
  final String updatedAt;

  const Employee({
    required this.id,
    required this.officeId,
    required this.name,
    this.email,
    this.phone,
    required this.employeeId,
    required this.designation,
    this.department,
    required this.isActive,
    required this.joinDate,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Employee.fromJson(Map<String, dynamic> json) => _$EmployeeFromJson(json);
  Map<String, dynamic> toJson() => _$EmployeeToJson(this);

  Employee copyWith({
    String? id,
    String? officeId,
    String? name,
    String? email,
    String? phone,
    String? employeeId,
    String? designation,
    String? department,
    bool? isActive,
    String? joinDate,
    String? createdAt,
    String? updatedAt,
  }) {
    return Employee(
      id: id ?? this.id,
      officeId: officeId ?? this.officeId,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      employeeId: employeeId ?? this.employeeId,
      designation: designation ?? this.designation,
      department: department ?? this.department,
      isActive: isActive ?? this.isActive,
      joinDate: joinDate ?? this.joinDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper getters
  DateTime get joinDateTime => DateTime.parse(joinDate);
  DateTime get createdDateTime => DateTime.parse(createdAt);
  DateTime get updatedDateTime => DateTime.parse(updatedAt);

  Duration get tenure => DateTime.now().difference(joinDateTime);
  int get tenureInDays => tenure.inDays;
  int get tenureInMonths => (tenureInDays / 30).floor();
  int get tenureInYears => (tenureInDays / 365).floor();
}

@JsonSerializable()
class AttendanceRecord {
  final String id;
  final String officeId;
  final String? employeeId;
  final String? userId;
  final String date;
  final String status; // PRESENT, ABSENT, LATE, HALF_DAY, LEAVE
  final String? checkInTime;
  final String? checkOutTime;
  final double? hoursWorked;
  final double? overtime;
  final String? notes;
  final String createdAt;
  final String updatedAt;
  final EmployeeInfo? employee;
  final UserInfo? user;

  const AttendanceRecord({
    required this.id,
    required this.officeId,
    this.employeeId,
    this.userId,
    required this.date,
    required this.status,
    this.checkInTime,
    this.checkOutTime,
    this.hoursWorked,
    this.overtime,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
    this.employee,
    this.user,
  });

  factory AttendanceRecord.fromJson(Map<String, dynamic> json) => _$AttendanceRecordFromJson(json);
  Map<String, dynamic> toJson() => _$AttendanceRecordToJson(this);

  AttendanceRecord copyWith({
    String? id,
    String? officeId,
    String? employeeId,
    String? userId,
    String? date,
    String? status,
    String? checkInTime,
    String? checkOutTime,
    double? hoursWorked,
    double? overtime,
    String? notes,
    String? createdAt,
    String? updatedAt,
    EmployeeInfo? employee,
    UserInfo? user,
  }) {
    return AttendanceRecord(
      id: id ?? this.id,
      officeId: officeId ?? this.officeId,
      employeeId: employeeId ?? this.employeeId,
      userId: userId ?? this.userId,
      date: date ?? this.date,
      status: status ?? this.status,
      checkInTime: checkInTime ?? this.checkInTime,
      checkOutTime: checkOutTime ?? this.checkOutTime,
      hoursWorked: hoursWorked ?? this.hoursWorked,
      overtime: overtime ?? this.overtime,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      employee: employee ?? this.employee,
      user: user ?? this.user,
    );
  }

  // Helper getters
  bool get isPresent => status == 'PRESENT';
  bool get isAbsent => status == 'ABSENT';
  bool get isLate => status == 'LATE';
  bool get isHalfDay => status == 'HALF_DAY';
  bool get isLeave => status == 'LEAVE';

  DateTime get dateTime => DateTime.parse(date);
  DateTime get createdDateTime => DateTime.parse(createdAt);
  DateTime get updatedDateTime => DateTime.parse(updatedAt);

  String get personName => employee?.name ?? user?.name ?? 'Unknown';
  String get personId => employee?.employeeId ?? user?.email ?? 'Unknown';
}

@JsonSerializable()
class EmployeeInfo {
  final String id;
  final String name;
  final String employeeId;
  final String designation;

  const EmployeeInfo({
    required this.id,
    required this.name,
    required this.employeeId,
    required this.designation,
  });

  factory EmployeeInfo.fromJson(Map<String, dynamic> json) => _$EmployeeInfoFromJson(json);
  Map<String, dynamic> toJson() => _$EmployeeInfoToJson(this);
}

@JsonSerializable()
class UserInfo {
  final String id;
  final String name;
  final String email;

  const UserInfo({
    required this.id,
    required this.name,
    required this.email,
  });

  factory UserInfo.fromJson(Map<String, dynamic> json) => _$UserInfoFromJson(json);
  Map<String, dynamic> toJson() => _$UserInfoToJson(this);
}

@JsonSerializable()
class AttendanceSummary {
  final int total;
  final int present;
  final int absent;
  final int late;
  final int halfDay;
  final int leave;
  final double attendanceRate;

  const AttendanceSummary({
    required this.total,
    required this.present,
    required this.absent,
    required this.late,
    required this.halfDay,
    required this.leave,
    required this.attendanceRate,
  });

  factory AttendanceSummary.fromJson(Map<String, dynamic> json) => _$AttendanceSummaryFromJson(json);
  Map<String, dynamic> toJson() => _$AttendanceSummaryToJson(this);

  // Helper getters
  double get presentPercentage => total > 0 ? (present / total) * 100 : 0;
  double get absentPercentage => total > 0 ? (absent / total) * 100 : 0;
  double get latePercentage => total > 0 ? (late / total) * 100 : 0;
  double get halfDayPercentage => total > 0 ? (halfDay / total) * 100 : 0;
  double get leavePercentage => total > 0 ? (leave / total) * 100 : 0;

  bool get hasGoodAttendance => attendanceRate >= 80.0;
  bool get hasLowAttendance => attendanceRate < 60.0;
}

@JsonSerializable()
class AttendanceResponse {
  final String date;
  final Office office;
  final List<AttendanceRecord> records;
  final AttendanceSummary summary;

  const AttendanceResponse({
    required this.date,
    required this.office,
    required this.records,
    required this.summary,
  });

  factory AttendanceResponse.fromJson(Map<String, dynamic> json) => _$AttendanceResponseFromJson(json);
  Map<String, dynamic> toJson() => _$AttendanceResponseToJson(this);
}

@JsonSerializable()
class OfficeSettings {
  final String officeId;
  final String workingDaysPattern; // monday_to_friday, monday_to_saturday, etc.
  final String defaultStartTime;
  final String defaultEndTime;
  final bool allowFlexibleTiming;
  final int gracePeriodMinutes;
  final bool requireCheckOut;

  const OfficeSettings({
    required this.officeId,
    required this.workingDaysPattern,
    required this.defaultStartTime,
    required this.defaultEndTime,
    required this.allowFlexibleTiming,
    required this.gracePeriodMinutes,
    required this.requireCheckOut,
  });

  factory OfficeSettings.fromJson(Map<String, dynamic> json) => _$OfficeSettingsFromJson(json);
  Map<String, dynamic> toJson() => _$OfficeSettingsToJson(this);
}

@JsonSerializable()
class AttendanceReport {
  final String officeId;
  final DateTime startDate;
  final DateTime endDate;
  final String reportType; // daily, weekly, monthly
  final List<EmployeeAttendanceSummary> summaries;
  final int totalWorkingDays;
  final int totalEmployees;

  const AttendanceReport({
    required this.officeId,
    required this.startDate,
    required this.endDate,
    required this.reportType,
    required this.summaries,
    required this.totalWorkingDays,
    required this.totalEmployees,
  });

  factory AttendanceReport.fromJson(Map<String, dynamic> json) => _$AttendanceReportFromJson(json);
  Map<String, dynamic> toJson() => _$AttendanceReportToJson(this);
}

@JsonSerializable()
class EmployeeAttendanceSummary {
  final String employeeId;
  final String employeeName;
  final int presentDays;
  final int absentDays;
  final int lateDays;
  final int halfDays;
  final double attendancePercentage;
  final int totalHoursWorked;

  const EmployeeAttendanceSummary({
    required this.employeeId,
    required this.employeeName,
    required this.presentDays,
    required this.absentDays,
    required this.lateDays,
    required this.halfDays,
    required this.attendancePercentage,
    required this.totalHoursWorked,
  });

  factory EmployeeAttendanceSummary.fromJson(Map<String, dynamic> json) => _$EmployeeAttendanceSummaryFromJson(json);
  Map<String, dynamic> toJson() => _$EmployeeAttendanceSummaryToJson(this);
}
