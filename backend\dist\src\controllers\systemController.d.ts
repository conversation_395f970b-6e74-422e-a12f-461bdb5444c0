import { Request, Response } from 'express';
export declare const getSystemStatuses: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const getWaterSystems: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const getElectricitySystems: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const getSecuritySystems: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const updateSystemStatus: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
