import { UserRole } from '@prisma/client';
export interface PropertyAccessContext {
    userId: string;
    role: UserRole;
    propertyId?: string;
    officeId?: string;
    requestedPath: string;
}
export interface PropertyAccessResult {
    granted: boolean;
    reason?: string;
    assignedProperties: string[];
    assignedOffices: string[];
    metadata?: Record<string, any>;
}
export declare class PropertyAccessService {
    /**
     * Check if user has access to a specific property
     */
    static checkPropertyAccess(context: PropertyAccessContext): Promise<PropertyAccessResult>;
    /**
     * Check path-based property access (for paths that don't have explicit propertyId)
     */
    private static checkPathPropertyAccess;
    /**
     * Get user's accessible properties with role-based filtering
     */
    static getUserAccessibleProperties(userId: string, role: UserRole, filters?: {
        propertyType?: string;
        status?: string;
        location?: string;
    }): Promise<any[]>;
    /**
     * Assign property to user
     */
    static assignPropertyToUser(userId: string, propertyId: string, assignedBy: string, options?: {
        roleContext?: string;
        expiresAt?: Date;
        metadata?: Record<string, any>;
    }): Promise<{
        success: boolean;
        message: string;
    }>;
    /**
     * Remove property assignment from user
     */
    static unassignPropertyFromUser(userId: string, propertyId: string, performedBy: string, reason?: string): Promise<{
        success: boolean;
        message: string;
    }>;
    /**
     * Audit property access attempts
     */
    private static auditPropertyAccess;
}
