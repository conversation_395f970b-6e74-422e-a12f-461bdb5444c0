import 'package:json_annotation/json_annotation.dart';

part 'user_settings.g.dart';

@JsonSerializable()
class UserSettings {
  final String userId;
  final NotificationSettings notifications;
  final AppearanceSettings appearance;
  final SecuritySettings security;
  final PrivacySettings privacy;
  final AccessibilitySettings accessibility;
  final String createdAt;
  final String updatedAt;

  const UserSettings({
    required this.userId,
    required this.notifications,
    required this.appearance,
    required this.security,
    required this.privacy,
    required this.accessibility,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserSettings.fromJson(Map<String, dynamic> json) => _$UserSettingsFromJson(json);
  Map<String, dynamic> toJson() => _$UserSettingsToJson(this);

  UserSettings copyWith({
    String? userId,
    NotificationSettings? notifications,
    AppearanceSettings? appearance,
    SecuritySettings? security,
    PrivacySettings? privacy,
    AccessibilitySettings? accessibility,
    String? createdAt,
    String? updatedAt,
  }) {
    return UserSettings(
      userId: userId ?? this.userId,
      notifications: notifications ?? this.notifications,
      appearance: appearance ?? this.appearance,
      security: security ?? this.security,
      privacy: privacy ?? this.privacy,
      accessibility: accessibility ?? this.accessibility,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper getters
  DateTime get createdDateTime => DateTime.parse(createdAt);
  DateTime get updatedDateTime => DateTime.parse(updatedAt);

  // Factory for default settings
  factory UserSettings.defaultSettings(String userId) {
    final now = DateTime.now().toIso8601String();
    return UserSettings(
      userId: userId,
      notifications: NotificationSettings.defaultSettings(),
      appearance: AppearanceSettings.defaultSettings(),
      security: SecuritySettings.defaultSettings(),
      privacy: PrivacySettings.defaultSettings(),
      accessibility: AccessibilitySettings.defaultSettings(),
      createdAt: now,
      updatedAt: now,
    );
  }
}

@JsonSerializable()
class NotificationSettings {
  final bool pushNotifications;
  final bool emailNotifications;
  final bool smsNotifications;
  final bool systemAlerts;
  final bool maintenanceAlerts;
  final bool securityAlerts;
  final bool propertyUpdates;
  final bool officeUpdates;
  final String quietHoursStart;
  final String quietHoursEnd;
  final bool weekendNotifications;

  const NotificationSettings({
    required this.pushNotifications,
    required this.emailNotifications,
    required this.smsNotifications,
    required this.systemAlerts,
    required this.maintenanceAlerts,
    required this.securityAlerts,
    required this.propertyUpdates,
    required this.officeUpdates,
    required this.quietHoursStart,
    required this.quietHoursEnd,
    required this.weekendNotifications,
  });

  factory NotificationSettings.fromJson(Map<String, dynamic> json) => _$NotificationSettingsFromJson(json);
  Map<String, dynamic> toJson() => _$NotificationSettingsToJson(this);

  NotificationSettings copyWith({
    bool? pushNotifications,
    bool? emailNotifications,
    bool? smsNotifications,
    bool? systemAlerts,
    bool? maintenanceAlerts,
    bool? securityAlerts,
    bool? propertyUpdates,
    bool? officeUpdates,
    String? quietHoursStart,
    String? quietHoursEnd,
    bool? weekendNotifications,
  }) {
    return NotificationSettings(
      pushNotifications: pushNotifications ?? this.pushNotifications,
      emailNotifications: emailNotifications ?? this.emailNotifications,
      smsNotifications: smsNotifications ?? this.smsNotifications,
      systemAlerts: systemAlerts ?? this.systemAlerts,
      maintenanceAlerts: maintenanceAlerts ?? this.maintenanceAlerts,
      securityAlerts: securityAlerts ?? this.securityAlerts,
      propertyUpdates: propertyUpdates ?? this.propertyUpdates,
      officeUpdates: officeUpdates ?? this.officeUpdates,
      quietHoursStart: quietHoursStart ?? this.quietHoursStart,
      quietHoursEnd: quietHoursEnd ?? this.quietHoursEnd,
      weekendNotifications: weekendNotifications ?? this.weekendNotifications,
    );
  }

  factory NotificationSettings.defaultSettings() {
    return const NotificationSettings(
      pushNotifications: true,
      emailNotifications: true,
      smsNotifications: false,
      systemAlerts: true,
      maintenanceAlerts: true,
      securityAlerts: true,
      propertyUpdates: true,
      officeUpdates: true,
      quietHoursStart: '22:00',
      quietHoursEnd: '07:00',
      weekendNotifications: false,
    );
  }
}

@JsonSerializable()
class AppearanceSettings {
  final String theme; // light, dark, system
  final String language;
  final String timezone;
  final String dateFormat;
  final String timeFormat;
  final String currency;
  final double fontSize;
  final bool compactMode;
  final bool showAnimations;

  const AppearanceSettings({
    required this.theme,
    required this.language,
    required this.timezone,
    required this.dateFormat,
    required this.timeFormat,
    required this.currency,
    required this.fontSize,
    required this.compactMode,
    required this.showAnimations,
  });

  factory AppearanceSettings.fromJson(Map<String, dynamic> json) => _$AppearanceSettingsFromJson(json);
  Map<String, dynamic> toJson() => _$AppearanceSettingsToJson(this);

  AppearanceSettings copyWith({
    String? theme,
    String? language,
    String? timezone,
    String? dateFormat,
    String? timeFormat,
    String? currency,
    double? fontSize,
    bool? compactMode,
    bool? showAnimations,
  }) {
    return AppearanceSettings(
      theme: theme ?? this.theme,
      language: language ?? this.language,
      timezone: timezone ?? this.timezone,
      dateFormat: dateFormat ?? this.dateFormat,
      timeFormat: timeFormat ?? this.timeFormat,
      currency: currency ?? this.currency,
      fontSize: fontSize ?? this.fontSize,
      compactMode: compactMode ?? this.compactMode,
      showAnimations: showAnimations ?? this.showAnimations,
    );
  }

  factory AppearanceSettings.defaultSettings() {
    return const AppearanceSettings(
      theme: 'system',
      language: 'en',
      timezone: 'Asia/Kolkata',
      dateFormat: 'dd/MM/yyyy',
      timeFormat: '24h',
      currency: 'INR',
      fontSize: 14.0,
      compactMode: false,
      showAnimations: true,
    );
  }

  // Helper getters
  bool get isDarkTheme => theme == 'dark';
  bool get isLightTheme => theme == 'light';
  bool get isSystemTheme => theme == 'system';
  bool get is24HourFormat => timeFormat == '24h';
}

@JsonSerializable()
class SecuritySettings {
  final bool biometricLogin;
  final bool autoLock;
  final int autoLockTimeout; // in minutes
  final bool requirePasswordForSensitiveActions;
  final bool twoFactorAuthentication;
  final bool sessionTimeout;
  final int sessionTimeoutMinutes;
  final bool logSecurityEvents;

  const SecuritySettings({
    required this.biometricLogin,
    required this.autoLock,
    required this.autoLockTimeout,
    required this.requirePasswordForSensitiveActions,
    required this.twoFactorAuthentication,
    required this.sessionTimeout,
    required this.sessionTimeoutMinutes,
    required this.logSecurityEvents,
  });

  factory SecuritySettings.fromJson(Map<String, dynamic> json) => _$SecuritySettingsFromJson(json);
  Map<String, dynamic> toJson() => _$SecuritySettingsToJson(this);

  SecuritySettings copyWith({
    bool? biometricLogin,
    bool? autoLock,
    int? autoLockTimeout,
    bool? requirePasswordForSensitiveActions,
    bool? twoFactorAuthentication,
    bool? sessionTimeout,
    int? sessionTimeoutMinutes,
    bool? logSecurityEvents,
  }) {
    return SecuritySettings(
      biometricLogin: biometricLogin ?? this.biometricLogin,
      autoLock: autoLock ?? this.autoLock,
      autoLockTimeout: autoLockTimeout ?? this.autoLockTimeout,
      requirePasswordForSensitiveActions: requirePasswordForSensitiveActions ?? this.requirePasswordForSensitiveActions,
      twoFactorAuthentication: twoFactorAuthentication ?? this.twoFactorAuthentication,
      sessionTimeout: sessionTimeout ?? this.sessionTimeout,
      sessionTimeoutMinutes: sessionTimeoutMinutes ?? this.sessionTimeoutMinutes,
      logSecurityEvents: logSecurityEvents ?? this.logSecurityEvents,
    );
  }

  factory SecuritySettings.defaultSettings() {
    return const SecuritySettings(
      biometricLogin: false,
      autoLock: true,
      autoLockTimeout: 15,
      requirePasswordForSensitiveActions: true,
      twoFactorAuthentication: false,
      sessionTimeout: true,
      sessionTimeoutMinutes: 60,
      logSecurityEvents: true,
    );
  }
}

@JsonSerializable()
class PrivacySettings {
  final bool shareUsageData;
  final bool shareLocationData;
  final bool allowAnalytics;
  final bool showOnlineStatus;
  final bool allowContactSync;
  final String dataRetentionPeriod; // in days
  final bool autoDeleteOldData;

  const PrivacySettings({
    required this.shareUsageData,
    required this.shareLocationData,
    required this.allowAnalytics,
    required this.showOnlineStatus,
    required this.allowContactSync,
    required this.dataRetentionPeriod,
    required this.autoDeleteOldData,
  });

  factory PrivacySettings.fromJson(Map<String, dynamic> json) => _$PrivacySettingsFromJson(json);
  Map<String, dynamic> toJson() => _$PrivacySettingsToJson(this);

  PrivacySettings copyWith({
    bool? shareUsageData,
    bool? shareLocationData,
    bool? allowAnalytics,
    bool? showOnlineStatus,
    bool? allowContactSync,
    String? dataRetentionPeriod,
    bool? autoDeleteOldData,
  }) {
    return PrivacySettings(
      shareUsageData: shareUsageData ?? this.shareUsageData,
      shareLocationData: shareLocationData ?? this.shareLocationData,
      allowAnalytics: allowAnalytics ?? this.allowAnalytics,
      showOnlineStatus: showOnlineStatus ?? this.showOnlineStatus,
      allowContactSync: allowContactSync ?? this.allowContactSync,
      dataRetentionPeriod: dataRetentionPeriod ?? this.dataRetentionPeriod,
      autoDeleteOldData: autoDeleteOldData ?? this.autoDeleteOldData,
    );
  }

  factory PrivacySettings.defaultSettings() {
    return const PrivacySettings(
      shareUsageData: false,
      shareLocationData: false,
      allowAnalytics: true,
      showOnlineStatus: true,
      allowContactSync: false,
      dataRetentionPeriod: '365',
      autoDeleteOldData: false,
    );
  }
}

@JsonSerializable()
class AccessibilitySettings {
  final bool highContrast;
  final bool largeText;
  final bool reduceMotion;
  final bool screenReader;
  final bool voiceCommands;
  final double textScaling;
  final bool hapticFeedback;
  final bool audioDescriptions;

  const AccessibilitySettings({
    required this.highContrast,
    required this.largeText,
    required this.reduceMotion,
    required this.screenReader,
    required this.voiceCommands,
    required this.textScaling,
    required this.hapticFeedback,
    required this.audioDescriptions,
  });

  factory AccessibilitySettings.fromJson(Map<String, dynamic> json) => _$AccessibilitySettingsFromJson(json);
  Map<String, dynamic> toJson() => _$AccessibilitySettingsToJson(this);

  AccessibilitySettings copyWith({
    bool? highContrast,
    bool? largeText,
    bool? reduceMotion,
    bool? screenReader,
    bool? voiceCommands,
    double? textScaling,
    bool? hapticFeedback,
    bool? audioDescriptions,
  }) {
    return AccessibilitySettings(
      highContrast: highContrast ?? this.highContrast,
      largeText: largeText ?? this.largeText,
      reduceMotion: reduceMotion ?? this.reduceMotion,
      screenReader: screenReader ?? this.screenReader,
      voiceCommands: voiceCommands ?? this.voiceCommands,
      textScaling: textScaling ?? this.textScaling,
      hapticFeedback: hapticFeedback ?? this.hapticFeedback,
      audioDescriptions: audioDescriptions ?? this.audioDescriptions,
    );
  }

  factory AccessibilitySettings.defaultSettings() {
    return const AccessibilitySettings(
      highContrast: false,
      largeText: false,
      reduceMotion: false,
      screenReader: false,
      voiceCommands: false,
      textScaling: 1.0,
      hapticFeedback: true,
      audioDescriptions: false,
    );
  }
}
