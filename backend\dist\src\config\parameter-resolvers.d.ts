export interface ParameterResolverConfig {
    parameterName: string;
    sourceTable: string;
    sourceField: string;
    keyField: string;
    cacheMinutes: number;
    metadata?: Record<string, any>;
}
export declare const PARAMETER_RESOLVERS: ParameterResolverConfig[];
export interface BreadcrumbTemplate {
    pathPattern: string;
    displayTemplate: string;
    description: string;
    examples: string[];
}
export declare const BREADCRUMB_TEMPLATES: BreadcrumbTemplate[];
export declare function getParameterResolver(parameterName: string): ParameterResolverConfig | undefined;
export declare function getBreadcrumbTemplate(pathPattern: string): BreadcrumbTemplate | undefined;
