"use strict";
// Screen Tab Definitions for Each Breadcrumb Path
Object.defineProperty(exports, "__esModule", { value: true });
exports.SCREEN_TAB_CONFIGURATIONS = void 0;
exports.getScreenTabConfiguration = getScreenTabConfiguration;
exports.getTabDefinition = getTabDefinition;
exports.getTabComponent = getTabComponent;
exports.SCREEN_TAB_CONFIGURATIONS = [
    // Security Management Screen
    {
        screenPath: '/properties/{propertyId}/systems/security',
        screenName: 'Security Management',
        tabs: [
            {
                tabId: 'overview',
                name: 'Overview',
                description: 'Security system overview and status',
                order: 1,
                icon: 'dashboard',
                components: [
                    {
                        componentId: 'security.overview.system_status',
                        name: 'System Status Card',
                        type: 'card',
                        permissions: ['view'],
                    },
                    {
                        componentId: 'security.overview.recent_alerts',
                        name: 'Recent Alerts',
                        type: 'table',
                        permissions: ['view'],
                    },
                    {
                        componentId: 'security.overview.financial_summary',
                        name: 'Financial Summary',
                        type: 'card',
                        permissions: ['view_financial'],
                    },
                    {
                        componentId: 'security.overview.quick_actions',
                        name: 'Quick Actions',
                        type: 'widget',
                        permissions: ['view'],
                    },
                ],
            },
            {
                tabId: 'cctv',
                name: 'CCTV',
                description: 'CCTV camera management and monitoring',
                order: 2,
                icon: 'videocam',
                components: [
                    {
                        componentId: 'security.cctv.camera_grid',
                        name: 'Camera Grid',
                        type: 'widget',
                        permissions: ['view'],
                    },
                    {
                        componentId: 'security.cctv.live_feeds',
                        name: 'Live Camera Feeds',
                        type: 'widget',
                        permissions: ['view_live'],
                    },
                    {
                        componentId: 'security.cctv.recordings',
                        name: 'Recording History',
                        type: 'table',
                        permissions: ['view_recordings'],
                    },
                    {
                        componentId: 'security.cctv.camera_controls',
                        name: 'Camera Controls',
                        type: 'form',
                        permissions: ['control_cameras'],
                    },
                    {
                        componentId: 'security.cctv.add_camera',
                        name: 'Add Camera Button',
                        type: 'button',
                        permissions: ['create'],
                    },
                    {
                        componentId: 'security.cctv.settings',
                        name: 'CCTV Settings',
                        type: 'form',
                        permissions: ['configure'],
                    },
                ],
            },
            {
                tabId: 'access_control',
                name: 'Access Control',
                description: 'Access control system management',
                order: 3,
                icon: 'security',
                components: [
                    {
                        componentId: 'security.access.system_status',
                        name: 'Access System Status',
                        type: 'widget',
                        permissions: ['view'],
                    },
                    {
                        componentId: 'security.access.user_management',
                        name: 'User Management',
                        type: 'table',
                        permissions: ['manage_users'],
                    },
                    {
                        componentId: 'security.access.access_logs',
                        name: 'Access Logs',
                        type: 'table',
                        permissions: ['view_logs'],
                    },
                    {
                        componentId: 'security.access.emergency_override',
                        name: 'Emergency Override',
                        type: 'button',
                        permissions: ['emergency_override'],
                    },
                    {
                        componentId: 'security.access.add_user',
                        name: 'Add User Button',
                        type: 'button',
                        permissions: ['create_user'],
                    },
                ],
            },
            {
                tabId: 'maintenance',
                name: 'Maintenance',
                description: 'Security system maintenance',
                order: 4,
                icon: 'build',
                components: [
                    {
                        componentId: 'security.maintenance.schedule',
                        name: 'Maintenance Schedule',
                        type: 'table',
                        permissions: ['view'],
                    },
                    {
                        componentId: 'security.maintenance.history',
                        name: 'Maintenance History',
                        type: 'table',
                        permissions: ['view'],
                    },
                    {
                        componentId: 'security.maintenance.create_task',
                        name: 'Create Maintenance Task',
                        type: 'button',
                        permissions: ['create'],
                    },
                    {
                        componentId: 'security.maintenance.vendor_contacts',
                        name: 'Vendor Contacts',
                        type: 'table',
                        permissions: ['view_vendors'],
                    },
                    {
                        componentId: 'security.maintenance.cost_tracking',
                        name: 'Cost Tracking',
                        type: 'display',
                        permissions: ['view_costs'],
                    },
                ],
            },
            {
                tabId: 'contacts',
                name: 'Contacts',
                description: 'Security-related contacts and vendors',
                order: 5,
                icon: 'contacts',
                components: [
                    {
                        componentId: 'security.contacts.emergency',
                        name: 'Emergency Contacts',
                        type: 'table',
                        permissions: ['view'],
                    },
                    {
                        componentId: 'security.contacts.vendors',
                        name: 'Vendor Contacts',
                        type: 'table',
                        permissions: ['view_vendors'],
                    },
                    {
                        componentId: 'security.contacts.contracts',
                        name: 'Contract Details',
                        type: 'display',
                        permissions: ['view_contracts'],
                    },
                    {
                        componentId: 'security.contacts.add_contact',
                        name: 'Add Contact Button',
                        type: 'button',
                        permissions: ['create'],
                    },
                ],
            },
        ],
    },
    // Electricity Management Screen
    {
        screenPath: '/properties/{propertyId}/systems/electricity',
        screenName: 'Electricity Management',
        tabs: [
            {
                tabId: 'overview',
                name: 'Overview',
                description: 'Electricity system overview',
                order: 1,
                icon: 'dashboard',
                components: [
                    {
                        componentId: 'electricity.overview.system_status',
                        name: 'System Status',
                        type: 'card',
                        permissions: ['view'],
                    },
                    {
                        componentId: 'electricity.overview.power_consumption',
                        name: 'Power Consumption Chart',
                        type: 'chart',
                        permissions: ['view'],
                    },
                    {
                        componentId: 'electricity.overview.cost_analysis',
                        name: 'Cost Analysis',
                        type: 'widget',
                        permissions: ['view_financial'],
                    },
                ],
            },
            {
                tabId: 'generator',
                name: 'Generator',
                description: 'Generator monitoring and control',
                order: 2,
                icon: 'electrical_services',
                components: [
                    {
                        componentId: 'electricity.generator.status',
                        name: 'Generator Status',
                        type: 'widget',
                        permissions: ['view'],
                    },
                    {
                        componentId: 'electricity.generator.fuel_level',
                        name: 'Fuel Level Display',
                        type: 'display',
                        permissions: ['view'],
                    },
                    {
                        componentId: 'electricity.generator.update_fuel',
                        name: 'Update Fuel Level',
                        type: 'form',
                        permissions: ['update_fuel'],
                    },
                    {
                        componentId: 'electricity.generator.start_stop',
                        name: 'Start/Stop Controls',
                        type: 'button',
                        permissions: ['control'],
                    },
                    {
                        componentId: 'electricity.generator.maintenance',
                        name: 'Generator Maintenance',
                        type: 'table',
                        permissions: ['view_maintenance'],
                    },
                    {
                        componentId: 'electricity.generator.settings',
                        name: 'Generator Settings',
                        type: 'form',
                        permissions: ['configure'],
                    },
                ],
            },
            {
                tabId: 'ups',
                name: 'UPS',
                description: 'UPS monitoring and management',
                order: 3,
                icon: 'battery_charging_full',
                components: [
                    {
                        componentId: 'electricity.ups.status',
                        name: 'UPS Status',
                        type: 'widget',
                        permissions: ['view'],
                    },
                    {
                        componentId: 'electricity.ups.battery_status',
                        name: 'Battery Status',
                        type: 'display',
                        permissions: ['view'],
                    },
                    {
                        componentId: 'electricity.ups.diagnostics',
                        name: 'Run Diagnostics',
                        type: 'button',
                        permissions: ['diagnostics'],
                    },
                    {
                        componentId: 'electricity.ups.settings',
                        name: 'UPS Settings',
                        type: 'form',
                        permissions: ['configure'],
                    },
                ],
            },
            {
                tabId: 'maintenance',
                name: 'Maintenance',
                description: 'Electrical system maintenance',
                order: 4,
                icon: 'build',
                components: [
                    {
                        componentId: 'electricity.maintenance.schedule',
                        name: 'Maintenance Schedule',
                        type: 'table',
                        permissions: ['view'],
                    },
                    {
                        componentId: 'electricity.maintenance.history',
                        name: 'Maintenance History',
                        type: 'table',
                        permissions: ['view'],
                    },
                    {
                        componentId: 'electricity.maintenance.create_task',
                        name: 'Create Task',
                        type: 'button',
                        permissions: ['create'],
                    },
                ],
            },
            {
                tabId: 'contacts',
                name: 'Contacts',
                description: 'Electrical contractors and vendors',
                order: 5,
                icon: 'contacts',
                components: [
                    {
                        componentId: 'electricity.contacts.emergency',
                        name: 'Emergency Contacts',
                        type: 'table',
                        permissions: ['view'],
                    },
                    {
                        componentId: 'electricity.contacts.vendors',
                        name: 'Vendor Contacts',
                        type: 'table',
                        permissions: ['view_vendors'],
                    },
                    {
                        componentId: 'electricity.contacts.contracts',
                        name: 'Contract Details',
                        type: 'display',
                        permissions: ['view_contracts'],
                    },
                ],
            },
        ],
    },
    // Water Management Screen
    {
        screenPath: '/properties/{propertyId}/systems/water',
        screenName: 'Water Management',
        tabs: [
            {
                tabId: 'overview',
                name: 'Overview',
                description: 'Water system overview',
                order: 1,
                icon: 'dashboard',
                components: [
                    {
                        componentId: 'water.overview.system_status',
                        name: 'System Status',
                        type: 'card',
                        permissions: ['view'],
                    },
                    {
                        componentId: 'water.overview.tank_levels',
                        name: 'Tank Levels',
                        type: 'widget',
                        permissions: ['view'],
                    },
                    {
                        componentId: 'water.overview.pump_status',
                        name: 'Pump Status',
                        type: 'widget',
                        permissions: ['view'],
                    },
                ],
            },
            {
                tabId: 'tanks',
                name: 'Tank Monitoring',
                description: 'Water tank monitoring',
                order: 2,
                icon: 'water_drop',
                components: [
                    {
                        componentId: 'water.tanks.level_indicators',
                        name: 'Level Indicators',
                        type: 'widget',
                        permissions: ['view'],
                    },
                    {
                        componentId: 'water.tanks.cleaning_schedule',
                        name: 'Cleaning Schedule',
                        type: 'table',
                        permissions: ['view'],
                    },
                    {
                        componentId: 'water.tanks.quality_reports',
                        name: 'Quality Reports',
                        type: 'table',
                        permissions: ['view_quality'],
                    },
                ],
            },
            {
                tabId: 'pumps',
                name: 'Pump Control',
                description: 'Water pump control and monitoring',
                order: 3,
                icon: 'settings',
                components: [
                    {
                        componentId: 'water.pumps.status_grid',
                        name: 'Pump Status Grid',
                        type: 'widget',
                        permissions: ['view'],
                    },
                    {
                        componentId: 'water.pumps.controls',
                        name: 'Pump Controls',
                        type: 'form',
                        permissions: ['control'],
                    },
                    {
                        componentId: 'water.pumps.maintenance',
                        name: 'Pump Maintenance',
                        type: 'table',
                        permissions: ['view_maintenance'],
                    },
                ],
            },
        ],
    },
    // Office Management Screen
    {
        screenPath: '/office',
        screenName: 'Office Management',
        tabs: [
            {
                tabId: 'overview',
                name: 'Overview',
                description: 'Office operations overview',
                order: 1,
                icon: 'dashboard',
                components: [
                    {
                        componentId: 'office.overview.summary',
                        name: 'Office Summary',
                        type: 'card',
                        permissions: ['view'],
                    },
                    {
                        componentId: 'office.overview.locations',
                        name: 'Office Locations',
                        type: 'table',
                        permissions: ['view'],
                    },
                ],
            },
            {
                tabId: 'attendance',
                name: 'Attendance',
                description: 'Employee attendance management',
                order: 2,
                icon: 'schedule',
                components: [
                    {
                        componentId: 'office.attendance.daily_summary',
                        name: 'Daily Summary',
                        type: 'widget',
                        permissions: ['view'],
                    },
                    {
                        componentId: 'office.attendance.employee_list',
                        name: 'Employee List',
                        type: 'table',
                        permissions: ['view'],
                    },
                    {
                        componentId: 'office.attendance.mark_attendance',
                        name: 'Mark Attendance',
                        type: 'button',
                        permissions: ['mark'],
                    },
                    {
                        componentId: 'office.attendance.reports',
                        name: 'Attendance Reports',
                        type: 'table',
                        permissions: ['view_reports'],
                    },
                ],
            },
            {
                tabId: 'employees',
                name: 'Employees',
                description: 'Employee management',
                order: 3,
                icon: 'people',
                components: [
                    {
                        componentId: 'office.employees.list',
                        name: 'Employee List',
                        type: 'table',
                        permissions: ['view'],
                    },
                    {
                        componentId: 'office.employees.add_employee',
                        name: 'Add Employee',
                        type: 'button',
                        permissions: ['create'],
                    },
                    {
                        componentId: 'office.employees.employee_details',
                        name: 'Employee Details',
                        type: 'form',
                        permissions: ['view_details'],
                    },
                    {
                        componentId: 'office.employees.salary_info',
                        name: 'Salary Information',
                        type: 'display',
                        permissions: ['view_salary'],
                    },
                ],
            },
        ],
    },
];
// Helper functions
function getScreenTabConfiguration(screenPath) {
    return exports.SCREEN_TAB_CONFIGURATIONS.find(config => config.screenPath === screenPath);
}
function getTabDefinition(screenPath, tabId) {
    const screenConfig = getScreenTabConfiguration(screenPath);
    return screenConfig?.tabs.find(tab => tab.tabId === tabId);
}
function getTabComponent(screenPath, tabId, componentId) {
    const tabDef = getTabDefinition(screenPath, tabId);
    return tabDef?.components.find(component => component.componentId === componentId);
}
