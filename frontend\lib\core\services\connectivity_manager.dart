import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';

enum ConnectivityStatus {
  online,
  offline,
  unknown,
}

class ConnectivityManager extends ChangeNotifier {
  static final ConnectivityManager _instance = ConnectivityManager._internal();
  factory ConnectivityManager() => _instance;
  ConnectivityManager._internal();

  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  
  ConnectivityStatus _status = ConnectivityStatus.unknown;
  bool _isInitialized = false;

  ConnectivityStatus get status => _status;
  bool get isOnline => _status == ConnectivityStatus.online;
  bool get isOffline => _status == ConnectivityStatus.offline;
  bool get isInitialized => _isInitialized;

  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Check initial connectivity
      final result = await _connectivity.checkConnectivity();
      _updateStatus(result);

      // Listen for connectivity changes
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
        _updateStatus,
        onError: (error) {
          debugPrint('Connectivity error: $error');
          _status = ConnectivityStatus.unknown;
          notifyListeners();
        },
      );

      _isInitialized = true;
    } catch (e) {
      debugPrint('Failed to initialize connectivity manager: $e');
      _status = ConnectivityStatus.unknown;
      _isInitialized = true;
    }
  }

  void _updateStatus(List<ConnectivityResult> results) {
    final previousStatus = _status;
    
    if (results.isEmpty || results.contains(ConnectivityResult.none)) {
      _status = ConnectivityStatus.offline;
    } else if (results.contains(ConnectivityResult.mobile) ||
               results.contains(ConnectivityResult.wifi) ||
               results.contains(ConnectivityResult.ethernet) ||
               results.contains(ConnectivityResult.vpn) ||
               results.contains(ConnectivityResult.bluetooth) ||
               results.contains(ConnectivityResult.other)) {
      _status = ConnectivityStatus.online;
    } else {
      _status = ConnectivityStatus.unknown;
    }

    if (previousStatus != _status) {
      notifyListeners();
      debugPrint('Connectivity status changed: $_status');
    }
  }

  Future<bool> checkConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      _updateStatus(result);
      return isOnline;
    } catch (e) {
      debugPrint('Failed to check connectivity: $e');
      return false;
    }
  }

  Stream<ConnectivityStatus> get statusStream {
    return _connectivity.onConnectivityChanged.map((results) {
      if (results.isEmpty || results.contains(ConnectivityResult.none)) {
        return ConnectivityStatus.offline;
      } else if (results.contains(ConnectivityResult.mobile) ||
                 results.contains(ConnectivityResult.wifi) ||
                 results.contains(ConnectivityResult.ethernet) ||
                 results.contains(ConnectivityResult.vpn) ||
                 results.contains(ConnectivityResult.bluetooth) ||
                 results.contains(ConnectivityResult.other)) {
        return ConnectivityStatus.online;
      } else {
        return ConnectivityStatus.unknown;
      }
    });
  }

  String get connectionType {
    switch (_status) {
      case ConnectivityStatus.online:
        return 'Online';
      case ConnectivityStatus.offline:
        return 'Offline';
      case ConnectivityStatus.unknown:
        return 'Unknown';
    }
  }

  Future<List<ConnectivityResult>> getCurrentConnectivity() async {
    try {
      return await _connectivity.checkConnectivity();
    } catch (e) {
      debugPrint('Failed to get current connectivity: $e');
      return [ConnectivityResult.none];
    }
  }

  bool hasConnection(List<ConnectivityResult> results) {
    return results.isNotEmpty && !results.contains(ConnectivityResult.none);
  }

  bool hasWifiConnection(List<ConnectivityResult> results) {
    return results.contains(ConnectivityResult.wifi);
  }

  bool hasMobileConnection(List<ConnectivityResult> results) {
    return results.contains(ConnectivityResult.mobile);
  }

  bool hasEthernetConnection(List<ConnectivityResult> results) {
    return results.contains(ConnectivityResult.ethernet);
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  // Utility method for waiting for connectivity
  Future<void> waitForConnection({Duration timeout = const Duration(seconds: 30)}) async {
    if (isOnline) return;

    final completer = Completer<void>();
    late StreamSubscription subscription;

    subscription = statusStream.listen((status) {
      if (status == ConnectivityStatus.online) {
        subscription.cancel();
        if (!completer.isCompleted) {
          completer.complete();
        }
      }
    });

    // Set timeout
    Timer(timeout, () {
      subscription.cancel();
      if (!completer.isCompleted) {
        completer.completeError(TimeoutException('Connection timeout', timeout));
      }
    });

    return completer.future;
  }

  // Method to retry an operation when connection is available
  Future<T> retryWhenOnline<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 2),
  }) async {
    int attempts = 0;
    
    while (attempts < maxRetries) {
      try {
        if (!isOnline) {
          await waitForConnection();
        }
        
        return await operation();
      } catch (e) {
        attempts++;
        if (attempts >= maxRetries) {
          rethrow;
        }
        
        await Future.delayed(retryDelay);
      }
    }
    
    throw Exception('Max retry attempts reached');
  }
}

class TimeoutException implements Exception {
  final String message;
  final Duration timeout;
  
  const TimeoutException(this.message, this.timeout);
  
  @override
  String toString() => 'TimeoutException: $message (${timeout.inSeconds}s)';
}
