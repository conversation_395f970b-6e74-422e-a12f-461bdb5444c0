import '../models/api_response.dart';
import '../models/electricity_system.dart';
import '../../core/services/api_client.dart';
import '../../core/services/service_locator.dart';
import '../../core/constants/api_constants.dart';

class ElectricityRepository {
  final ApiClient _apiClient = serviceLocator.apiClient;

  Future<ApiResponse<List<ElectricitySystem>>> getElectricitySystems({required String propertyId}) async {
    try {
      final endpoint = ApiConstants.electricitySystems.replaceAll('{propertyId}', propertyId);
      final response = await _apiClient.get(endpoint);

      if (response.isSuccess) {
        final responseData = response.data['data'];
        final List<dynamic> systems = responseData['systems'] ?? [];
        final electricitySystems = systems.map((json) => ElectricitySystem.fromBackendJson(json)).toList();
        return ApiResponse.success(data: electricitySystems);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch electricity systems: $e');
    }
  }


}
