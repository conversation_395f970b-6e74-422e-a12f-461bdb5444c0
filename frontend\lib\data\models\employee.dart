import 'package:json_annotation/json_annotation.dart';

part 'employee.g.dart';

@JsonSerializable()
class Employee {
  final String id;
  final String name;
  final String email;
  final String? phone;
  final String? position;
  final String? department;
  final String status;
  final String? propertyId;
  final DateTime? hireDate;
  final double? salary;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Employee({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    this.position,
    this.department,
    required this.status,
    this.propertyId,
    this.hireDate,
    this.salary,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Employee.fromJson(Map<String, dynamic> json) => _$EmployeeFromJson(json);

  Map<String, dynamic> toJson() => _$EmployeeToJson(this);

  Employee copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? position,
    String? department,
    String? status,
    String? propertyId,
    DateTime? hireDate,
    double? salary,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Employee(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      position: position ?? this.position,
      department: department ?? this.department,
      status: status ?? this.status,
      propertyId: propertyId ?? this.propertyId,
      hireDate: hireDate ?? this.hireDate,
      salary: salary ?? this.salary,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Employee &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          name == other.name &&
          email == other.email &&
          phone == other.phone &&
          position == other.position &&
          department == other.department &&
          status == other.status &&
          propertyId == other.propertyId &&
          hireDate == other.hireDate &&
          salary == other.salary &&
          createdAt == other.createdAt &&
          updatedAt == other.updatedAt;

  @override
  int get hashCode =>
      id.hashCode ^
      name.hashCode ^
      email.hashCode ^
      phone.hashCode ^
      position.hashCode ^
      department.hashCode ^
      status.hashCode ^
      propertyId.hashCode ^
      hireDate.hashCode ^
      salary.hashCode ^
      createdAt.hashCode ^
      updatedAt.hashCode;

  @override
  String toString() {
    return 'Employee{id: $id, name: $name, email: $email, phone: $phone, position: $position, department: $department, status: $status, propertyId: $propertyId, hireDate: $hireDate, salary: $salary, createdAt: $createdAt, updatedAt: $updatedAt}';
  }
}
