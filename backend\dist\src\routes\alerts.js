"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middleware/auth");
const rbac_1 = require("../middleware/rbac");
const alertController_1 = require("../controllers/alertController");
const router = express_1.default.Router();
// Apply authentication to all routes
router.use(auth_1.authenticateToken);
/**
 * @swagger
 * /v1/alerts:
 *   get:
 *     summary: Get all alerts with filtering and pagination
 *     tags: [Alerts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: propertyId
 *         schema:
 *           type: string
 *         description: Filter by property ID
 *       - in: query
 *         name: severity
 *         schema:
 *           type: string
 *           enum: [LOW, MEDIUM, HIGH, CRITICAL]
 *         description: Filter by severity
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [OPEN, ACKNOWLEDGED, RESOLVED]
 *         description: Filter by status
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Filter by category
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Items per page
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [createdAt, severity, status, title]
 *           default: createdAt
 *         description: Sort field
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: Sort order
 *     responses:
 *       200:
 *         description: Alerts retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         type: object
 *                     total:
 *                       type: integer
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *                     hasNext:
 *                       type: boolean
 *                     hasPrevious:
 *                       type: boolean
 *       403:
 *         description: Access denied
 *       500:
 *         description: Server error
 */
router.get('/', (0, rbac_1.rbac)(['SUPER_ADMIN', 'PROPERTY_MANAGER', 'EMPLOYEE']), alertController_1.getAlerts);
/**
 * @swagger
 * /v1/alerts/statistics:
 *   get:
 *     summary: Get alert statistics
 *     tags: [Alerts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: propertyId
 *         schema:
 *           type: string
 *         description: Filter by property ID
 *       - in: query
 *         name: dateFrom
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for statistics
 *       - in: query
 *         name: dateTo
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for statistics
 *     responses:
 *       200:
 *         description: Alert statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalAlerts:
 *                       type: integer
 *                     openAlerts:
 *                       type: integer
 *                     acknowledgedAlerts:
 *                       type: integer
 *                     resolvedAlerts:
 *                       type: integer
 *                     criticalAlerts:
 *                       type: integer
 *                     highAlerts:
 *                       type: integer
 *                     mediumAlerts:
 *                       type: integer
 *                     lowAlerts:
 *                       type: integer
 *                     alertsByCategory:
 *                       type: object
 *                     alertsByProperty:
 *                       type: object
 *                     trends:
 *                       type: array
 *                       items:
 *                         type: object
 *       403:
 *         description: Access denied
 *       500:
 *         description: Server error
 */
router.get('/statistics', (0, rbac_1.rbac)(['SUPER_ADMIN', 'PROPERTY_MANAGER', 'EMPLOYEE']), alertController_1.getAlertStatistics);
/**
 * @swagger
 * /v1/alerts/{id}:
 *   get:
 *     summary: Get single alert
 *     tags: [Alerts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Alert ID
 *     responses:
 *       200:
 *         description: Alert retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *       403:
 *         description: Access denied
 *       404:
 *         description: Alert not found
 *       500:
 *         description: Server error
 */
router.get('/:id', (0, rbac_1.rbac)(['SUPER_ADMIN', 'PROPERTY_MANAGER', 'EMPLOYEE']), alertController_1.getAlert);
/**
 * @swagger
 * /v1/alerts:
 *   post:
 *     summary: Create new alert
 *     tags: [Alerts]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - message
 *               - severity
 *               - category
 *             properties:
 *               propertyId:
 *                 type: string
 *               title:
 *                 type: string
 *               message:
 *                 type: string
 *               severity:
 *                 type: string
 *                 enum: [LOW, MEDIUM, HIGH, CRITICAL]
 *               category:
 *                 type: string
 *               metadata:
 *                 type: object
 *     responses:
 *       201:
 *         description: Alert created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                 message:
 *                   type: string
 *       400:
 *         description: Validation error
 *       403:
 *         description: Access denied
 *       500:
 *         description: Server error
 */
router.post('/', (0, rbac_1.rbac)(['SUPER_ADMIN', 'PROPERTY_MANAGER', 'EMPLOYEE']), alertController_1.createAlert);
/**
 * @swagger
 * /v1/alerts/{id}/status:
 *   put:
 *     summary: Update alert status
 *     tags: [Alerts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Alert ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [OPEN, ACKNOWLEDGED, RESOLVED]
 *     responses:
 *       200:
 *         description: Alert status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                 message:
 *                   type: string
 *       403:
 *         description: Access denied
 *       404:
 *         description: Alert not found
 *       500:
 *         description: Server error
 */
router.put('/:id/status', (0, rbac_1.rbac)(['SUPER_ADMIN', 'PROPERTY_MANAGER', 'EMPLOYEE']), alertController_1.updateAlertStatus);
/**
 * @swagger
 * /v1/alerts/{id}:
 *   delete:
 *     summary: Delete alert
 *     tags: [Alerts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Alert ID
 *     responses:
 *       200:
 *         description: Alert deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       403:
 *         description: Access denied
 *       404:
 *         description: Alert not found
 *       500:
 *         description: Server error
 */
router.delete('/:id', (0, rbac_1.rbac)(['SUPER_ADMIN']), alertController_1.deleteAlert);
exports.default = router;
