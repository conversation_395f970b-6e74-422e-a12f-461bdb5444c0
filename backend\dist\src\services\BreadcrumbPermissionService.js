"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BreadcrumbPermissionService = void 0;
const prisma_1 = require("@/lib/prisma");
const role_path_permissions_1 = require("@/config/role-path-permissions");
const PropertyAccessService_1 = require("./PropertyAccessService");
class BreadcrumbPermissionService {
    /**
     * Check permissions for a breadcrumb path
     */
    static async checkPathPermission(context) {
        try {
            // Resolve path with parameters
            const resolvedPath = this.resolvePath(context.path, context.params);
            // 1. Check property-level access first (most restrictive)
            const propertyAccessResult = await this.checkPropertyAccess(context, resolvedPath);
            if (!propertyAccessResult.granted) {
                await this.auditAccess(context, false, propertyAccessResult.reason);
                return {
                    hasAccess: false,
                    accessLevel: 'none',
                    permissions: [],
                    tabs: {},
                    components: {},
                    reason: propertyAccessResult.reason
                };
            }
            // 2. Get role-based path permissions
            const pathRule = (0, role_path_permissions_1.resolvePathPermissions)(context.role, resolvedPath);
            if (!pathRule) {
                await this.auditAccess(context, false, 'No permission rule found');
                return {
                    hasAccess: false,
                    accessLevel: 'none',
                    permissions: [],
                    tabs: {},
                    components: {},
                    reason: 'No permission rule found for path'
                };
            }
            // 3. Check ABAC conditions
            const conditionsResult = await this.evaluateConditions(context, pathRule.conditions);
            if (!conditionsResult.granted) {
                await this.auditAccess(context, false, conditionsResult.reason);
                return {
                    hasAccess: false,
                    accessLevel: 'none',
                    permissions: [],
                    tabs: {},
                    components: {},
                    reason: conditionsResult.reason
                };
            }
            // Get tab and component permissions for this path
            const tabPermissions = await this.getTabPermissions(context, resolvedPath, pathRule);
            const componentPermissions = await this.getComponentPermissions(context, resolvedPath, pathRule);
            await this.auditAccess(context, true, 'Access granted');
            return {
                hasAccess: true,
                accessLevel: pathRule.accessLevel,
                permissions: pathRule.permissions,
                restrictions: pathRule.restrictions,
                tabs: tabPermissions,
                components: componentPermissions,
            };
        }
        catch (error) {
            console.error('Error checking path permission:', error);
            await this.auditAccess(context, false, 'System error');
            return {
                hasAccess: false,
                accessLevel: 'none',
                permissions: [],
                tabs: {},
                components: {},
                reason: 'System error during permission check'
            };
        }
    }
    /**
     * Check property-level access control
     */
    static async checkPropertyAccess(context, resolvedPath) {
        try {
            const propertyAccessContext = {
                userId: context.userId,
                role: context.role,
                propertyId: context.params?.propertyId,
                officeId: context.params?.officeId,
                requestedPath: resolvedPath
            };
            const result = await PropertyAccessService_1.PropertyAccessService.checkPropertyAccess(propertyAccessContext);
            // Store assigned properties in context for later use
            if (result.granted) {
                context.userAssignedProperties = result.assignedProperties;
                context.userAssignedOffices = result.assignedOffices;
            }
            return {
                granted: result.granted,
                reason: result.reason
            };
        }
        catch (error) {
            console.error('Error checking property access:', error);
            return {
                granted: false,
                reason: 'System error during property access check'
            };
        }
    }
    /**
     * Get tab-level permissions for a path
     */
    static async getTabPermissions(context, resolvedPath, pathRule) {
        try {
            // Get screen tabs from database
            const breadcrumbPath = await prisma_1.prisma.breadcrumbPath.findUnique({
                where: { path: resolvedPath },
                include: {
                    screenTabs: {
                        include: {
                            tabPermissions: {
                                where: {
                                    role: { name: context.role }
                                }
                            },
                            tabComponents: {
                                include: {
                                    tabComponentPermissions: {
                                        where: {
                                            role: { name: context.role }
                                        }
                                    }
                                }
                            }
                        },
                        orderBy: { order: 'asc' }
                    }
                }
            });
            const tabMap = {};
            if (breadcrumbPath?.screenTabs) {
                for (const tab of breadcrumbPath.screenTabs) {
                    const tabPermission = tab.tabPermissions[0]; // Should be only one per role
                    if (!tabPermission) {
                        // No explicit permission = no access
                        tabMap[tab.tabId] = {
                            visible: false,
                            enabled: false,
                            accessLevel: 'none',
                            components: {}
                        };
                        continue;
                    }
                    // Get component permissions for this tab
                    const tabComponentMap = {};
                    for (const component of tab.tabComponents) {
                        const componentPermission = component.tabComponentPermissions[0];
                        // Check if component should be hidden based on tab restrictions
                        const isHidden = tabPermission.restrictions?.hideComponents?.includes(component.componentId) ?? false;
                        const isDisabled = tabPermission.restrictions?.disableComponents?.includes(component.componentId) ?? false;
                        tabComponentMap[component.componentId] = {
                            visible: !isHidden && (componentPermission?.accessLevel !== 'hidden'),
                            enabled: !isDisabled && (componentPermission?.accessLevel !== 'disabled') && tabPermission.accessLevel !== 'read',
                            permissions: component.permissions || [],
                            restrictions: {
                                ...tabPermission.restrictions,
                                ...componentPermission?.restrictions
                            }
                        };
                    }
                    tabMap[tab.tabId] = {
                        visible: tabPermission.isVisible,
                        enabled: tabPermission.isEnabled,
                        accessLevel: tabPermission.accessLevel,
                        restrictions: tabPermission.restrictions,
                        components: tabComponentMap
                    };
                }
            }
            return tabMap;
        }
        catch (error) {
            console.error('Error getting tab permissions:', error);
            return {};
        }
    }
    /**
     * Get component-level permissions for a path
     */
    static async getComponentPermissions(context, resolvedPath, pathRule) {
        try {
            // Get path definition from database
            const breadcrumbPath = await prisma_1.prisma.breadcrumbPath.findUnique({
                where: { path: resolvedPath },
                include: {
                    pathComponents: {
                        include: {
                            componentPermissions: {
                                where: {
                                    role: { name: context.role }
                                }
                            }
                        }
                    }
                }
            });
            const componentMap = {};
            if (breadcrumbPath) {
                for (const component of breadcrumbPath.pathComponents) {
                    const componentPermission = component.componentPermissions[0]; // Should be only one per role
                    // Check if component should be hidden based on path restrictions
                    const isHidden = pathRule.restrictions?.hideComponents?.includes(component.componentId) ?? false;
                    const isDisabled = pathRule.restrictions?.disableComponents?.includes(component.componentId) ?? false;
                    // Determine component permissions based on path access level and restrictions
                    let componentPermissions = component.permissions || [];
                    if (pathRule.accessLevel === 'read') {
                        componentPermissions = componentPermissions.filter(p => p === 'view' || p.startsWith('view_'));
                    }
                    componentMap[component.componentId] = {
                        visible: !isHidden && (componentPermission?.accessLevel !== 'hidden'),
                        enabled: !isDisabled && (componentPermission?.accessLevel !== 'disabled') && pathRule.accessLevel !== 'read',
                        permissions: componentPermissions,
                        restrictions: {
                            ...pathRule.restrictions,
                            ...componentPermission?.restrictions
                        }
                    };
                }
            }
            return componentMap;
        }
        catch (error) {
            console.error('Error getting component permissions:', error);
            return {};
        }
    }
    /**
     * Get breadcrumb navigation for a path with permission filtering
     */
    static async getBreadcrumbNavigation(context) {
        try {
            const resolvedPath = this.resolvePath(context.path, context.params);
            const breadcrumbs = [];
            // Build breadcrumb hierarchy
            const pathParts = resolvedPath.split('/').filter(part => part);
            let currentPath = '';
            for (let i = 0; i < pathParts.length; i++) {
                currentPath += '/' + pathParts[i];
                // Check if user has access to this path level
                const pathContext = { ...context, path: currentPath };
                const permission = await this.checkPathPermission(pathContext);
                if (permission.hasAccess) {
                    const breadcrumbPath = await prisma_1.prisma.breadcrumbPath.findUnique({
                        where: { path: currentPath }
                    });
                    if (breadcrumbPath) {
                        breadcrumbs.push({
                            path: currentPath,
                            name: await this.resolveBreadcrumbName(breadcrumbPath.name, context.params),
                            level: breadcrumbPath.level,
                            accessible: true,
                            accessLevel: permission.accessLevel
                        });
                    }
                }
            }
            return breadcrumbs;
        }
        catch (error) {
            console.error('Error building breadcrumb navigation:', error);
            return [];
        }
    }
    /**
     * Get all accessible paths for a user (for navigation menu)
     */
    static async getAccessiblePaths(context) {
        try {
            const allPaths = await prisma_1.prisma.breadcrumbPath.findMany({
                where: { isActive: true },
                select: { path: true }
            });
            const accessiblePaths = [];
            for (const pathRecord of allPaths) {
                const pathContext = { ...context, path: pathRecord.path };
                const permission = await this.checkPathPermission(pathContext);
                if (permission.hasAccess) {
                    accessiblePaths.push(pathRecord.path);
                }
            }
            return accessiblePaths;
        }
        catch (error) {
            console.error('Error getting accessible paths:', error);
            return [];
        }
    }
    /**
     * Resolve path parameters
     */
    static resolvePath(path, params) {
        if (!params)
            return path;
        let resolvedPath = path;
        for (const [key, value] of Object.entries(params)) {
            resolvedPath = resolvedPath.replace(`{${key}}`, value);
        }
        return resolvedPath;
    }
    /**
     * Resolve breadcrumb display name with parameters
     */
    static async resolveBreadcrumbName(name, params) {
        if (!params)
            return name;
        let resolvedName = name;
        // Replace parameter placeholders with actual values
        for (const [key, value] of Object.entries(params)) {
            if (key === 'propertyId') {
                // Fetch property name from database
                const property = await prisma_1.prisma.property.findUnique({
                    where: { id: value },
                    select: { name: true }
                });
                resolvedName = resolvedName.replace(`{${key}}`, property?.name || value);
            }
            else {
                resolvedName = resolvedName.replace(`{${key}}`, value);
            }
        }
        return resolvedName;
    }
    /**
     * Evaluate ABAC conditions
     */
    static async evaluateConditions(context, conditions) {
        if (!conditions)
            return { granted: true };
        try {
            // Property type condition
            if (conditions.propertyType && context.params?.propertyId) {
                const property = await prisma_1.prisma.property.findUnique({
                    where: { id: context.params.propertyId },
                    select: { type: true }
                });
                if (property?.type !== conditions.propertyType) {
                    return { granted: false, reason: `Access restricted to ${conditions.propertyType} properties` };
                }
            }
            // Time-based conditions
            if (conditions.timeRestriction) {
                const now = new Date();
                const currentHour = now.getHours();
                const { startHour, endHour } = conditions.timeRestriction;
                if (currentHour < startHour || currentHour > endHour) {
                    return { granted: false, reason: 'Access denied: Outside allowed time window' };
                }
            }
            // User assignment conditions
            if (conditions.requireAssignment && context.params?.propertyId) {
                const user = await prisma_1.prisma.user.findUnique({
                    where: { id: context.userId },
                    select: { assignedProperties: true }
                });
                if (!user?.assignedProperties.includes(context.params.propertyId)) {
                    return { granted: false, reason: 'Access denied: Property not assigned to user' };
                }
            }
            return { granted: true };
        }
        catch (error) {
            console.error('Error evaluating conditions:', error);
            return { granted: false, reason: 'Error evaluating access conditions' };
        }
    }
    /**
     * Audit path access
     */
    static async auditAccess(context, granted, reason) {
        try {
            await prisma_1.prisma.navigationAudit.create({
                data: {
                    userId: context.userId,
                    path: context.path,
                    action: 'navigate',
                    granted,
                    reason,
                    context: {
                        params: context.params,
                        query: context.query,
                        metadata: context.metadata
                    }
                }
            });
        }
        catch (error) {
            console.error('Error auditing access:', error);
        }
    }
}
exports.BreadcrumbPermissionService = BreadcrumbPermissionService;
