// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Office _$OfficeFromJson(Map<String, dynamic> json) => Office(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
      address: json['address'] as String,
      city: json['city'] as String,
      state: json['state'] as String,
      zipCode: json['zipCode'] as String,
      country: json['country'] as String,
      description: json['description'] as String?,
      capacity: (json['capacity'] as num?)?.toInt(),
      currentOccupancy: (json['currentOccupancy'] as num?)?.toInt(),
      managerId: json['managerId'] as String?,
      status: json['status'] as String,
      amenities: (json['amenities'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      workingHours: json['workingHours'] as Map<String, dynamic>?,
      contactInfo: json['contactInfo'] as Map<String, dynamic>?,
      coordinates: (json['coordinates'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      isActive: json['isActive'] as bool,
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
    );

Map<String, dynamic> _$OfficeToJson(Office instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
      'address': instance.address,
      'city': instance.city,
      'state': instance.state,
      'zipCode': instance.zipCode,
      'country': instance.country,
      'description': instance.description,
      'capacity': instance.capacity,
      'currentOccupancy': instance.currentOccupancy,
      'managerId': instance.managerId,
      'status': instance.status,
      'amenities': instance.amenities,
      'workingHours': instance.workingHours,
      'contactInfo': instance.contactInfo,
      'coordinates': instance.coordinates,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'isActive': instance.isActive,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
    };

OfficeWithStats _$OfficeWithStatsFromJson(Map<String, dynamic> json) =>
    OfficeWithStats(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
      address: json['address'] as String,
      city: json['city'] as String,
      state: json['state'] as String,
      zipCode: json['zipCode'] as String,
      country: json['country'] as String,
      description: json['description'] as String?,
      capacity: (json['capacity'] as num?)?.toInt(),
      currentOccupancy: (json['currentOccupancy'] as num?)?.toInt(),
      managerId: json['managerId'] as String?,
      status: json['status'] as String,
      amenities: (json['amenities'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      workingHours: json['workingHours'] as Map<String, dynamic>?,
      contactInfo: json['contactInfo'] as Map<String, dynamic>?,
      coordinates: (json['coordinates'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      isActive: json['isActive'] as bool,
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
      employeeCount: (json['employeeCount'] as num).toInt(),
      presentToday: (json['presentToday'] as num).toInt(),
      attendanceRate: (json['attendanceRate'] as num).toDouble(),
    );

Map<String, dynamic> _$OfficeWithStatsToJson(OfficeWithStats instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
      'address': instance.address,
      'city': instance.city,
      'state': instance.state,
      'zipCode': instance.zipCode,
      'country': instance.country,
      'description': instance.description,
      'capacity': instance.capacity,
      'currentOccupancy': instance.currentOccupancy,
      'managerId': instance.managerId,
      'status': instance.status,
      'amenities': instance.amenities,
      'workingHours': instance.workingHours,
      'contactInfo': instance.contactInfo,
      'coordinates': instance.coordinates,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'isActive': instance.isActive,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
      'employeeCount': instance.employeeCount,
      'presentToday': instance.presentToday,
      'attendanceRate': instance.attendanceRate,
    };

Employee _$EmployeeFromJson(Map<String, dynamic> json) => Employee(
      id: json['id'] as String,
      officeId: json['officeId'] as String,
      name: json['name'] as String,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      employeeId: json['employeeId'] as String,
      designation: json['designation'] as String,
      department: json['department'] as String?,
      isActive: json['isActive'] as bool,
      joinDate: json['joinDate'] as String,
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
    );

Map<String, dynamic> _$EmployeeToJson(Employee instance) => <String, dynamic>{
      'id': instance.id,
      'officeId': instance.officeId,
      'name': instance.name,
      'email': instance.email,
      'phone': instance.phone,
      'employeeId': instance.employeeId,
      'designation': instance.designation,
      'department': instance.department,
      'isActive': instance.isActive,
      'joinDate': instance.joinDate,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
    };

AttendanceRecord _$AttendanceRecordFromJson(Map<String, dynamic> json) =>
    AttendanceRecord(
      id: json['id'] as String,
      officeId: json['officeId'] as String,
      employeeId: json['employeeId'] as String?,
      userId: json['userId'] as String?,
      date: json['date'] as String,
      status: json['status'] as String,
      checkInTime: json['checkInTime'] as String?,
      checkOutTime: json['checkOutTime'] as String?,
      hoursWorked: (json['hoursWorked'] as num?)?.toDouble(),
      overtime: (json['overtime'] as num?)?.toDouble(),
      notes: json['notes'] as String?,
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
      employee: json['employee'] == null
          ? null
          : EmployeeInfo.fromJson(json['employee'] as Map<String, dynamic>),
      user: json['user'] == null
          ? null
          : UserInfo.fromJson(json['user'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$AttendanceRecordToJson(AttendanceRecord instance) =>
    <String, dynamic>{
      'id': instance.id,
      'officeId': instance.officeId,
      'employeeId': instance.employeeId,
      'userId': instance.userId,
      'date': instance.date,
      'status': instance.status,
      'checkInTime': instance.checkInTime,
      'checkOutTime': instance.checkOutTime,
      'hoursWorked': instance.hoursWorked,
      'overtime': instance.overtime,
      'notes': instance.notes,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
      'employee': instance.employee,
      'user': instance.user,
    };

EmployeeInfo _$EmployeeInfoFromJson(Map<String, dynamic> json) => EmployeeInfo(
      id: json['id'] as String,
      name: json['name'] as String,
      employeeId: json['employeeId'] as String,
      designation: json['designation'] as String,
    );

Map<String, dynamic> _$EmployeeInfoToJson(EmployeeInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'employeeId': instance.employeeId,
      'designation': instance.designation,
    };

UserInfo _$UserInfoFromJson(Map<String, dynamic> json) => UserInfo(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
    );

Map<String, dynamic> _$UserInfoToJson(UserInfo instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
    };

AttendanceSummary _$AttendanceSummaryFromJson(Map<String, dynamic> json) =>
    AttendanceSummary(
      total: (json['total'] as num).toInt(),
      present: (json['present'] as num).toInt(),
      absent: (json['absent'] as num).toInt(),
      late: (json['late'] as num).toInt(),
      halfDay: (json['halfDay'] as num).toInt(),
      leave: (json['leave'] as num).toInt(),
      attendanceRate: (json['attendanceRate'] as num).toDouble(),
    );

Map<String, dynamic> _$AttendanceSummaryToJson(AttendanceSummary instance) =>
    <String, dynamic>{
      'total': instance.total,
      'present': instance.present,
      'absent': instance.absent,
      'late': instance.late,
      'halfDay': instance.halfDay,
      'leave': instance.leave,
      'attendanceRate': instance.attendanceRate,
    };

AttendanceResponse _$AttendanceResponseFromJson(Map<String, dynamic> json) =>
    AttendanceResponse(
      date: json['date'] as String,
      office: Office.fromJson(json['office'] as Map<String, dynamic>),
      records: (json['records'] as List<dynamic>)
          .map((e) => AttendanceRecord.fromJson(e as Map<String, dynamic>))
          .toList(),
      summary:
          AttendanceSummary.fromJson(json['summary'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$AttendanceResponseToJson(AttendanceResponse instance) =>
    <String, dynamic>{
      'date': instance.date,
      'office': instance.office,
      'records': instance.records,
      'summary': instance.summary,
    };

OfficeSettings _$OfficeSettingsFromJson(Map<String, dynamic> json) =>
    OfficeSettings(
      officeId: json['officeId'] as String,
      workingDaysPattern: json['workingDaysPattern'] as String,
      defaultStartTime: json['defaultStartTime'] as String,
      defaultEndTime: json['defaultEndTime'] as String,
      allowFlexibleTiming: json['allowFlexibleTiming'] as bool,
      gracePeriodMinutes: (json['gracePeriodMinutes'] as num).toInt(),
      requireCheckOut: json['requireCheckOut'] as bool,
    );

Map<String, dynamic> _$OfficeSettingsToJson(OfficeSettings instance) =>
    <String, dynamic>{
      'officeId': instance.officeId,
      'workingDaysPattern': instance.workingDaysPattern,
      'defaultStartTime': instance.defaultStartTime,
      'defaultEndTime': instance.defaultEndTime,
      'allowFlexibleTiming': instance.allowFlexibleTiming,
      'gracePeriodMinutes': instance.gracePeriodMinutes,
      'requireCheckOut': instance.requireCheckOut,
    };

AttendanceReport _$AttendanceReportFromJson(Map<String, dynamic> json) =>
    AttendanceReport(
      officeId: json['officeId'] as String,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      reportType: json['reportType'] as String,
      summaries: (json['summaries'] as List<dynamic>)
          .map((e) =>
              EmployeeAttendanceSummary.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalWorkingDays: (json['totalWorkingDays'] as num).toInt(),
      totalEmployees: (json['totalEmployees'] as num).toInt(),
    );

Map<String, dynamic> _$AttendanceReportToJson(AttendanceReport instance) =>
    <String, dynamic>{
      'officeId': instance.officeId,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'reportType': instance.reportType,
      'summaries': instance.summaries,
      'totalWorkingDays': instance.totalWorkingDays,
      'totalEmployees': instance.totalEmployees,
    };

EmployeeAttendanceSummary _$EmployeeAttendanceSummaryFromJson(
        Map<String, dynamic> json) =>
    EmployeeAttendanceSummary(
      employeeId: json['employeeId'] as String,
      employeeName: json['employeeName'] as String,
      presentDays: (json['presentDays'] as num).toInt(),
      absentDays: (json['absentDays'] as num).toInt(),
      lateDays: (json['lateDays'] as num).toInt(),
      halfDays: (json['halfDays'] as num).toInt(),
      attendancePercentage: (json['attendancePercentage'] as num).toDouble(),
      totalHoursWorked: (json['totalHoursWorked'] as num).toInt(),
    );

Map<String, dynamic> _$EmployeeAttendanceSummaryToJson(
        EmployeeAttendanceSummary instance) =>
    <String, dynamic>{
      'employeeId': instance.employeeId,
      'employeeName': instance.employeeName,
      'presentDays': instance.presentDays,
      'absentDays': instance.absentDays,
      'lateDays': instance.lateDays,
      'halfDays': instance.halfDays,
      'attendancePercentage': instance.attendancePercentage,
      'totalHoursWorked': instance.totalHoursWorked,
    };
