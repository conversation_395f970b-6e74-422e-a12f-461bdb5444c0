export interface ResolvedBreadcrumbItem {
    path: string;
    originalPath: string;
    displayName: string;
    level: number;
    parameters: Record<string, string>;
    resolvedParameters: Record<string, string>;
}
export interface BreadcrumbResolutionContext {
    userId: string;
    role: string;
    parameters: Record<string, string>;
    metadata?: Record<string, any>;
}
export declare class BreadcrumbResolverService {
    /**
     * Resolve dynamic breadcrumb path with human-readable names
     */
    static resolveBreadcrumbPath(pathPattern: string, context: BreadcrumbResolutionContext): Promise<ResolvedBreadcrumbItem[]>;
    /**
     * Resolve individual breadcrumb item
     */
    private static resolveBreadcrumbItem;
    /**
     * Resolve parameter value to human-readable name
     */
    private static resolveParameter;
    /**
     * Execute parameter resolution query
     */
    private static executeParameterQuery;
    /**
     * Get cached breadcrumb if available and not expired
     */
    private static getCachedBreadcrumb;
    /**
     * Cache breadcrumb resolution result
     */
    private static cacheBreadcrumb;
    /**
     * Build breadcrumb hierarchy from cached data
     */
    private static buildBreadcrumbHierarchy;
    /**
     * Get breadcrumb suggestions for autocomplete/search
     */
    static getBreadcrumbSuggestions(query: string, context: BreadcrumbResolutionContext, limit?: number): Promise<ResolvedBreadcrumbItem[]>;
    /**
     * Preload common breadcrumb resolutions
     */
    static preloadCommonBreadcrumbs(userId: string): Promise<void>;
    /**
     * Clean expired cache entries
     */
    static cleanExpiredCache(): Promise<void>;
}
