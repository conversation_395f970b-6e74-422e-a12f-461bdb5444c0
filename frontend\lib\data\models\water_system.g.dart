// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'water_system.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WaterSystem _$WaterSystemFromJson(Map<String, dynamic> json) => WaterSystem(
      id: json['id'] as String,
      name: json['name'] as String,
      propertyId: json['propertyId'] as String,
      type: json['type'] as String,
      status: json['status'] as String,
      specifications: json['specifications'] as Map<String, dynamic>,
      currentReadings: json['currentReadings'] as Map<String, dynamic>,
      location: json['location'] as String,
      description: json['description'] as String?,
      isActive: json['isActive'] as bool,
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
    );

Map<String, dynamic> _$WaterSystemToJson(WaterSystem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'propertyId': instance.propertyId,
      'type': instance.type,
      'status': instance.status,
      'specifications': instance.specifications,
      'currentReadings': instance.currentReadings,
      'location': instance.location,
      'description': instance.description,
      'isActive': instance.isActive,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
    };
