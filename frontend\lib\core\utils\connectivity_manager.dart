import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';

class ConnectivityManager {
  static const String _testHost = 'google.com';
  static const int _testPort = 443;
  static const Duration _testTimeout = Duration(seconds: 5);
  
  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  
  final StreamController<bool> _connectionStatusController = StreamController<bool>.broadcast();
  bool _isConnected = false;
  bool _isInitialized = false;

  /// Stream of connection status changes
  Stream<bool> get connectionStream => _connectionStatusController.stream;

  /// Current connection status
  bool get isConnected => _isConnected;

  /// Initialize connectivity monitoring
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    // Check initial connectivity
    _isConnected = await _checkConnectivity();
    
    // Listen for connectivity changes
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      _onConnectivityChanged,
      onError: (error) {
        debugPrint('Connectivity error: $error');
      },
    );
    
    _isInitialized = true;
  }

  /// Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _connectionStatusController.close();
    _isInitialized = false;
  }

  /// Check if device has internet connectivity
  Future<bool> _checkConnectivity() async {
    try {
      final connectivityResults = await _connectivity.checkConnectivity();

      // If no connectivity, return false immediately
      if (connectivityResults.contains(ConnectivityResult.none) || connectivityResults.isEmpty) {
        return false;
      }

      // Test actual internet connectivity
      return await _testInternetConnection();
    } catch (e) {
      debugPrint('Connectivity check error: $e');
      return false;
    }
  }

  /// Test actual internet connection by connecting to a reliable host
  Future<bool> _testInternetConnection() async {
    try {
      final socket = await Socket.connect(
        _testHost,
        _testPort,
        timeout: _testTimeout,
      );
      socket.destroy();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Handle connectivity changes
  void _onConnectivityChanged(List<ConnectivityResult> results) async {
    final wasConnected = _isConnected;
    _isConnected = await _checkConnectivity();

    // Only emit if status actually changed
    if (wasConnected != _isConnected) {
      _connectionStatusController.add(_isConnected);

      if (_isConnected) {
        debugPrint('Internet connection restored');
      } else {
        debugPrint('Internet connection lost');
      }
    }
  }

  /// Force check connectivity status
  Future<bool> checkConnectivity() async {
    final wasConnected = _isConnected;
    _isConnected = await _checkConnectivity();
    
    // Emit status change if different
    if (wasConnected != _isConnected) {
      _connectionStatusController.add(_isConnected);
    }
    
    return _isConnected;
  }

  /// Get detailed connectivity information
  Future<ConnectivityInfo> getConnectivityInfo() async {
    final connectivityResults = await _connectivity.checkConnectivity();
    final hasInternet = await _testInternetConnection();

    // Use the first non-none result, or none if all are none
    final primaryResult = connectivityResults.firstWhere(
      (result) => result != ConnectivityResult.none,
      orElse: () => ConnectivityResult.none,
    );

    return ConnectivityInfo(
      connectivityResult: primaryResult,
      hasInternet: hasInternet,
      timestamp: DateTime.now(),
    );
  }

  /// Wait for internet connection with timeout
  Future<bool> waitForConnection({Duration? timeout}) async {
    if (_isConnected) return true;
    
    final completer = Completer<bool>();
    StreamSubscription<bool>? subscription;
    Timer? timeoutTimer;
    
    subscription = connectionStream.listen((isConnected) {
      if (isConnected) {
        subscription?.cancel();
        timeoutTimer?.cancel();
        if (!completer.isCompleted) {
          completer.complete(true);
        }
      }
    });
    
    if (timeout != null) {
      timeoutTimer = Timer(timeout, () {
        subscription?.cancel();
        if (!completer.isCompleted) {
          completer.complete(false);
        }
      });
    }
    
    return completer.future;
  }

  /// Execute a function when connected, with retry mechanism
  Future<T?> executeWhenConnected<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 2),
    Duration? connectionTimeout,
  }) async {
    for (int attempt = 0; attempt < maxRetries; attempt++) {
      // Wait for connection if not connected
      if (!_isConnected) {
        final connected = await waitForConnection(timeout: connectionTimeout);
        if (!connected) {
          continue; // Try again
        }
      }
      
      try {
        return await operation();
      } catch (e) {
        debugPrint('Operation failed (attempt ${attempt + 1}): $e');
        
        // Check if it's a network error
        if (_isNetworkError(e)) {
          // Force check connectivity
          await checkConnectivity();
          
          // Wait before retry
          if (attempt < maxRetries - 1) {
            await Future.delayed(retryDelay);
          }
        } else {
          // Non-network error, don't retry
          rethrow;
        }
      }
    }
    
    return null;
  }

  /// Check if an error is network-related
  bool _isNetworkError(dynamic error) {
    if (error is SocketException) return true;
    if (error is TimeoutException) return true;
    if (error is HttpException) return true;
    
    final errorString = error.toString().toLowerCase();
    return errorString.contains('network') ||
           errorString.contains('connection') ||
           errorString.contains('timeout') ||
           errorString.contains('unreachable') ||
           errorString.contains('failed host lookup');
  }

  /// Get connection type string
  String getConnectionTypeString(ConnectivityResult result) {
    switch (result) {
      case ConnectivityResult.wifi:
        return 'WiFi';
      case ConnectivityResult.mobile:
        return 'Mobile Data';
      case ConnectivityResult.ethernet:
        return 'Ethernet';
      case ConnectivityResult.bluetooth:
        return 'Bluetooth';
      case ConnectivityResult.vpn:
        return 'VPN';
      case ConnectivityResult.other:
        return 'Other';
      case ConnectivityResult.none:
        return 'No Connection';
    }
  }
}

/// Detailed connectivity information
class ConnectivityInfo {
  final ConnectivityResult connectivityResult;
  final bool hasInternet;
  final DateTime timestamp;

  ConnectivityInfo({
    required this.connectivityResult,
    required this.hasInternet,
    required this.timestamp,
  });

  bool get isConnected => hasInternet;
  
  String get connectionType {
    switch (connectivityResult) {
      case ConnectivityResult.wifi:
        return 'WiFi';
      case ConnectivityResult.mobile:
        return 'Mobile Data';
      case ConnectivityResult.ethernet:
        return 'Ethernet';
      case ConnectivityResult.bluetooth:
        return 'Bluetooth';
      case ConnectivityResult.vpn:
        return 'VPN';
      case ConnectivityResult.other:
        return 'Other';
      case ConnectivityResult.none:
        return 'No Connection';
    }
  }

  String get statusDescription {
    if (!hasInternet) {
      return 'No Internet Connection';
    }
    return 'Connected via $connectionType';
  }

  @override
  String toString() {
    return 'ConnectivityInfo(type: $connectionType, hasInternet: $hasInternet, timestamp: $timestamp)';
  }
}

/// Connectivity status enum for easier handling
enum ConnectionStatus {
  connected,
  disconnected,
  unknown,
}

/// Extension for connectivity result
extension ConnectivityResultExtension on ConnectivityResult {
  bool get hasConnection => this != ConnectivityResult.none;
  
  String get displayName {
    switch (this) {
      case ConnectivityResult.wifi:
        return 'WiFi';
      case ConnectivityResult.mobile:
        return 'Mobile Data';
      case ConnectivityResult.ethernet:
        return 'Ethernet';
      case ConnectivityResult.bluetooth:
        return 'Bluetooth';
      case ConnectivityResult.vpn:
        return 'VPN';
      case ConnectivityResult.other:
        return 'Other';
      case ConnectivityResult.none:
        return 'No Connection';
    }
  }
}
