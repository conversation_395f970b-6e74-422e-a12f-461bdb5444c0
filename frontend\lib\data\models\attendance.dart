import 'package:json_annotation/json_annotation.dart';

part 'attendance.g.dart';

@JsonSerializable()
class Attendance {
  final String id;
  final String employeeId;
  final String officeId;
  final String status;
  final String type;
  final String timestamp;
  final String? location;
  final String? notes;
  final String createdAt;
  final String updatedAt;
  final EmployeeInfo? employee;
  final OfficeInfo? office;

  const Attendance({
    required this.id,
    required this.employeeId,
    required this.officeId,
    required this.status,
    required this.type,
    required this.timestamp,
    this.location,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
    this.employee,
    this.office,
  });

  factory Attendance.fromJson(Map<String, dynamic> json) => _$AttendanceFromJson(json);
  Map<String, dynamic> toJson() => _$AttendanceToJson(this);

  Attendance copyWith({
    String? id,
    String? employeeId,
    String? officeId,
    String? status,
    String? type,
    String? timestamp,
    String? location,
    String? notes,
    String? createdAt,
    String? updatedAt,
    EmployeeInfo? employee,
    OfficeInfo? office,
  }) {
    return Attendance(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      officeId: officeId ?? this.officeId,
      status: status ?? this.status,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      location: location ?? this.location,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      employee: employee ?? this.employee,
      office: office ?? this.office,
    );
  }

  // Helper getters
  bool get isPresent => status == 'PRESENT';
  bool get isAbsent => status == 'ABSENT';
  bool get isLate => status == 'LATE';
  bool get isOnLeave => status == 'ON_LEAVE';
  
  bool get isCheckIn => type == 'CHECK_IN';
  bool get isCheckOut => type == 'CHECK_OUT';
  bool get isBreakStart => type == 'BREAK_START';
  bool get isBreakEnd => type == 'BREAK_END';
  
  DateTime get timestampDateTime => DateTime.parse(timestamp);
  DateTime get createdDateTime => DateTime.parse(createdAt);
  DateTime get updatedDateTime => DateTime.parse(updatedAt);
}

@JsonSerializable()
class EmployeeInfo {
  final String id;
  final String name;
  final String email;
  final String? department;
  final String? position;

  const EmployeeInfo({
    required this.id,
    required this.name,
    required this.email,
    this.department,
    this.position,
  });

  factory EmployeeInfo.fromJson(Map<String, dynamic> json) => _$EmployeeInfoFromJson(json);
  Map<String, dynamic> toJson() => _$EmployeeInfoToJson(this);
}

@JsonSerializable()
class OfficeInfo {
  final String id;
  final String name;
  final String type;
  final String? address;

  const OfficeInfo({
    required this.id,
    required this.name,
    required this.type,
    this.address,
  });

  factory OfficeInfo.fromJson(Map<String, dynamic> json) => _$OfficeInfoFromJson(json);
  Map<String, dynamic> toJson() => _$OfficeInfoToJson(this);
}

@JsonSerializable()
class AttendanceStatistics {
  final int totalEmployees;
  final int presentEmployees;
  final int absentEmployees;
  final int lateEmployees;
  final int onLeaveEmployees;
  final double attendanceRate;
  final Map<String, int> attendanceByDepartment;
  final Map<String, int> attendanceByHour;

  const AttendanceStatistics({
    required this.totalEmployees,
    required this.presentEmployees,
    required this.absentEmployees,
    required this.lateEmployees,
    required this.onLeaveEmployees,
    required this.attendanceRate,
    required this.attendanceByDepartment,
    required this.attendanceByHour,
  });

  factory AttendanceStatistics.fromJson(Map<String, dynamic> json) => _$AttendanceStatisticsFromJson(json);
  Map<String, dynamic> toJson() => _$AttendanceStatisticsToJson(this);

  factory AttendanceStatistics.empty() {
    return const AttendanceStatistics(
      totalEmployees: 0,
      presentEmployees: 0,
      absentEmployees: 0,
      lateEmployees: 0,
      onLeaveEmployees: 0,
      attendanceRate: 0.0,
      attendanceByDepartment: {},
      attendanceByHour: {},
    );
  }
}

@JsonSerializable()
class CreateAttendanceRequest {
  final String employeeId;
  final String officeId;
  final String status;
  final String type;
  final String timestamp;
  final String? location;
  final String? notes;

  const CreateAttendanceRequest({
    required this.employeeId,
    required this.officeId,
    required this.status,
    required this.type,
    required this.timestamp,
    this.location,
    this.notes,
  });

  factory CreateAttendanceRequest.fromJson(Map<String, dynamic> json) => _$CreateAttendanceRequestFromJson(json);
  Map<String, dynamic> toJson() => _$CreateAttendanceRequestToJson(this);
}

@JsonSerializable()
class UpdateAttendanceRequest {
  final String? status;
  final String? type;
  final String? timestamp;
  final String? location;
  final String? notes;

  const UpdateAttendanceRequest({
    this.status,
    this.type,
    this.timestamp,
    this.location,
    this.notes,
  });

  factory UpdateAttendanceRequest.fromJson(Map<String, dynamic> json) => _$UpdateAttendanceRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UpdateAttendanceRequestToJson(this);
}

@JsonSerializable()
class AttendanceQueryParams {
  final int? page;
  final int? limit;
  final String? status;
  final String? type;
  final String? employeeId;
  final String? officeId;
  final String? dateFrom;
  final String? dateTo;

  const AttendanceQueryParams({
    this.page,
    this.limit,
    this.status,
    this.type,
    this.employeeId,
    this.officeId,
    this.dateFrom,
    this.dateTo,
  });

  factory AttendanceQueryParams.fromJson(Map<String, dynamic> json) => _$AttendanceQueryParamsFromJson(json);
  Map<String, dynamic> toJson() => _$AttendanceQueryParamsToJson(this);

  Map<String, dynamic> toQueryParameters() {
    final params = <String, dynamic>{};
    if (page != null) params['page'] = page.toString();
    if (limit != null) params['limit'] = limit.toString();
    if (status != null) params['status'] = status;
    if (type != null) params['type'] = type;
    if (employeeId != null) params['employeeId'] = employeeId;
    if (officeId != null) params['officeId'] = officeId;
    if (dateFrom != null) params['dateFrom'] = dateFrom;
    if (dateTo != null) params['dateTo'] = dateTo;
    return params;
  }
}
