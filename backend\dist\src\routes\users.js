"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const client_1 = require("@prisma/client");
const prisma_1 = require("@/lib/prisma");
const auth_1 = require("@/lib/auth");
const validation_1 = require("@/lib/validation");
const errorHandler_1 = require("@/middleware/errorHandler");
const auth_2 = require("@/middleware/auth");
const rateLimiter_1 = require("@/middleware/rateLimiter");
const router = (0, express_1.Router)();
// Apply authentication and rate limiting
router.use(auth_2.authenticate);
router.use(rateLimiter_1.conditionalRateLimit);
/**
 * @swagger
 * /users:
 *   get:
 *     tags: [Users]
 *     summary: List users
 *     description: Get paginated list of users (Admin only)
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *       - in: query
 *         name: role
 *         schema:
 *           type: string
 *           enum: [SUPER_ADMIN, PROPERTY_MANAGER, OFFICE_MANAGER, SECURITY_PERSONNEL, MAINTENANCE_STAFF, CONSTRUCTION_SUPERVISOR]
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Users retrieved successfully
 */
router.get('/', (0, auth_2.authorize)([client_1.UserRole.SUPER_ADMIN], 'canManageUsers'), (0, errorHandler_1.validateRequest)(validation_1.paginationSchema, 'query'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { page, limit, role, search } = req.query;
    // Build where clause
    let whereClause = {};
    if (role)
        whereClause.role = role;
    if (search) {
        whereClause.OR = [
            { name: { contains: search, mode: 'insensitive' } },
            { email: { contains: search, mode: 'insensitive' } },
        ];
    }
    // Get total count
    const total = await prisma_1.prisma.user.count({ where: whereClause });
    // Get users with pagination
    const users = await prisma_1.prisma.user.findMany({
        where: whereClause,
        include: {
            assignedProperties: {
                include: {
                    property: {
                        select: {
                            id: true,
                            name: true,
                            type: true,
                        },
                    },
                },
            },
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
    });
    // Transform users to response format
    const usersResponse = users.map(user => ({
        id: user.id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        role: user.role,
        assignedProperties: user.assignedProperties.map(ap => ap.propertyId),
        isActive: user.isActive,
        avatar: user.avatar,
        timezone: user.timezone,
        language: user.language,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        lastLogin: user.lastLogin,
        permissions: auth_1.AuthService.getUserPermissions(user.role),
    }));
    const totalPages = Math.ceil(total / limit);
    const response = {
        data: usersResponse,
        pagination: {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1,
        },
    };
    res.json({
        success: true,
        data: response,
        timestamp: new Date().toISOString(),
    });
}));
/**
 * @swagger
 * /users:
 *   post:
 *     tags: [Users]
 *     summary: Create new user
 *     description: Create a new user (Admin only)
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateUserRequest'
 *     responses:
 *       201:
 *         description: User created successfully
 */
router.post('/', (0, auth_2.authorize)([client_1.UserRole.SUPER_ADMIN], 'canManageUsers'), (0, auth_2.requireAction)('create'), (0, errorHandler_1.validateRequest)(validation_1.createUserSchema), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const userData = req.body;
    const currentUser = req.user;
    // Check if email already exists
    const existingUser = await prisma_1.prisma.user.findUnique({
        where: { email: userData.email },
    });
    if (existingUser) {
        throw new errorHandler_1.AppError('Email already exists', 400, 'EMAIL_EXISTS');
    }
    // Generate password if not provided
    const password = userData.password || 'password123';
    const hashedPassword = await auth_1.AuthService.hashPassword(password);
    // Create user
    const user = await prisma_1.prisma.user.create({
        data: {
            name: userData.name,
            email: userData.email,
            phone: userData.phone,
            password: hashedPassword,
            role: userData.role,
            isActive: true,
            timezone: 'Asia/Kolkata',
            language: 'en',
        },
    });
    // Assign properties if provided
    if (userData.assignedProperties && userData.assignedProperties.length > 0) {
        await prisma_1.prisma.userProperty.createMany({
            data: userData.assignedProperties.map(propertyId => ({
                userId: user.id,
                propertyId,
            })),
        });
    }
    // Log activity
    await prisma_1.prisma.activity.create({
        data: {
            userId: currentUser.id,
            action: 'CREATE_USER',
            description: `Created user: ${user.name} (${user.email})`,
            metadata: {
                newUserId: user.id,
                role: user.role,
            },
            ipAddress: req.ip,
            userAgent: req.headers['user-agent'],
        },
    });
    // Prepare response (exclude password)
    const userResponse = {
        id: user.id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        role: user.role,
        assignedProperties: userData.assignedProperties || [],
        isActive: user.isActive,
        avatar: user.avatar,
        timezone: user.timezone,
        language: user.language,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        lastLogin: user.lastLogin,
        permissions: auth_1.AuthService.getUserPermissions(user.role),
    };
    res.status(201).json({
        success: true,
        data: userResponse,
        message: 'User created successfully',
        timestamp: new Date().toISOString(),
    });
}));
/**
 * @swagger
 * /users/{userId}:
 *   get:
 *     tags: [Users]
 *     summary: Get user details
 *     description: Get specific user details (Admin only)
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: User retrieved successfully
 *       404:
 *         description: User not found
 */
router.get('/:userId', (0, auth_2.authorize)([client_1.UserRole.SUPER_ADMIN], 'canManageUsers'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { userId } = req.params;
    const user = await prisma_1.prisma.user.findUnique({
        where: { id: userId },
        include: {
            assignedProperties: {
                include: {
                    property: {
                        select: {
                            id: true,
                            name: true,
                            type: true,
                        },
                    },
                },
            },
        },
    });
    if (!user) {
        throw new errorHandler_1.AppError('User not found', 404, 'USER_NOT_FOUND');
    }
    const userResponse = {
        id: user.id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        role: user.role,
        assignedProperties: user.assignedProperties.map(ap => ap.propertyId),
        isActive: user.isActive,
        avatar: user.avatar,
        timezone: user.timezone,
        language: user.language,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        lastLogin: user.lastLogin,
        permissions: auth_1.AuthService.getUserPermissions(user.role),
    };
    res.json({
        success: true,
        data: userResponse,
        timestamp: new Date().toISOString(),
    });
}));
/**
 * @swagger
 * /users/{userId}:
 *   put:
 *     tags: [Users]
 *     summary: Update user
 *     description: Update user information (Admin only)
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateUserRequest'
 *     responses:
 *       200:
 *         description: User updated successfully
 */
router.put('/:userId', (0, auth_2.authorize)([client_1.UserRole.SUPER_ADMIN], 'canManageUsers'), (0, auth_2.requireAction)('update'), (0, errorHandler_1.validateRequest)(validation_1.updateUserSchema), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { userId } = req.params;
    const updateData = req.body;
    const currentUser = req.user;
    // Check if user exists
    const existingUser = await prisma_1.prisma.user.findUnique({
        where: { id: userId },
    });
    if (!existingUser) {
        throw new errorHandler_1.AppError('User not found', 404, 'USER_NOT_FOUND');
    }
    // Update user
    const updatedUser = await prisma_1.prisma.user.update({
        where: { id: userId },
        data: updateData,
    });
    // Log activity
    await prisma_1.prisma.activity.create({
        data: {
            userId: currentUser.id,
            action: 'UPDATE_USER',
            description: `Updated user: ${updatedUser.name} (${updatedUser.email})`,
            metadata: {
                updatedUserId: userId,
                changes: updateData,
            },
            ipAddress: req.ip,
            userAgent: req.headers['user-agent'],
        },
    });
    const userResponse = {
        id: updatedUser.id,
        name: updatedUser.name,
        email: updatedUser.email,
        phone: updatedUser.phone,
        role: updatedUser.role,
        assignedProperties: [], // Would need to fetch separately
        isActive: updatedUser.isActive,
        avatar: updatedUser.avatar,
        timezone: updatedUser.timezone,
        language: updatedUser.language,
        createdAt: updatedUser.createdAt,
        updatedAt: updatedUser.updatedAt,
        lastLogin: updatedUser.lastLogin,
        permissions: auth_1.AuthService.getUserPermissions(updatedUser.role),
    };
    res.json({
        success: true,
        data: userResponse,
        message: 'User updated successfully',
        timestamp: new Date().toISOString(),
    });
}));
exports.default = router;
