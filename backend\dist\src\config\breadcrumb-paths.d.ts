export interface BreadcrumbPathDefinition {
    path: string;
    name: string;
    description?: string;
    level: number;
    parentPath?: string;
    components?: ComponentDefinition[];
    metadata?: Record<string, any>;
}
export interface ComponentDefinition {
    componentId: string;
    name: string;
    type: 'widget' | 'button' | 'form' | 'table' | 'card' | 'chart' | 'input' | 'display';
    section?: string;
    permissions: string[];
    metadata?: Record<string, any>;
}
export declare const BREADCRUMB_PATHS: BreadcrumbPathDefinition[];
