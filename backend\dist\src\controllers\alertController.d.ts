import { Request, Response } from 'express';
export declare const getAlerts: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const getAlert: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const createAlert: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const updateAlertStatus: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const deleteAlert: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const getAlertStatistics: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
