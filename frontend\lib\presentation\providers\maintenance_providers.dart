import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/models/maintenance.dart';
import '../../data/repositories/maintenance_repository.dart';
import '../../core/services/service_locator.dart';

import '../providers/auth_providers.dart';

// Maintenance Repository Provider
final maintenanceRepositoryProvider = Provider<MaintenanceRepository>((ref) {
  return serviceLocator.maintenanceRepository;
});

// Cache Manager Provider
final cacheManagerProvider = Provider((ref) {
  return serviceLocator.cacheManager;
});

// Connectivity Manager Provider
final connectivityManagerProvider = Provider((ref) {
  return serviceLocator.connectivityManager;
});

// Maintenance Issues Provider with Caching and Offline Support
final maintenanceIssuesProvider = FutureProvider.family<List<MaintenanceIssue>, MaintenanceParams>((ref, params) async {
  final repository = ref.read(maintenanceRepositoryProvider);
  final cacheManager = ref.read(cacheManagerProvider);
  final connectivityManager = ref.read(connectivityManagerProvider);
  final userPermissions = ref.read(userPermissionsProvider);

  // Check RBAC permissions
  if (!userPermissions.canViewMaintenance) {
    throw UnauthorizedException('Insufficient permissions to view maintenance data');
  }

  // Apply property-based filtering
  final allowedPropertyIds = userPermissions.assignedPropertyIds;
  
  final cacheKey = 'maintenance_issues_${params.hashCode}';
  
  try {
    // Check connectivity
    final isConnected = connectivityManager.isOnline;
    
    if (!isConnected) {
      // Return cached data if offline
      final cachedData = await cacheManager.getCachedData<List<dynamic>>(cacheKey);
      if (cachedData != null) {
        final issues = cachedData.map((json) => MaintenanceIssue.fromJson(json)).toList();
        return issues.where((issue) =>
          allowedPropertyIds.isEmpty || allowedPropertyIds.contains(issue.propertyId)
        ).toList();
      }
      throw OfflineException('No internet connection and no cached data available');
    }

    // Fetch from API
    final response = await repository.getMaintenanceIssues(
      propertyId: params.propertyId,
      status: params.status,
      priority: params.priority,
      department: params.department,
      page: params.page,
      limit: params.limit,
      assignedTo: params.assignedTo,
    );

    if (response.isSuccess && response.data != null) {
      final issues = response.data!;
      
      // Apply property-based filtering
      final filteredIssues = issues.where((issue) => 
        allowedPropertyIds.isEmpty || allowedPropertyIds.contains(issue.propertyId)
      ).toList();
      
      // Cache the filtered data
      await cacheManager.cacheData(cacheKey, filteredIssues.map((issue) => issue.toJson()).toList(), duration: const Duration(minutes: 5));
      
      return filteredIssues;
    } else {
      // Try to return cached data on API failure
      final cachedData = await cacheManager.getCachedData<List<dynamic>>(cacheKey);
      if (cachedData != null) {
        final issues = cachedData.map((json) => MaintenanceIssue.fromJson(json)).toList();
        return issues.where((issue) =>
          allowedPropertyIds.isEmpty || allowedPropertyIds.contains(issue.propertyId)
        ).toList();
      }
      throw ApiException(response.message ?? 'Failed to fetch maintenance issues');
    }
  } catch (e) {
    // Enhanced error handling with retry mechanism
    if (e is! OfflineException && e is! UnauthorizedException) {
      // Try cached data as fallback
      final cachedData = await cacheManager.getCachedData<List<dynamic>>(cacheKey);
      if (cachedData != null) {
        final issues = cachedData.map((json) => MaintenanceIssue.fromJson(json)).toList();
        return issues.where((issue) =>
          allowedPropertyIds.isEmpty || allowedPropertyIds.contains(issue.propertyId)
        ).toList();
      }
    }
    rethrow;
  }
});

// Maintenance Statistics Provider with Caching
final maintenanceStatisticsProvider = FutureProvider.family<MaintenanceStatistics, String?>((ref, propertyId) async {
  final repository = ref.read(maintenanceRepositoryProvider);
  final cacheManager = ref.read(cacheManagerProvider);
  final userPermissions = ref.read(userPermissionsProvider);

  // Check RBAC permissions
  if (!userPermissions.canViewMaintenance) {
    throw UnauthorizedException('Insufficient permissions to view maintenance statistics');
  }

  final cacheKey = 'maintenance_stats_${propertyId ?? 'all'}';
  
  try {
    // Check cache first
    final cachedStats = await cacheManager.getCachedData<Map<String, dynamic>>(cacheKey);
    if (cachedStats != null) {
      return MaintenanceStatistics.fromJson(cachedStats);
    }

    final response = await repository.getMaintenanceStatistics(propertyId: propertyId);
    
    if (response.success && response.data != null) {
      final stats = MaintenanceStatistics.fromJson(response.data!);

      // Cache for 10 minutes
      await cacheManager.cacheData(cacheKey, stats.toJson(), duration: const Duration(minutes: 10));

      return stats;
    } else {
      throw ApiException(response.message ?? 'Failed to fetch maintenance statistics');
    }
  } catch (e) {
    // Return cached data on error if available
    final cachedStats = await cacheManager.getCachedData<Map<String, dynamic>>(cacheKey);
    if (cachedStats != null) {
      return MaintenanceStatistics.fromJson(cachedStats);
    }
    rethrow;
  }
});

// Maintenance Issue Detail Provider
final maintenanceIssueDetailProvider = FutureProvider.family<MaintenanceIssue?, String>((ref, issueId) async {
  final repository = ref.read(maintenanceRepositoryProvider);
  final userPermissions = ref.read(userPermissionsProvider);

  // Check RBAC permissions
  if (!userPermissions.canViewMaintenance) {
    throw UnauthorizedException('Insufficient permissions to view maintenance details');
  }

  try {
    final response = await repository.getMaintenanceIssueDetail(issueId);
    
    if (response.success && response.data != null) {
      final issue = response.data!;
      
      // Check property-based access
      final allowedPropertyIds = userPermissions.assignedPropertyIds;
      if (allowedPropertyIds.isNotEmpty && !allowedPropertyIds.contains(issue.propertyId)) {
        throw UnauthorizedException('Access denied to this property\'s maintenance data');
      }
      
      return issue;
    } else {
      throw ApiException(response.message ?? 'Failed to fetch maintenance issue details');
    }
  } catch (e) {
    rethrow;
  }
});

// Filtered Maintenance Issues Provider with Search and Filters
final filteredMaintenanceIssuesProvider = FutureProvider<List<MaintenanceIssue>>((ref) async {
  final searchQuery = ref.watch(maintenanceSearchQueryProvider);
  final selectedStatus = ref.watch(maintenanceStatusFilterProvider);
  final selectedPriority = ref.watch(maintenancePriorityFilterProvider);
  final selectedDepartment = ref.watch(maintenanceDepartmentFilterProvider);
  final selectedProperty = ref.watch(maintenancePropertyFilterProvider);

  final params = MaintenanceParams(
    propertyId: selectedProperty == 'all' ? null : selectedProperty,
    status: selectedStatus == 'all' ? null : selectedStatus,
    priority: selectedPriority == 'all' ? null : selectedPriority,
    department: selectedDepartment == 'all' ? null : selectedDepartment,
    page: 1,
    limit: 100,
  );

  final issues = await ref.watch(maintenanceIssuesProvider(params).future);
  
  // Apply search filter
  if (searchQuery.isEmpty) {
    return issues;
  }
  
  return issues.where((issue) =>
    issue.title.toLowerCase().contains(searchQuery.toLowerCase()) ||
    (issue.description?.toLowerCase().contains(searchQuery.toLowerCase()) ?? false) ||
    (issue.department?.name.toLowerCase().contains(searchQuery.toLowerCase()) ?? false)
  ).toList();
});

// State Providers for Filters and Search
final maintenanceSearchQueryProvider = StateProvider<String>((ref) => '');
final maintenanceStatusFilterProvider = StateProvider<String>((ref) => 'all');
final maintenancePriorityFilterProvider = StateProvider<String>((ref) => 'all');
final maintenanceDepartmentFilterProvider = StateProvider<String>((ref) => 'all');
final maintenancePropertyFilterProvider = StateProvider<String>((ref) => 'all');

// Retry Provider for Failed Operations
final maintenanceRetryProvider = StateProvider<int>((ref) => 0);

// Maintenance Parameters Class
class MaintenanceParams {
  final String? propertyId;
  final String? status;
  final String? priority;
  final String? department;
  final int page;
  final int limit;
  final String? assignedTo;

  const MaintenanceParams({
    this.propertyId,
    this.status,
    this.priority,
    this.department,
    this.page = 1,
    this.limit = 20,
    this.assignedTo,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MaintenanceParams &&
          runtimeType == other.runtimeType &&
          propertyId == other.propertyId &&
          status == other.status &&
          priority == other.priority &&
          department == other.department &&
          page == other.page &&
          limit == other.limit &&
          assignedTo == other.assignedTo;

  @override
  int get hashCode =>
      propertyId.hashCode ^
      status.hashCode ^
      priority.hashCode ^
      department.hashCode ^
      page.hashCode ^
      limit.hashCode ^
      assignedTo.hashCode;
}

// Custom Exceptions
class ApiException implements Exception {
  final String message;
  ApiException(this.message);
  
  @override
  String toString() => 'ApiException: $message';
}

class OfflineException implements Exception {
  final String message;
  OfflineException(this.message);
  
  @override
  String toString() => 'OfflineException: $message';
}

class UnauthorizedException implements Exception {
  final String message;
  UnauthorizedException(this.message);
  
  @override
  String toString() => 'UnauthorizedException: $message';
}

// Create Maintenance Issue Provider
final createMaintenanceIssueProvider = FutureProvider.family<bool, CreateMaintenanceParams>((ref, params) async {
  final repository = ref.read(maintenanceRepositoryProvider);
  final userPermissions = ref.read(userPermissionsProvider);

  // Check RBAC permissions
  if (!userPermissions.canCreateMaintenance) {
    throw UnauthorizedException('Insufficient permissions to create maintenance issues');
  }

  try {
    final response = await repository.createMaintenanceIssue(
      propertyId: params.propertyId,
      title: params.title,
      description: params.description,
      priority: params.priority,
      department: params.department,
      assignedTo: params.assignedTo,
    );

    if (response.success) {
      // Invalidate related providers to refresh data
      ref.invalidate(maintenanceIssuesProvider);
      ref.invalidate(maintenanceStatisticsProvider);
      return true;
    } else {
      throw ApiException(response.message ?? 'Failed to create maintenance issue');
    }
  } catch (e) {
    rethrow;
  }
});

// Create Maintenance Parameters
class CreateMaintenanceParams {
  final String propertyId;
  final String title;
  final String description;
  final String priority;
  final String department;
  final String? assignedTo;

  const CreateMaintenanceParams({
    required this.propertyId,
    required this.title,
    required this.description,
    required this.priority,
    required this.department,
    this.assignedTo,
  });
}
