import '../models/api_response.dart';
import '../models/office.dart';
import '../../core/services/api_client.dart';
import '../../core/services/service_locator.dart';
import '../../core/constants/api_constants.dart';

class OfficeRepository {
  final ApiClient _apiClient = serviceLocator.apiClient;

  Future<ApiResponse<List<Office>>> getOffices({
    String? type,
    String? status,
    String? search,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (type != null) queryParams['type'] = type;
      if (status != null) queryParams['status'] = status;
      if (search != null && search.isNotEmpty) queryParams['search'] = search;

      final response = await _apiClient.get(
        ApiConstants.officesList,
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        final offices = data.map((json) => Office.fromJson(json)).toList();
        return ApiResponse.success(data: offices);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch offices: $e');
    }
  }

  Future<ApiResponse<Office>> getOffice(String id) async {
    try {
      final endpoint = ApiConstants.officeDetail.replaceAll('{officeId}', id);
      final response = await _apiClient.get(endpoint);

      if (response.isSuccess) {
        final office = Office.fromJson(response.data['data']);
        return ApiResponse.success(data: office);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch office: $e');
    }
  }

  Future<ApiResponse<Office>> createOffice(Office office) async {
    try {
      final response = await _apiClient.post(
        ApiConstants.officesList,
        data: office.toJson(),
      );

      if (response.isSuccess) {
        final createdOffice = Office.fromJson(response.data['data']);
        return ApiResponse.success(data: createdOffice);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to create office: $e');
    }
  }

  Future<ApiResponse<Office>> updateOffice(String id, Office office) async {
    try {
      final endpoint = ApiConstants.officeDetail.replaceAll('{officeId}', id);
      final response = await _apiClient.put(
        endpoint,
        data: office.toJson(),
      );

      if (response.isSuccess) {
        final updatedOffice = Office.fromJson(response.data['data']);
        return ApiResponse.success(data: updatedOffice);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to update office: $e');
    }
  }

  Future<ApiResponse<void>> deleteOffice(String id) async {
    try {
      final endpoint = ApiConstants.officeDetail.replaceAll('{officeId}', id);
      final response = await _apiClient.delete(endpoint);

      if (response.isSuccess) {
        return ApiResponse.success(data: null);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to delete office: $e');
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getOfficeEmployees(String officeId) async {
    try {
      final endpoint = ApiConstants.officeEmployees.replaceAll('{officeId}', officeId);
      final response = await _apiClient.get(endpoint);

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data: data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch office employees: $e');
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getOfficeAttendance({
    required String officeId,
    String? date,
    String? startDate,
    String? endDate,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (date != null) queryParams['date'] = date;
      if (startDate != null) queryParams['startDate'] = startDate;
      if (endDate != null) queryParams['endDate'] = endDate;

      final endpoint = ApiConstants.officeAttendance.replaceAll('{officeId}', officeId);
      final response = await _apiClient.get(
        endpoint,
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data: data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch office attendance: $e');
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> markAttendance({
    required String officeId,
    required String employeeId,
    required String status,
    String? notes,
  }) async {
    try {
      final endpoint = ApiConstants.officeAttendance.replaceAll('{officeId}', officeId);
      final response = await _apiClient.post(
        endpoint,
        data: {
          'employeeId': employeeId,
          'status': status,
          if (notes != null) 'notes': notes,
        },
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data['data']);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to mark attendance: $e');
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> getOfficeStatistics({
    String? officeId,
    String? dateFrom,
    String? dateTo,
  }) async {
    try {
      final queryParams = <String, dynamic>{};

      if (officeId != null) queryParams['officeId'] = officeId;
      if (dateFrom != null) queryParams['dateFrom'] = dateFrom;
      if (dateTo != null) queryParams['dateTo'] = dateTo;

      final response = await _apiClient.get(
        '${ApiConstants.officesList}/stats',
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data['data']);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch office statistics: $e');
    }
  }

  Future<ApiResponse<OfficeDetail>> getOfficeDetail(String id) async {
    try {
      final endpoint = ApiConstants.officeDetail.replaceAll('{officeId}', id);
      final response = await _apiClient.get(endpoint);

      if (response.isSuccess) {
        final officeDetail = OfficeDetail.fromJson(response.data['data']);
        return ApiResponse.success(data: officeDetail);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch office detail: $e');
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> submitAttendance({
    required String officeId,
    required String employeeId,
    required String status,
    String? notes,
  }) async {
    try {
      final endpoint = ApiConstants.officeAttendance.replaceAll('{officeId}', officeId);
      final response = await _apiClient.post(
        endpoint,
        data: {
          'employeeId': employeeId,
          'status': status,
          if (notes != null) 'notes': notes,
        },
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data['data']);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to submit attendance: $e');
    }
  }
}

// Office detail model for extended office information
class OfficeDetail extends Office {
  final List<Map<String, dynamic>> employees;
  final List<Map<String, dynamic>> attendance;
  final Map<String, dynamic> statistics;

  OfficeDetail({
    required super.id,
    required super.name,
    required super.type,
    required super.address,
    required super.city,
    required super.state,
    required super.zipCode,
    required super.country,
    super.description,
    super.capacity,
    super.currentOccupancy,
    super.managerId,
    required super.status,
    super.amenities,
    super.workingHours,
    super.contactInfo,
    super.coordinates,
    super.latitude,
    super.longitude,
    required super.isActive,
    required super.createdAt,
    required super.updatedAt,
    required this.employees,
    required this.attendance,
    required this.statistics,
  });

  factory OfficeDetail.fromJson(Map<String, dynamic> json) {
    final office = Office.fromJson(json);
    return OfficeDetail(
      id: office.id,
      name: office.name,
      type: office.type,
      address: office.address,
      city: office.city,
      state: office.state,
      zipCode: office.zipCode,
      country: office.country,
      description: office.description,
      capacity: office.capacity,
      currentOccupancy: office.currentOccupancy,
      managerId: office.managerId,
      status: office.status,
      amenities: office.amenities,
      workingHours: office.workingHours,
      contactInfo: office.contactInfo,
      coordinates: office.coordinates,
      isActive: office.isActive,
      createdAt: office.createdAt,
      updatedAt: office.updatedAt,
      employees: List<Map<String, dynamic>>.from(json['employees'] ?? []),
      attendance: List<Map<String, dynamic>>.from(json['attendance'] ?? []),
      statistics: Map<String, dynamic>.from(json['statistics'] ?? {}),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'employees': employees,
      'attendance': attendance,
      'statistics': statistics,
    });
    return json;
  }
}

// Attendance model
class Attendance {
  final String id;
  final String employeeId;
  final String employeeName;
  final String officeId;
  final String status;
  final DateTime date;
  final DateTime? checkInTime;
  final DateTime? checkOutTime;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Attendance({
    required this.id,
    required this.employeeId,
    required this.employeeName,
    required this.officeId,
    required this.status,
    required this.date,
    this.checkInTime,
    this.checkOutTime,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Attendance.fromJson(Map<String, dynamic> json) {
    return Attendance(
      id: json['id'],
      employeeId: json['employeeId'],
      employeeName: json['employeeName'] ?? '',
      officeId: json['officeId'],
      status: json['status'],
      date: DateTime.parse(json['date']),
      checkInTime: json['checkInTime'] != null ? DateTime.parse(json['checkInTime']) : null,
      checkOutTime: json['checkOutTime'] != null ? DateTime.parse(json['checkOutTime']) : null,
      notes: json['notes'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'employeeId': employeeId,
      'employeeName': employeeName,
      'officeId': officeId,
      'status': status,
      'date': date.toIso8601String(),
      'checkInTime': checkInTime?.toIso8601String(),
      'checkOutTime': checkOutTime?.toIso8601String(),
      'notes': notes,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}
