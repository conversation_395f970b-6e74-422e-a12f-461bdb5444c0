import { Request, Response, NextFunction } from 'express';
import { UserRole } from '@prisma/client';
export interface PermissionContext {
    userId: string;
    role: UserRole;
    propertyId?: string;
    officeId?: string;
    resourceId?: string;
    timestamp: Date;
    ipAddress?: string;
    userAgent?: string;
}
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                email: string;
                role: UserRole;
                assignedProperties: string[];
            };
            permissionContext?: PermissionContext;
            permissionResult?: any;
            filteredQuery?: any;
            uiPermissions?: any;
        }
    }
}
export interface AuthenticatedRequest extends Request {
    user: {
        id: string;
        email: string;
        role: UserRole;
        assignedProperties: string[];
    };
}
export declare const authenticate: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const authorize: (requiredRoles?: UserRole[], requiredPermission?: keyof import("@/types").UserPermissions) => (req: Request, res: Response, next: NextFunction) => void;
export declare const requirePropertyAccess: (propertyIdParam?: string) => (req: Request, res: Response, next: NextFunction) => void;
export declare const requireAction: (action: "create" | "read" | "update" | "delete" | "export" | "import") => (req: Request, res: Response, next: NextFunction) => void;
export declare const optionalAuth: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const enhancedAuthorize: (resource: string, action: string, options?: {
    requirePropertyAccess?: boolean;
    requireOfficeAccess?: boolean;
    customConditions?: Record<string, any>;
}) => (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const checkPermission: (permission: string) => (req: Request, res: Response, next: NextFunction) => void;
export declare const authenticateToken: (req: Request, res: Response, next: NextFunction) => Promise<void>;
