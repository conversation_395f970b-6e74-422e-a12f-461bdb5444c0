import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/models/user.dart';
import '../../data/repositories/auth_repository.dart';
import '../../core/services/service_locator.dart';

// Auth Repository Provider
final authRepositoryProvider = Provider<AuthRepository>((ref) {
  return serviceLocator.authRepository;
});

// Current User Provider
final currentUserProvider = FutureProvider<User?>((ref) async {
  final repository = ref.read(authRepositoryProvider);
  
  try {
    final response = await repository.getCurrentUser();
    if (response.success && response.data != null) {
      return response.data!;
    }
    return null;
  } catch (e) {
    return null;
  }
});

// User Permissions Provider with RBAC
final userPermissionsProvider = Provider<UserPermissions>((ref) {
  final userAsyncValue = ref.watch(currentUserProvider);
  
  return userAsyncValue.when(
    data: (user) => user != null ? UserPermissions.fromUser(user) : UserPermissions.guest(),
    loading: () => UserPermissions.guest(),
    error: (_, __) => UserPermissions.guest(),
  );
});

// Breadcrumb-based Permission Provider
final breadcrumbPermissionsProvider = Provider.family<BreadcrumbPermissions, String>((ref, breadcrumbPath) {
  final userPermissions = ref.watch(userPermissionsProvider);
  return BreadcrumbPermissions.fromPath(breadcrumbPath, userPermissions);
});

// Tab-level Permission Provider
final tabPermissionsProvider = Provider.family<TabPermissions, TabContext>((ref, context) {
  final userPermissions = ref.watch(userPermissionsProvider);
  return TabPermissions.fromContext(context, userPermissions);
});

/// Comprehensive RBAC Permissions System
class UserPermissions {
  final String userId;
  final String role;
  final List<String> permissions;
  final List<String> assignedPropertyIds;
  final List<String> assignedOfficeIds;
  final Map<String, dynamic> roleConfig;
  final Map<String, List<String>> screenPermissions;
  final Map<String, List<String>> tabPermissions;

  const UserPermissions({
    required this.userId,
    required this.role,
    required this.permissions,
    required this.assignedPropertyIds,
    required this.assignedOfficeIds,
    required this.roleConfig,
    required this.screenPermissions,
    required this.tabPermissions,
  });

  factory UserPermissions.fromUser(User user) {
    // Convert the UserPermissions object to a list of permission strings
    final permissionsList = <String>[];
    if (user.permissions.canViewDashboard) permissionsList.add('dashboard.view');
    if (user.permissions.canManageProperties) permissionsList.addAll(['properties.view', 'properties.create', 'properties.update', 'properties.delete']);
    if (user.permissions.canManageOffice) permissionsList.addAll(['offices.view', 'offices.create', 'offices.update', 'offices.delete']);
    if (user.permissions.canManageSecurity) permissionsList.addAll(['security.view', 'security.manage']);
    if (user.permissions.canManageMaintenance) permissionsList.addAll(['maintenance.view', 'maintenance.create', 'maintenance.update', 'maintenance.delete']);
    if (user.permissions.canManageUsers) permissionsList.addAll(['users.view', 'users.manage']);
    if (user.permissions.canViewReports) permissionsList.add('reports.view');
    if (user.permissions.canExportData) permissionsList.add('data.export');
    if (user.permissions.canManageWaterSystems) permissionsList.addAll(['systems.water.view', 'systems.water.control']);
    if (user.permissions.canManageElectricitySystems) permissionsList.addAll(['systems.electricity.view', 'systems.electricity.control']);
    if (user.permissions.canManageSecuritySystems) permissionsList.addAll(['systems.security.view', 'systems.security.control']);

    return UserPermissions(
      userId: user.id,
      role: user.role,
      permissions: permissionsList,
      assignedPropertyIds: user.assignedPropertyIds,
      assignedOfficeIds: user.assignedOfficeIds,
      roleConfig: user.roleConfig ?? {},
      screenPermissions: _parseScreenPermissions(permissionsList),
      tabPermissions: _parseTabPermissions(permissionsList),
    );
  }

  factory UserPermissions.guest() {
    return const UserPermissions(
      userId: '',
      role: 'GUEST',
      permissions: [],
      assignedPropertyIds: [],
      assignedOfficeIds: [],
      roleConfig: {},
      screenPermissions: {},
      tabPermissions: {},
    );
  }

  // Screen-level permissions
  bool get canViewDashboard => hasPermission('dashboard.view') || isAdmin;
  bool get canViewProperties => hasPermission('properties.view') || isAdmin;
  bool get canViewMaintenance => hasPermission('maintenance.view') || isAdmin;
  bool get canViewOffices => hasPermission('offices.view') || isAdmin;
  bool get canViewSystems => hasPermission('systems.view') || isAdmin;
  bool get canViewSettings => hasPermission('settings.view') || isAdmin;

  // Action-level permissions
  bool get canCreateMaintenance => hasPermission('maintenance.create') || isAdmin;
  bool get canUpdateMaintenance => hasPermission('maintenance.update') || isAdmin;
  bool get canDeleteMaintenance => hasPermission('maintenance.delete') || isAdmin;
  bool get canAssignMaintenance => hasPermission('maintenance.assign') || isAdmin;
  bool get canExportAnalytics => hasPermission('maintenance.analytics.export') || isAdmin;
  bool get canConfigureAnalytics => hasPermission('maintenance.analytics.settings') || isAdmin;

  bool get canCreateProperty => hasPermission('properties.create') || isAdmin;
  bool get canUpdateProperty => hasPermission('properties.update') || isAdmin;
  bool get canDeleteProperty => hasPermission('properties.delete') || isAdmin;

  // Employee permissions
  bool get canViewEmployees => hasPermission('employees.view') || isAdmin;
  bool get canCreateEmployees => hasPermission('employees.create') || isAdmin;
  bool get canUpdateEmployees => hasPermission('employees.update') || isAdmin;
  bool get canDeleteEmployees => hasPermission('employees.delete') || isAdmin;
  bool get canExportEmployees => hasPermission('employees.export') || isAdmin;

  bool get canManageUsers => hasPermission('users.manage') || isAdmin;
  bool get canViewReports => hasPermission('reports.view') || isAdmin;
  bool get canExportData => hasPermission('data.export') || isAdmin;

  // System-specific permissions
  bool get canViewWaterSystems => hasPermission('systems.water.view') || isAdmin;
  bool get canControlWaterSystems => hasPermission('systems.water.control') || isAdmin;
  bool get canViewElectricitySystems => hasPermission('systems.electricity.view') || isAdmin;
  bool get canControlElectricitySystems => hasPermission('systems.electricity.control') || isAdmin;
  bool get canViewSecuritySystems => hasPermission('systems.security.view') || isAdmin;
  bool get canControlSecuritySystems => hasPermission('systems.security.control') || isAdmin;

  // Role checks
  bool get isAdmin => role == 'ADMIN' || role == 'SUPER_ADMIN';
  bool get isManager => role == 'MANAGER' || isAdmin;
  bool get isEmployee => role == 'EMPLOYEE' || isManager;
  bool get isSecurityPersonnel => role == 'SECURITY_PERSONNEL';
  bool get isMaintenancePersonnel => role == 'MAINTENANCE_PERSONNEL';
  bool get isGuest => role == 'GUEST';

  // Property-based access control
  bool canAccessProperty(String propertyId) {
    if (isAdmin) return true;
    return assignedPropertyIds.isEmpty || assignedPropertyIds.contains(propertyId);
  }

  bool canAccessOffice(String officeId) {
    if (isAdmin) return true;
    return assignedOfficeIds.isEmpty || assignedOfficeIds.contains(officeId);
  }

  // Screen access with breadcrumb path
  bool canAccessScreen(String screenPath) {
    if (isAdmin) return true;
    
    final screenPerms = screenPermissions[screenPath];
    if (screenPerms == null) return false;
    
    return screenPerms.any((perm) => hasPermission(perm));
  }

  // Tab access within screens
  bool canAccessTab(String screenPath, String tabName) {
    if (isAdmin) return true;
    
    final tabKey = '$screenPath.$tabName';
    final tabPerms = tabPermissions[tabKey];
    if (tabPerms == null) return false;
    
    return tabPerms.any((perm) => hasPermission(perm));
  }

  // Generic permission check
  bool hasPermission(String permission) {
    return permissions.contains(permission) || permissions.contains('*');
  }

  // Check multiple permissions (OR logic)
  bool hasAnyPermission(List<String> permissionList) {
    return permissionList.any((perm) => hasPermission(perm));
  }

  // Check multiple permissions (AND logic)
  bool hasAllPermissions(List<String> permissionList) {
    return permissionList.every((perm) => hasPermission(perm));
  }

  static Map<String, List<String>> _parseScreenPermissions(List<String> permissions) {
    final Map<String, List<String>> screenPerms = {};
    
    // Define screen permission mappings
    screenPerms['dashboard'] = ['dashboard.view'];
    screenPerms['properties'] = ['properties.view'];
    screenPerms['properties.detail'] = ['properties.view', 'properties.detail'];
    screenPerms['maintenance'] = ['maintenance.view'];
    screenPerms['offices'] = ['offices.view'];
    screenPerms['systems'] = ['systems.view'];
    screenPerms['systems.water'] = ['systems.water.view'];
    screenPerms['systems.electricity'] = ['systems.electricity.view'];
    screenPerms['systems.security'] = ['systems.security.view'];
    screenPerms['settings'] = ['settings.view'];
    
    return screenPerms;
  }

  static Map<String, List<String>> _parseTabPermissions(List<String> permissions) {
    final Map<String, List<String>> tabPerms = {};
    
    // Define tab permission mappings
    tabPerms['properties.residential'] = ['properties.view'];
    tabPerms['properties.office'] = ['properties.view'];
    tabPerms['properties.construction'] = ['properties.view'];
    
    tabPerms['offices.locations'] = ['offices.view'];
    tabPerms['offices.construction_sites'] = ['offices.view'];
    
    tabPerms['systems.water.overview'] = ['systems.water.view'];
    tabPerms['systems.water.tanks'] = ['systems.water.view'];
    tabPerms['systems.water.pumps'] = ['systems.water.view'];
    
    tabPerms['systems.electricity.overview'] = ['systems.electricity.view'];
    tabPerms['systems.electricity.generator'] = ['systems.electricity.view'];
    tabPerms['systems.electricity.consumption'] = ['systems.electricity.view'];
    
    tabPerms['systems.security.overview'] = ['systems.security.view'];
    tabPerms['systems.security.cameras'] = ['systems.security.view'];
    tabPerms['systems.security.access'] = ['systems.security.view'];
    
    return tabPerms;
  }
}

/// Breadcrumb-based permissions for granular access control
class BreadcrumbPermissions {
  final String path;
  final bool canAccess;
  final List<String> requiredPermissions;
  final String? denialReason;

  const BreadcrumbPermissions({
    required this.path,
    required this.canAccess,
    required this.requiredPermissions,
    this.denialReason,
  });

  factory BreadcrumbPermissions.fromPath(String path, UserPermissions userPermissions) {
    final pathSegments = path.split(' > ');
    final requiredPerms = _getRequiredPermissionsForPath(pathSegments);
    
    final canAccess = userPermissions.isAdmin || 
                     requiredPerms.any((perm) => userPermissions.hasPermission(perm));
    
    String? denialReason;
    if (!canAccess) {
      denialReason = 'Insufficient permissions. Required: ${requiredPerms.join(' or ')}';
    }

    return BreadcrumbPermissions(
      path: path,
      canAccess: canAccess,
      requiredPermissions: requiredPerms,
      denialReason: denialReason,
    );
  }

  static List<String> _getRequiredPermissionsForPath(List<String> pathSegments) {
    if (pathSegments.isEmpty) return [];
    
    final screen = pathSegments.first.toLowerCase();
    
    switch (screen) {
      case 'dashboard':
        return ['dashboard.view'];
      case 'properties':
        if (pathSegments.length > 1) {
          final section = pathSegments[1].toLowerCase();
          return ['properties.view', 'properties.$section.view'];
        }
        return ['properties.view'];
      case 'maintenance':
        return ['maintenance.view'];
      case 'offices':
        return ['offices.view'];
      case 'systems':
        if (pathSegments.length > 1) {
          final system = pathSegments[1].toLowerCase();
          return ['systems.view', 'systems.$system.view'];
        }
        return ['systems.view'];
      case 'settings':
        return ['settings.view'];
      default:
        return [];
    }
  }
}

/// Tab-level permissions
class TabPermissions {
  final String screenPath;
  final String tabName;
  final bool canAccess;
  final List<String> requiredPermissions;

  const TabPermissions({
    required this.screenPath,
    required this.tabName,
    required this.canAccess,
    required this.requiredPermissions,
  });

  factory TabPermissions.fromContext(TabContext context, UserPermissions userPermissions) {
    final requiredPerms = _getRequiredPermissionsForTab(context.screenPath, context.tabName);
    
    final canAccess = userPermissions.isAdmin || 
                     requiredPerms.any((perm) => userPermissions.hasPermission(perm));

    return TabPermissions(
      screenPath: context.screenPath,
      tabName: context.tabName,
      canAccess: canAccess,
      requiredPermissions: requiredPerms,
    );
  }

  static List<String> _getRequiredPermissionsForTab(String screenPath, String tabName) {
    final key = '$screenPath.$tabName';
    
    switch (key) {
      case 'properties.residential':
      case 'properties.office':
      case 'properties.construction':
        return ['properties.view'];
      case 'offices.locations':
      case 'offices.construction_sites':
        return ['offices.view'];
      case 'systems.water':
      case 'systems.electricity':
      case 'systems.security':
      case 'systems.internet':
      case 'systems.ott':
        return ['systems.view', 'systems.${tabName.toLowerCase()}.view'];
      default:
        return [];
    }
  }
}

/// Context for tab permissions
class TabContext {
  final String screenPath;
  final String tabName;
  final String? propertyId;
  final String? officeId;

  const TabContext({
    required this.screenPath,
    required this.tabName,
    this.propertyId,
    this.officeId,
  });
}
