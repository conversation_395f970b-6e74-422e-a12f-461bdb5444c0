import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../../data/models/api_response.dart';

class ApiClient {
  static const String _baseUrl = 'http://localhost:3000/api';
  static const Duration _timeout = Duration(seconds: 30);
  
  final http.Client _client = http.Client();
  String? _authToken;

  // Set authentication token
  void setAuthToken(String token) {
    _authToken = token;
  }

  // Clear authentication token
  void clearAuthToken() {
    _authToken = null;
  }

  // Get common headers
  Map<String, String> _getHeaders({Map<String, String>? additionalHeaders}) {
    final headers = <String, String>{
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (_authToken != null) {
      headers['Authorization'] = 'Bearer $_authToken';
    }

    if (additionalHeaders != null) {
      headers.addAll(additionalHeaders);
    }

    return headers;
  }

  // Handle HTTP response
  ApiResponse<dynamic> _handleResponse(http.Response response) {
    try {
      final Map<String, dynamic> data = json.decode(response.body);
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return ApiResponse.success(data: data);
      } else {
        final message = data['message'] ?? 'Unknown error occurred';
        return ApiResponse.error(error: message);
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to parse response: $e');
    }
  }

  // Handle HTTP exceptions
  ApiResponse<dynamic> _handleException(dynamic e) {
    if (e is SocketException) {
      return ApiResponse.error(error: 'No internet connection');
    } else if (e is HttpException) {
      return ApiResponse.error(error: 'HTTP error: ${e.message}');
    } else if (e is FormatException) {
      return ApiResponse.error(error: 'Invalid response format');
    } else {
      return ApiResponse.error(error: 'Network error: $e');
    }
  }

  // GET request
  Future<ApiResponse<dynamic>> get(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  }) async {
    try {
      final uri = Uri.parse('$_baseUrl$endpoint');
      final finalUri = queryParameters != null 
          ? uri.replace(queryParameters: queryParameters.map((k, v) => MapEntry(k, v.toString())))
          : uri;

      final response = await _client
          .get(finalUri, headers: _getHeaders(additionalHeaders: headers))
          .timeout(_timeout);

      return _handleResponse(response);
    } catch (e) {
      return _handleException(e);
    }
  }

  // POST request
  Future<ApiResponse<dynamic>> post(
    String endpoint, {
    dynamic data,
    Map<String, String>? headers,
  }) async {
    try {
      final uri = Uri.parse('$_baseUrl$endpoint');
      final body = data != null ? json.encode(data) : null;

      final response = await _client
          .post(uri, headers: _getHeaders(additionalHeaders: headers), body: body)
          .timeout(_timeout);

      return _handleResponse(response);
    } catch (e) {
      return _handleException(e);
    }
  }

  // PUT request
  Future<ApiResponse<dynamic>> put(
    String endpoint, {
    dynamic data,
    Map<String, String>? headers,
  }) async {
    try {
      final uri = Uri.parse('$_baseUrl$endpoint');
      final body = data != null ? json.encode(data) : null;

      final response = await _client
          .put(uri, headers: _getHeaders(additionalHeaders: headers), body: body)
          .timeout(_timeout);

      return _handleResponse(response);
    } catch (e) {
      return _handleException(e);
    }
  }

  // DELETE request
  Future<ApiResponse<dynamic>> delete(
    String endpoint, {
    Map<String, String>? headers,
  }) async {
    try {
      final uri = Uri.parse('$_baseUrl$endpoint');

      final response = await _client
          .delete(uri, headers: _getHeaders(additionalHeaders: headers))
          .timeout(_timeout);

      return _handleResponse(response);
    } catch (e) {
      return _handleException(e);
    }
  }

  // PATCH request
  Future<ApiResponse<dynamic>> patch(
    String endpoint, {
    dynamic data,
    Map<String, String>? headers,
  }) async {
    try {
      final uri = Uri.parse('$_baseUrl$endpoint');
      final body = data != null ? json.encode(data) : null;

      final response = await _client
          .patch(uri, headers: _getHeaders(additionalHeaders: headers), body: body)
          .timeout(_timeout);

      return _handleResponse(response);
    } catch (e) {
      return _handleException(e);
    }
  }

  // Upload file
  Future<ApiResponse<dynamic>> uploadFile(
    String endpoint,
    File file, {
    Map<String, String>? fields,
    String fieldName = 'file',
  }) async {
    try {
      final uri = Uri.parse('$_baseUrl$endpoint');
      final request = http.MultipartRequest('POST', uri);

      // Add headers
      request.headers.addAll(_getHeaders());

      // Add file
      request.files.add(await http.MultipartFile.fromPath(fieldName, file.path));

      // Add fields
      if (fields != null) {
        request.fields.addAll(fields);
      }

      final streamedResponse = await request.send().timeout(_timeout);
      final response = await http.Response.fromStream(streamedResponse);

      return _handleResponse(response);
    } catch (e) {
      return _handleException(e);
    }
  }

  // Dispose client
  void dispose() {
    _client.close();
  }
}
