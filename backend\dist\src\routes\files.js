"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const sharp_1 = __importDefault(require("sharp"));
const uuid_1 = require("uuid");
const errorHandler_1 = require("@/middleware/errorHandler");
const auth_1 = require("@/middleware/auth");
const rateLimiter_1 = require("@/middleware/rateLimiter");
const prisma_1 = require("@/lib/prisma");
const router = (0, express_1.Router)();
// Apply authentication and rate limiting
router.use(auth_1.authenticate);
router.use(rateLimiter_1.uploadRateLimit);
// Ensure upload directory exists
const uploadDir = process.env.UPLOAD_PATH || './uploads';
if (!fs_1.default.existsSync(uploadDir)) {
    fs_1.default.mkdirSync(uploadDir, { recursive: true });
}
// Configure multer for file uploads
const storage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        const subDir = path_1.default.join(uploadDir, file.fieldname);
        if (!fs_1.default.existsSync(subDir)) {
            fs_1.default.mkdirSync(subDir, { recursive: true });
        }
        cb(null, subDir);
    },
    filename: (req, file, cb) => {
        const uniqueName = `${(0, uuid_1.v4)()}-${Date.now()}${path_1.default.extname(file.originalname)}`;
        cb(null, uniqueName);
    },
});
const fileFilter = (req, file, cb) => {
    const allowedTypes = (process.env.ALLOWED_FILE_TYPES || 'image/jpeg,image/png,image/webp,application/pdf').split(',');
    if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
    }
    else {
        cb(new errorHandler_1.AppError(`File type ${file.mimetype} not allowed`, 400, 'INVALID_FILE_TYPE'));
    }
};
const upload = (0, multer_1.default)({
    storage,
    fileFilter,
    limits: {
        fileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760'), // 10MB default
        files: 5, // Max 5 files per request
    },
});
/**
 * @swagger
 * /files/upload/avatar:
 *   post:
 *     tags: [Files]
 *     summary: Upload user avatar
 *     description: Upload and process user avatar image
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               avatar:
 *                 type: string
 *                 format: binary
 *                 description: Avatar image file (JPEG, PNG, WebP)
 *     responses:
 *       200:
 *         description: Avatar uploaded successfully
 *       400:
 *         description: Invalid file or file too large
 */
router.post('/upload/avatar', (0, auth_1.requireAction)('update'), upload.single('avatar'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const user = req.user;
    const file = req.file;
    if (!file) {
        throw new errorHandler_1.AppError('No file uploaded', 400, 'NO_FILE_UPLOADED');
    }
    // Process image if it's an image file
    let processedFilePath = file.path;
    if (file.mimetype.startsWith('image/')) {
        const processedFileName = `processed-${file.filename}`;
        processedFilePath = path_1.default.join(path_1.default.dirname(file.path), processedFileName);
        await (0, sharp_1.default)(file.path)
            .resize(200, 200, {
            fit: 'cover',
            position: 'center',
        })
            .jpeg({ quality: 90 })
            .toFile(processedFilePath);
        // Remove original file
        fs_1.default.unlinkSync(file.path);
    }
    // Generate URL
    const fileUrl = `/files/avatar/${path_1.default.basename(processedFilePath)}`;
    // Update user avatar
    await prisma_1.prisma.user.update({
        where: { id: user.id },
        data: { avatar: fileUrl },
    });
    // Log activity
    await prisma_1.prisma.activity.create({
        data: {
            userId: user.id,
            action: 'UPDATE_AVATAR',
            description: 'Updated profile avatar',
            metadata: {
                fileName: file.originalname,
                fileSize: file.size,
                fileType: file.mimetype,
            },
            ipAddress: req.ip,
            userAgent: req.headers['user-agent'],
        },
    });
    const response = {
        filename: path_1.default.basename(processedFilePath),
        originalName: file.originalname,
        mimetype: file.mimetype,
        size: file.size,
        url: fileUrl,
        uploadedAt: new Date().toISOString(),
    };
    res.json({
        success: true,
        data: response,
        message: 'Avatar uploaded successfully',
        timestamp: new Date().toISOString(),
    });
}));
/**
 * @swagger
 * /files/upload/property-images:
 *   post:
 *     tags: [Files]
 *     summary: Upload property images
 *     description: Upload multiple images for a property
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               images:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: Property image files (JPEG, PNG, WebP)
 *               propertyId:
 *                 type: string
 *                 format: uuid
 *                 description: Property ID
 *     responses:
 *       200:
 *         description: Images uploaded successfully
 */
router.post('/upload/property-images', (0, auth_1.requireAction)('update'), upload.array('images', 5), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const user = req.user;
    const files = req.files;
    const { propertyId } = req.body;
    if (!files || files.length === 0) {
        throw new errorHandler_1.AppError('No files uploaded', 400, 'NO_FILES_UPLOADED');
    }
    if (!propertyId) {
        throw new errorHandler_1.AppError('Property ID is required', 400, 'PROPERTY_ID_REQUIRED');
    }
    // Check if property exists and user has access
    const property = await prisma_1.prisma.property.findFirst({
        where: {
            id: propertyId,
            ...(user.role !== 'SUPER_ADMIN' && {
                assignedUsers: {
                    some: { userId: user.id },
                },
            }),
        },
    });
    if (!property) {
        throw new errorHandler_1.AppError('Property not found or access denied', 404, 'PROPERTY_NOT_FOUND');
    }
    const uploadedFiles = [];
    // Process each image
    for (const file of files) {
        let processedFilePath = file.path;
        if (file.mimetype.startsWith('image/')) {
            const processedFileName = `processed-${file.filename}`;
            processedFilePath = path_1.default.join(path_1.default.dirname(file.path), processedFileName);
            await (0, sharp_1.default)(file.path)
                .resize(1200, 800, {
                fit: 'inside',
                withoutEnlargement: true,
            })
                .jpeg({ quality: 85 })
                .toFile(processedFilePath);
            // Remove original file
            fs_1.default.unlinkSync(file.path);
        }
        const fileUrl = `/files/property-images/${path_1.default.basename(processedFilePath)}`;
        uploadedFiles.push({
            filename: path_1.default.basename(processedFilePath),
            originalName: file.originalname,
            mimetype: file.mimetype,
            size: file.size,
            url: fileUrl,
            uploadedAt: new Date().toISOString(),
        });
    }
    // Update property images
    const imageUrls = uploadedFiles.map(file => file.url);
    await prisma_1.prisma.property.update({
        where: { id: propertyId },
        data: {
            images: {
                push: imageUrls,
            },
        },
    });
    // Log activity
    await prisma_1.prisma.activity.create({
        data: {
            userId: user.id,
            propertyId,
            action: 'UPLOAD_PROPERTY_IMAGES',
            description: `Uploaded ${uploadedFiles.length} images for property`,
            metadata: {
                imageCount: uploadedFiles.length,
                totalSize: uploadedFiles.reduce((sum, file) => sum + file.size, 0),
            },
            ipAddress: req.ip,
            userAgent: req.headers['user-agent'],
        },
    });
    res.json({
        success: true,
        data: uploadedFiles,
        message: `${uploadedFiles.length} images uploaded successfully`,
        timestamp: new Date().toISOString(),
    });
}));
/**
 * @swagger
 * /files/upload/documents:
 *   post:
 *     tags: [Files]
 *     summary: Upload documents
 *     description: Upload documents (PDF, images)
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               documents:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: Document files
 *               category:
 *                 type: string
 *                 description: Document category
 *               propertyId:
 *                 type: string
 *                 format: uuid
 *                 description: Related property ID (optional)
 *     responses:
 *       200:
 *         description: Documents uploaded successfully
 */
router.post('/upload/documents', (0, auth_1.requireAction)('create'), upload.array('documents', 5), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const user = req.user;
    const files = req.files;
    const { category, propertyId } = req.body;
    if (!files || files.length === 0) {
        throw new errorHandler_1.AppError('No files uploaded', 400, 'NO_FILES_UPLOADED');
    }
    const uploadedFiles = [];
    // Process each document
    for (const file of files) {
        const fileUrl = `/files/documents/${file.filename}`;
        uploadedFiles.push({
            filename: file.filename,
            originalName: file.originalname,
            mimetype: file.mimetype,
            size: file.size,
            url: fileUrl,
            uploadedAt: new Date().toISOString(),
        });
    }
    // Log activity
    await prisma_1.prisma.activity.create({
        data: {
            userId: user.id,
            propertyId: propertyId || null,
            action: 'UPLOAD_DOCUMENTS',
            description: `Uploaded ${uploadedFiles.length} documents`,
            metadata: {
                category: category || 'general',
                documentCount: uploadedFiles.length,
                totalSize: uploadedFiles.reduce((sum, file) => sum + file.size, 0),
            },
            ipAddress: req.ip,
            userAgent: req.headers['user-agent'],
        },
    });
    res.json({
        success: true,
        data: uploadedFiles,
        message: `${uploadedFiles.length} documents uploaded successfully`,
        timestamp: new Date().toISOString(),
    });
}));
/**
 * @swagger
 * /files/{category}/{filename}:
 *   get:
 *     tags: [Files]
 *     summary: Serve uploaded file
 *     description: Serve uploaded files
 *     parameters:
 *       - in: path
 *         name: category
 *         required: true
 *         schema:
 *           type: string
 *           enum: [avatar, property-images, documents]
 *       - in: path
 *         name: filename
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: File served successfully
 *       404:
 *         description: File not found
 */
router.get('/:category/:filename', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { category, filename } = req.params;
    // Validate category
    const allowedCategories = ['avatar', 'property-images', 'documents'];
    if (!allowedCategories.includes(category)) {
        throw new errorHandler_1.AppError('Invalid file category', 400, 'INVALID_CATEGORY');
    }
    const filePath = path_1.default.join(uploadDir, category, filename);
    // Check if file exists
    if (!fs_1.default.existsSync(filePath)) {
        throw new errorHandler_1.AppError('File not found', 404, 'FILE_NOT_FOUND');
    }
    // Get file stats
    const stats = fs_1.default.statSync(filePath);
    const fileExtension = path_1.default.extname(filename).toLowerCase();
    // Set appropriate content type
    let contentType = 'application/octet-stream';
    if (['.jpg', '.jpeg'].includes(fileExtension))
        contentType = 'image/jpeg';
    else if (fileExtension === '.png')
        contentType = 'image/png';
    else if (fileExtension === '.webp')
        contentType = 'image/webp';
    else if (fileExtension === '.pdf')
        contentType = 'application/pdf';
    // Set headers
    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Length', stats.size);
    res.setHeader('Cache-Control', 'public, max-age=31536000'); // 1 year cache
    // Stream file
    const fileStream = fs_1.default.createReadStream(filePath);
    fileStream.pipe(res);
}));
exports.default = router;
