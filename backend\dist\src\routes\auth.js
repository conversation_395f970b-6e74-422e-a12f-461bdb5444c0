"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const prisma_1 = require("@/lib/prisma");
const auth_1 = require("@/lib/auth");
const validation_1 = require("@/lib/validation");
const errorHandler_1 = require("@/middleware/errorHandler");
const rateLimiter_1 = require("@/middleware/rateLimiter");
const auth_2 = require("@/middleware/auth");
const router = (0, express_1.Router)();
// Apply rate limiting to all auth routes
router.use(rateLimiter_1.authRateLimit);
/**
 * @swagger
 * /auth/login:
 *   post:
 *     tags: [Authentication]
 *     summary: User login
 *     description: Authenticate user and return JWT token with user permissions
 *     security: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LoginRequest'
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/LoginResponse'
 *       401:
 *         description: Invalid credentials
 *       429:
 *         description: Too many login attempts
 */
router.post('/login', (0, errorHandler_1.validateRequest)(validation_1.loginSchema), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { email, password, deviceId, deviceName } = req.body;
    // Find user by email
    const user = await prisma_1.prisma.user.findUnique({
        where: { email },
        include: {
            assignedProperties: {
                select: {
                    propertyId: true,
                },
            },
        },
    });
    if (!user || !user.isActive) {
        throw new errorHandler_1.AppError('Invalid email or password', 401, 'INVALID_CREDENTIALS');
    }
    // Verify password
    const isPasswordValid = await auth_1.AuthService.comparePassword(password, user.password);
    if (!isPasswordValid) {
        throw new errorHandler_1.AppError('Invalid email or password', 401, 'INVALID_CREDENTIALS');
    }
    // Generate tokens
    const assignedProperties = user.assignedProperties.map(ap => ap.propertyId);
    const tokenPayload = {
        userId: user.id,
        email: user.email,
        role: user.role,
        assignedProperties,
    };
    const accessToken = auth_1.AuthService.generateAccessToken(tokenPayload);
    const refreshToken = auth_1.AuthService.generateRefreshToken(tokenPayload);
    // Store refresh token in database
    const expiresAt = auth_1.AuthService.getTokenExpiration(process.env.JWT_REFRESH_EXPIRES_IN || '7d');
    await prisma_1.prisma.refreshToken.create({
        data: {
            token: refreshToken,
            userId: user.id,
            expiresAt,
        },
    });
    // Update last login
    await prisma_1.prisma.user.update({
        where: { id: user.id },
        data: { lastLogin: new Date() },
    });
    // Log activity
    await prisma_1.prisma.activity.create({
        data: {
            userId: user.id,
            action: 'LOGIN',
            description: `User logged in${deviceName ? ` from ${deviceName}` : ''}`,
            metadata: {
                deviceId,
                deviceName,
                userAgent: req.headers['user-agent'],
            },
            ipAddress: req.ip,
            userAgent: req.headers['user-agent'],
        },
    });
    // Prepare user response
    const userResponse = {
        id: user.id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        role: user.role,
        assignedProperties,
        isActive: user.isActive,
        avatar: user.avatar,
        timezone: user.timezone,
        language: user.language,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        lastLogin: new Date(),
        permissions: auth_1.AuthService.getUserPermissions(user.role),
    };
    const authToken = {
        accessToken,
        refreshToken,
        expiresAt: auth_1.AuthService.getTokenExpiration().toISOString(),
        tokenType: 'Bearer',
    };
    const response = {
        user: userResponse,
        token: authToken,
        message: 'Login successful',
    };
    res.json({
        success: true,
        data: response,
        timestamp: new Date().toISOString(),
    });
}));
/**
 * @swagger
 * /auth/refresh:
 *   post:
 *     tags: [Authentication]
 *     summary: Refresh access token
 *     description: Refresh JWT token using refresh token
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               refreshToken:
 *                 type: string
 *             required: [refreshToken]
 *     responses:
 *       200:
 *         description: Token refreshed successfully
 *       401:
 *         description: Invalid refresh token
 */
router.post('/refresh', (0, errorHandler_1.validateRequest)(validation_1.refreshTokenSchema), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { refreshToken } = req.body;
    // Verify refresh token
    let payload;
    try {
        payload = auth_1.AuthService.verifyRefreshToken(refreshToken);
    }
    catch (error) {
        throw new errorHandler_1.AppError('Invalid refresh token', 401, 'INVALID_REFRESH_TOKEN');
    }
    // Check if refresh token exists in database
    const storedToken = await prisma_1.prisma.refreshToken.findUnique({
        where: { token: refreshToken },
        include: {
            user: {
                include: {
                    assignedProperties: {
                        select: {
                            propertyId: true,
                        },
                    },
                },
            },
        },
    });
    if (!storedToken || storedToken.expiresAt < new Date() || !storedToken.user.isActive) {
        // Clean up expired token
        if (storedToken) {
            await prisma_1.prisma.refreshToken.delete({
                where: { id: storedToken.id },
            });
        }
        throw new errorHandler_1.AppError('Invalid or expired refresh token', 401, 'INVALID_REFRESH_TOKEN');
    }
    // Generate new tokens
    const assignedProperties = storedToken.user.assignedProperties.map(ap => ap.propertyId);
    const newTokenPayload = {
        userId: storedToken.user.id,
        email: storedToken.user.email,
        role: storedToken.user.role,
        assignedProperties,
    };
    const newAccessToken = auth_1.AuthService.generateAccessToken(newTokenPayload);
    const newRefreshToken = auth_1.AuthService.generateRefreshToken(newTokenPayload);
    // Update refresh token in database
    const newExpiresAt = auth_1.AuthService.getTokenExpiration(process.env.JWT_REFRESH_EXPIRES_IN || '7d');
    await prisma_1.prisma.refreshToken.update({
        where: { id: storedToken.id },
        data: {
            token: newRefreshToken,
            expiresAt: newExpiresAt,
        },
    });
    const authToken = {
        accessToken: newAccessToken,
        refreshToken: newRefreshToken,
        expiresAt: auth_1.AuthService.getTokenExpiration().toISOString(),
        tokenType: 'Bearer',
    };
    res.json({
        success: true,
        data: authToken,
        message: 'Token refreshed successfully',
        timestamp: new Date().toISOString(),
    });
}));
/**
 * @swagger
 * /auth/logout:
 *   post:
 *     tags: [Authentication]
 *     summary: User logout
 *     description: Invalidate user session and tokens
 *     responses:
 *       200:
 *         description: Logout successful
 */
router.post('/logout', auth_2.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    // Remove all refresh tokens for the user
    await prisma_1.prisma.refreshToken.deleteMany({
        where: { userId },
    });
    // Log activity
    await prisma_1.prisma.activity.create({
        data: {
            userId,
            action: 'LOGOUT',
            description: 'User logged out',
            ipAddress: req.ip,
            userAgent: req.headers['user-agent'],
        },
    });
    res.json({
        success: true,
        message: 'Logout successful',
        timestamp: new Date().toISOString(),
    });
}));
/**
 * @swagger
 * /auth/me:
 *   get:
 *     tags: [Authentication]
 *     summary: Get current user
 *     description: Get current authenticated user information
 *     responses:
 *       200:
 *         description: User information retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get('/me', auth_2.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const user = await prisma_1.prisma.user.findUnique({
        where: { id: userId },
        include: {
            assignedProperties: {
                select: {
                    propertyId: true,
                },
            },
        },
    });
    if (!user) {
        throw new errorHandler_1.AppError('User not found', 404, 'USER_NOT_FOUND');
    }
    const userResponse = {
        id: user.id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        role: user.role,
        assignedProperties: user.assignedProperties.map(ap => ap.propertyId),
        isActive: user.isActive,
        avatar: user.avatar,
        timezone: user.timezone,
        language: user.language,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        lastLogin: user.lastLogin,
        permissions: auth_1.AuthService.getUserPermissions(user.role),
    };
    res.json({
        success: true,
        data: userResponse,
        timestamp: new Date().toISOString(),
    });
}));
exports.default = router;
