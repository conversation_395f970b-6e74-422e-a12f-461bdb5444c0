import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/presentation/screens/dashboard/dashboard_screen.dart';
import 'package:frontend/presentation/providers/dashboard_providers.dart';

void main() {
  group('Dashboard Toggle Tests', () {
    testWidgets('Dashboard V1 should show toggle button', (WidgetTester tester) async {
      // Create a ProviderScope with the dashboard screen
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const DashboardScreen(),
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Check if V2 toggle button exists
      expect(find.text('V2'), findsOneWidget);
      expect(find.text('Try V2'), findsOneWidget);
      expect(find.text('New Dashboard V2 Available!'), findsOneWidget);
    });

    testWidgets('Dashboard version provider should toggle correctly', (WidgetTester tester) async {
      final container = ProviderContainer();
      
      // Initial state should be false (V1)
      expect(container.read(dashboardVersionProvider), false);
      
      // Toggle to V2
      container.read(dashboardVersionProvider.notifier).toggleVersion();
      expect(container.read(dashboardVersionProvider), true);
      
      // Toggle back to V1
      container.read(dashboardVersionProvider.notifier).toggleVersion();
      expect(container.read(dashboardVersionProvider), false);
      
      container.dispose();
    });

    testWidgets('Clicking V2 button should switch to Dashboard V2', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const DashboardScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find and tap the V2 button in the app bar
      final v2Button = find.text('V2');
      expect(v2Button, findsOneWidget);
      
      await tester.tap(v2Button);
      await tester.pumpAndSettle();

      // After tapping, we should see Dashboard V2 with V1 button
      expect(find.text('V1'), findsOneWidget);
      expect(find.text('Dashboard V2'), findsOneWidget);
    });

    testWidgets('Floating action button should switch to Dashboard V2', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const DashboardScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find and tap the floating action button
      final fabButton = find.text('Try V2');
      expect(fabButton, findsWidgets); // Could be in banner and FAB
      
      // Tap the FAB specifically
      final fab = find.byType(FloatingActionButton);
      expect(fab, findsOneWidget);
      
      await tester.tap(fab);
      await tester.pumpAndSettle();

      // After tapping, we should see Dashboard V2
      expect(find.text('V1'), findsOneWidget);
      expect(find.text('Dashboard V2'), findsOneWidget);
    });

    testWidgets('Banner button should switch to Dashboard V2', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const DashboardScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find the banner "Try V2" button (not the FAB)
      final bannerButtons = find.text('Try V2');
      expect(bannerButtons, findsWidgets);
      
      // Tap the first "Try V2" button (should be the banner)
      await tester.tap(bannerButtons.first);
      await tester.pumpAndSettle();

      // After tapping, we should see Dashboard V2
      expect(find.text('V1'), findsOneWidget);
      expect(find.text('Dashboard V2'), findsOneWidget);
    });
  });
}
