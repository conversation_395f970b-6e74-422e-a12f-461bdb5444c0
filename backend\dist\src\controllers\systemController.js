"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateSystemStatus = exports.getSecuritySystems = exports.getElectricitySystems = exports.getWaterSystems = exports.getSystemStatuses = void 0;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
// Get all system statuses with filtering
const getSystemStatuses = async (req, res) => {
    try {
        const { propertyId, systemType, status } = req.query;
        const userId = req.user?.id;
        const userRole = req.user?.role;
        // Build filter
        let filter = {};
        // Property-based access control
        if (userRole !== 'SUPER_ADMIN') {
            const userProperties = await prisma.userProperty.findMany({
                where: { userId },
                select: { propertyId: true }
            });
            const assignedPropertyIds = userProperties.map(up => up.propertyId);
            if (assignedPropertyIds.length > 0) {
                filter.propertyId = { in: assignedPropertyIds };
            }
            else {
                return res.json({ success: true, data: [] });
            }
        }
        if (propertyId) {
            filter.propertyId = propertyId;
        }
        if (systemType) {
            filter.systemType = systemType;
        }
        if (status) {
            filter.status = status;
        }
        const systemStatuses = await prisma.systemStatus.findMany({
            where: filter,
            include: {
                property: {
                    select: { id: true, name: true, type: true, address: true }
                }
            },
            orderBy: { lastChecked: 'desc' }
        });
        res.json({
            success: true,
            data: systemStatuses
        });
    }
    catch (error) {
        console.error('Error fetching system statuses:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch system statuses',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};
exports.getSystemStatuses = getSystemStatuses;
// Get water systems for a property
const getWaterSystems = async (req, res) => {
    try {
        const { propertyId } = req.params;
        const userId = req.user?.id;
        const userRole = req.user?.role;
        // Check property access
        if (userRole !== 'SUPER_ADMIN') {
            const userProperty = await prisma.userProperty.findFirst({
                where: { userId, propertyId }
            });
            if (!userProperty) {
                return res.status(403).json({
                    success: false,
                    message: 'Access denied to this property\'s water systems'
                });
            }
        }
        const waterSystems = await prisma.waterSystem.findMany({
            where: { propertyId, isActive: true },
            include: {
                property: {
                    select: { id: true, name: true }
                }
            },
            orderBy: { tankName: 'asc' }
        });
        // Calculate summary statistics
        const totalCapacity = waterSystems.reduce((sum, system) => sum + system.capacity, 0);
        const totalCurrentLevel = waterSystems.reduce((sum, system) => sum + system.currentLevel, 0);
        const averageLevel = waterSystems.length > 0 ? (totalCurrentLevel / totalCapacity) * 100 : 0;
        const activePumps = waterSystems.filter(system => system.pumpStatus === 'ON').length;
        const maintenanceRequired = waterSystems.filter(system => system.nextMaintenance && new Date(system.nextMaintenance) <= new Date()).length;
        res.json({
            success: true,
            data: {
                systems: waterSystems,
                summary: {
                    totalSystems: waterSystems.length,
                    totalCapacity,
                    totalCurrentLevel,
                    averageLevel: Math.round(averageLevel),
                    activePumps,
                    maintenanceRequired
                }
            }
        });
    }
    catch (error) {
        console.error('Error fetching water systems:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch water systems',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};
exports.getWaterSystems = getWaterSystems;
// Get electricity systems for a property
const getElectricitySystems = async (req, res) => {
    try {
        const { propertyId } = req.params;
        const userId = req.user?.id;
        const userRole = req.user?.role;
        // Check property access
        if (userRole !== 'SUPER_ADMIN') {
            const userProperty = await prisma.userProperty.findFirst({
                where: { userId, propertyId }
            });
            if (!userProperty) {
                return res.status(403).json({
                    success: false,
                    message: 'Access denied to this property\'s electricity systems'
                });
            }
        }
        const electricitySystems = await prisma.electricitySystem.findMany({
            where: { propertyId, isActive: true },
            include: {
                property: {
                    select: { id: true, name: true }
                }
            },
            orderBy: { systemName: 'asc' }
        });
        // Calculate summary statistics
        const totalPowerConsumption = electricitySystems.reduce((sum, system) => sum + (system.powerConsumption || 0), 0);
        const averageFuelLevel = electricitySystems.length > 0
            ? electricitySystems.reduce((sum, system) => sum + (system.fuelLevel || 0), 0) / electricitySystems.length
            : 0;
        const activeGenerators = electricitySystems.filter(system => system.generatorStatus === 'ON').length;
        const mainsAvailable = electricitySystems.filter(system => system.mainsPowerStatus === 'AVAILABLE').length;
        const maintenanceRequired = electricitySystems.filter(system => system.nextMaintenance && new Date(system.nextMaintenance) <= new Date()).length;
        res.json({
            success: true,
            data: {
                systems: electricitySystems,
                summary: {
                    totalSystems: electricitySystems.length,
                    totalPowerConsumption,
                    averageFuelLevel: Math.round(averageFuelLevel),
                    activeGenerators,
                    mainsAvailable,
                    maintenanceRequired
                }
            }
        });
    }
    catch (error) {
        console.error('Error fetching electricity systems:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch electricity systems',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};
exports.getElectricitySystems = getElectricitySystems;
// Get security systems for a property
const getSecuritySystems = async (req, res) => {
    try {
        const { propertyId } = req.params;
        const userId = req.user?.id;
        const userRole = req.user?.role;
        // Check property access
        if (userRole !== 'SUPER_ADMIN') {
            const userProperty = await prisma.userProperty.findFirst({
                where: { userId, propertyId }
            });
            if (!userProperty) {
                return res.status(403).json({
                    success: false,
                    message: 'Access denied to this property\'s security systems'
                });
            }
        }
        const securitySystems = await prisma.securitySystem.findMany({
            where: { propertyId, isActive: true },
            include: {
                property: {
                    select: { id: true, name: true }
                },
                cameras: {
                    orderBy: { cameraName: 'asc' }
                }
            },
            orderBy: { systemName: 'asc' }
        });
        // Calculate summary statistics
        const totalCameras = securitySystems.reduce((sum, system) => sum + system.cameraCount, 0);
        const totalActiveCameras = securitySystems.reduce((sum, system) => sum + system.activeCameras, 0);
        const totalAccessPoints = securitySystems.reduce((sum, system) => sum + system.accessPoints, 0);
        const totalActiveAccess = securitySystems.reduce((sum, system) => sum + system.activeAccess, 0);
        const armedSystems = securitySystems.filter(system => system.alarmStatus === 'ARMED').length;
        const triggeredAlarms = securitySystems.filter(system => system.alarmStatus === 'TRIGGERED').length;
        const maintenanceRequired = securitySystems.filter(system => system.nextMaintenance && new Date(system.nextMaintenance) <= new Date()).length;
        res.json({
            success: true,
            data: {
                systems: securitySystems,
                summary: {
                    totalSystems: securitySystems.length,
                    totalCameras,
                    totalActiveCameras,
                    totalAccessPoints,
                    totalActiveAccess,
                    armedSystems,
                    triggeredAlarms,
                    maintenanceRequired,
                    cameraOperationalRate: totalCameras > 0 ? Math.round((totalActiveCameras / totalCameras) * 100) : 0,
                    accessOperationalRate: totalAccessPoints > 0 ? Math.round((totalActiveAccess / totalAccessPoints) * 100) : 0
                }
            }
        });
    }
    catch (error) {
        console.error('Error fetching security systems:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch security systems',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};
exports.getSecuritySystems = getSecuritySystems;
// Update system status
const updateSystemStatus = async (req, res) => {
    try {
        const { id } = req.params;
        const { status, description, healthScore, metadata } = req.body;
        const userId = req.user?.id;
        const userRole = req.user?.role;
        // Get the system status to check property access
        const systemStatus = await prisma.systemStatus.findUnique({
            where: { id },
            include: { property: true }
        });
        if (!systemStatus) {
            return res.status(404).json({
                success: false,
                message: 'System status not found'
            });
        }
        // Check property access
        if (userRole !== 'SUPER_ADMIN') {
            const userProperty = await prisma.userProperty.findFirst({
                where: { userId, propertyId: systemStatus.propertyId }
            });
            if (!userProperty) {
                return res.status(403).json({
                    success: false,
                    message: 'Access denied to update this system status'
                });
            }
        }
        // Update the system status
        const updatedSystemStatus = await prisma.systemStatus.update({
            where: { id },
            data: {
                status,
                description,
                healthScore,
                metadata,
                lastChecked: new Date()
            },
            include: {
                property: {
                    select: { id: true, name: true, type: true }
                }
            }
        });
        // Log the activity
        await prisma.activity.create({
            data: {
                userId,
                propertyId: systemStatus.propertyId,
                action: 'SYSTEM_STATUS_UPDATED',
                description: `Updated ${systemStatus.systemType} system status to ${status}`,
                metadata: {
                    systemId: id,
                    systemType: systemStatus.systemType,
                    oldStatus: systemStatus.status,
                    newStatus: status
                }
            }
        });
        res.json({
            success: true,
            data: updatedSystemStatus,
            message: 'System status updated successfully'
        });
    }
    catch (error) {
        console.error('Error updating system status:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update system status',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};
exports.updateSystemStatus = updateSystemStatus;
