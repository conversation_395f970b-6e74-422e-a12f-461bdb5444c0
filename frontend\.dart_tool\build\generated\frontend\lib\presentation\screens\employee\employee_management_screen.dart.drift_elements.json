{"valid_import": true, "imports": [{"uri": "package:flutter/material.dart", "transitive": false}, {"uri": "package:flutter_riverpod/flutter_riverpod.dart", "transitive": false}, {"uri": "package:frontend/presentation/providers/auth_providers.dart", "transitive": false}, {"uri": "package:frontend/presentation/providers/employee_providers.dart", "transitive": false}, {"uri": "package:frontend/presentation/widgets/permission_wrapper.dart", "transitive": false}, {"uri": "package:frontend/core/theme/app_theme.dart", "transitive": false}, {"uri": "package:frontend/data/models/employee.dart", "transitive": false}], "elements": []}