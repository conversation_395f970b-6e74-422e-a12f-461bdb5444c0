import 'package:flutter/material.dart';
import '../../widgets/permission_widgets.dart';
import '../../../core/constants/app_constants.dart';
import '../../../data/models/user_model.dart';

class AdminPanelScreen extends StatefulWidget {
  const AdminPanelScreen({Key? key}) : super(key: key);

  @override
  State<AdminPanelScreen> createState() => _AdminPanelScreenState();
}

class _AdminPanelScreenState extends State<AdminPanelScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PermissionWrapper(
      permission: 'rbac.view',
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Admin Panel'),
          backgroundColor: Color(AppConstants.primaryColor),
          foregroundColor: Colors.white,
          bottom: TabBar(
            controller: _tabController,
            indicatorColor: Colors.white,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            tabs: const [
              Tab(text: 'User Management', icon: Icon(Icons.people)),
              Tab(text: 'Role Management', icon: Icon(Icons.admin_panel_settings)),
              Tab(text: 'Permissions', icon: Icon(Icons.security)),
            ],
          ),
        ),
        body: TabBarView(
          controller: _tabController,
          children: const [
            UserManagementTab(),
            RoleManagementTab(),
            PermissionsTab(),
          ],
        ),
      ),
    );
  }
}

class UserManagementTab extends StatefulWidget {
  const UserManagementTab({Key? key}) : super(key: key);

  @override
  State<UserManagementTab> createState() => _UserManagementTabState();
}

class _UserManagementTabState extends State<UserManagementTab> {
  List<UserModel> users = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUsers();
  }

  Future<void> _loadUsers() async {
    setState(() => isLoading = true);
    try {
      // TODO: Implement API call to fetch users
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call
      setState(() {
        users = [
          UserModel(
            id: '1',
            name: 'John Admin',
            email: '<EMAIL>',
            role: UserRole.superAdmin,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          UserModel(
            id: '2',
            name: 'Jane Manager',
            email: '<EMAIL>',
            role: UserRole.propertyManager,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];
        isLoading = false;
      });
    } catch (e) {
      setState(() => isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading users: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Users (${users.length})',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              PermissionWrapper(
                permission: 'employees.create',
                child: ElevatedButton.icon(
                  onPressed: () => _showCreateUserDialog(),
                  icon: const Icon(Icons.add),
                  label: const Text('Add User'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(AppConstants.primaryColor),
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: users.length,
              itemBuilder: (context, index) {
                final user = users[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: Color(AppConstants.primaryColor),
                      child: Text(
                        user.name.substring(0, 1).toUpperCase(),
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                    title: Text(user.name),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(user.email),
                        Text(
                          _getRoleDisplayName(user.role),
                          style: TextStyle(
                            color: _getRoleColor(user.role),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Switch(
                          value: user.isActive,
                          onChanged: (value) => _toggleUserStatus(user, value),
                          activeColor: Color(AppConstants.primaryColor),
                        ),
                        PopupMenuButton<String>(
                          onSelected: (value) => _handleUserAction(user, value),
                          itemBuilder: (context) => [
                            const PopupMenuItem(
                              value: 'edit',
                              child: Row(
                                children: [
                                  Icon(Icons.edit),
                                  SizedBox(width: 8),
                                  Text('Edit'),
                                ],
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'permissions',
                              child: Row(
                                children: [
                                  Icon(Icons.security),
                                  SizedBox(width: 8),
                                  Text('Permissions'),
                                ],
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'delete',
                              child: Row(
                                children: [
                                  Icon(Icons.delete, color: Colors.red),
                                  SizedBox(width: 8),
                                  Text('Delete', style: TextStyle(color: Colors.red)),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  String _getRoleDisplayName(UserRole role) {
    switch (role) {
      case UserRole.superAdmin:
        return 'Super Administrator';
      case UserRole.propertyManager:
        return 'Property Manager';
      case UserRole.officeManager:
        return 'Office Manager';
      case UserRole.securityPersonnel:
        return 'Security Personnel';
      case UserRole.maintenanceStaff:
        return 'Maintenance Staff';
      case UserRole.constructionSupervisor:
        return 'Construction Supervisor';
    }
  }

  Color _getRoleColor(UserRole role) {
    switch (role) {
      case UserRole.superAdmin:
        return Colors.red;
      case UserRole.propertyManager:
        return Colors.blue;
      case UserRole.officeManager:
        return Colors.green;
      case UserRole.securityPersonnel:
        return Colors.orange;
      case UserRole.maintenanceStaff:
        return Colors.purple;
      case UserRole.constructionSupervisor:
        return Colors.teal;
    }
  }

  void _showCreateUserDialog() {
    showDialog(
      context: context,
      builder: (context) => const CreateUserDialog(),
    );
  }

  void _toggleUserStatus(UserModel user, bool isActive) {
    // TODO: Implement API call to toggle user status
    setState(() {
      final index = users.indexWhere((u) => u.id == user.id);
      if (index != -1) {
        users[index] = user.copyWith(isActive: isActive);
      }
    });
  }

  void _handleUserAction(UserModel user, String action) {
    switch (action) {
      case 'edit':
        _showEditUserDialog(user);
        break;
      case 'permissions':
        _showUserPermissionsDialog(user);
        break;
      case 'delete':
        _showDeleteUserDialog(user);
        break;
    }
  }

  void _showEditUserDialog(UserModel user) {
    showDialog(
      context: context,
      builder: (context) => EditUserDialog(user: user),
    );
  }

  void _showUserPermissionsDialog(UserModel user) {
    showDialog(
      context: context,
      builder: (context) => UserPermissionsDialog(user: user),
    );
  }

  void _showDeleteUserDialog(UserModel user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete User'),
        content: Text('Are you sure you want to delete ${user.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // TODO: Implement delete user API call
              Navigator.pop(context);
              setState(() {
                users.removeWhere((u) => u.id == user.id);
              });
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

class RoleManagementTab extends StatelessWidget {
  const RoleManagementTab({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text('Role Management - Coming Soon'),
    );
  }
}

class PermissionsTab extends StatelessWidget {
  const PermissionsTab({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text('Permissions Management - Coming Soon'),
    );
  }
}

class CreateUserDialog extends StatelessWidget {
  const CreateUserDialog({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Create New User'),
      content: const Text('User creation form - Coming Soon'),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Create'),
        ),
      ],
    );
  }
}

class EditUserDialog extends StatelessWidget {
  final UserModel user;

  const EditUserDialog({Key? key, required this.user}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Edit User'),
      content: Text('Edit form for ${user.name} - Coming Soon'),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Save'),
        ),
      ],
    );
  }
}

class UserPermissionsDialog extends StatelessWidget {
  final UserModel user;

  const UserPermissionsDialog({Key? key, required this.user}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('${user.name} Permissions'),
      content: Text('Permissions management for ${user.name} - Coming Soon'),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Close'),
        ),
      ],
    );
  }
}
