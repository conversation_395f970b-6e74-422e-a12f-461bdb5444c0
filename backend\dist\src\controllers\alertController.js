"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAlertStatistics = exports.deleteAlert = exports.updateAlertStatus = exports.createAlert = exports.getAlert = exports.getAlerts = void 0;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
// Get all alerts with filtering and pagination
const getAlerts = async (req, res) => {
    try {
        const { propertyId, severity, status, category, page = 1, limit = 20, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
        const userId = req.user?.id;
        const userRole = req.user?.role;
        // Build filter
        let filter = {};
        // Property-based access control
        if (userRole !== 'SUPER_ADMIN') {
            const userProperties = await prisma.userProperty.findMany({
                where: { userId },
                select: { propertyId: true }
            });
            const assignedPropertyIds = userProperties.map(up => up.propertyId);
            if (assignedPropertyIds.length > 0) {
                filter.propertyId = { in: assignedPropertyIds };
            }
            else {
                return res.json({
                    success: true,
                    data: {
                        data: [],
                        total: 0,
                        page: parseInt(page),
                        limit: parseInt(limit),
                        totalPages: 0,
                        hasNext: false,
                        hasPrevious: false
                    }
                });
            }
        }
        if (propertyId) {
            filter.propertyId = propertyId;
        }
        if (severity) {
            filter.severity = severity;
        }
        if (status) {
            filter.status = status;
        }
        if (category) {
            filter.category = category;
        }
        // Pagination
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        const skip = (pageNum - 1) * limitNum;
        // Sort
        const sortObj = {};
        sortObj[sortBy] = sortOrder === 'desc' ? 'desc' : 'asc';
        // Execute query
        const [alerts, total] = await Promise.all([
            prisma.alert.findMany({
                where: filter,
                include: {
                    property: {
                        select: { id: true, name: true, type: true, address: true }
                    }
                },
                orderBy: sortObj,
                skip,
                take: limitNum
            }),
            prisma.alert.count({ where: filter })
        ]);
        // Calculate pagination info
        const totalPages = Math.ceil(total / limitNum);
        const hasNext = pageNum < totalPages;
        const hasPrevious = pageNum > 1;
        res.json({
            success: true,
            data: {
                data: alerts,
                total,
                page: pageNum,
                limit: limitNum,
                totalPages,
                hasNext,
                hasPrevious
            }
        });
    }
    catch (error) {
        console.error('Error fetching alerts:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch alerts',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};
exports.getAlerts = getAlerts;
// Get single alert
const getAlert = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user?.id;
        const userRole = req.user?.role;
        const alert = await prisma.alert.findUnique({
            where: { id },
            include: {
                property: {
                    select: { id: true, name: true, type: true, address: true }
                }
            }
        });
        if (!alert) {
            return res.status(404).json({
                success: false,
                message: 'Alert not found'
            });
        }
        // Check property access
        if (userRole !== 'SUPER_ADMIN' && alert.propertyId) {
            const userProperty = await prisma.userProperty.findFirst({
                where: { userId, propertyId: alert.propertyId }
            });
            if (!userProperty) {
                return res.status(403).json({
                    success: false,
                    message: 'Access denied to this alert'
                });
            }
        }
        res.json({
            success: true,
            data: alert
        });
    }
    catch (error) {
        console.error('Error fetching alert:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch alert',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};
exports.getAlert = getAlert;
// Create new alert
const createAlert = async (req, res) => {
    try {
        const { propertyId, title, message, severity, category, metadata } = req.body;
        const userId = req.user?.id;
        const userRole = req.user?.role;
        // Check property access if propertyId is provided
        if (propertyId && userRole !== 'SUPER_ADMIN') {
            const userProperty = await prisma.userProperty.findFirst({
                where: { userId, propertyId }
            });
            if (!userProperty) {
                return res.status(403).json({
                    success: false,
                    message: 'Access denied to create alerts for this property'
                });
            }
        }
        const alert = await prisma.alert.create({
            data: {
                propertyId,
                title,
                message,
                severity,
                category,
                metadata
            },
            include: {
                property: {
                    select: { id: true, name: true, type: true }
                }
            }
        });
        // Log the activity
        await prisma.activity.create({
            data: {
                userId,
                propertyId,
                action: 'ALERT_CREATED',
                description: `Created ${severity} alert: ${title}`,
                metadata: {
                    alertId: alert.id,
                    severity,
                    category
                }
            }
        });
        res.status(201).json({
            success: true,
            data: alert,
            message: 'Alert created successfully'
        });
    }
    catch (error) {
        console.error('Error creating alert:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create alert',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};
exports.createAlert = createAlert;
// Update alert status
const updateAlertStatus = async (req, res) => {
    try {
        const { id } = req.params;
        const { status } = req.body;
        const userId = req.user?.id;
        const userRole = req.user?.role;
        const alert = await prisma.alert.findUnique({
            where: { id }
        });
        if (!alert) {
            return res.status(404).json({
                success: false,
                message: 'Alert not found'
            });
        }
        // Check property access
        if (userRole !== 'SUPER_ADMIN' && alert.propertyId) {
            const userProperty = await prisma.userProperty.findFirst({
                where: { userId, propertyId: alert.propertyId }
            });
            if (!userProperty) {
                return res.status(403).json({
                    success: false,
                    message: 'Access denied to update this alert'
                });
            }
        }
        const updatedAlert = await prisma.alert.update({
            where: { id },
            data: {
                status,
                resolvedAt: status === 'RESOLVED' ? new Date() : null
            },
            include: {
                property: {
                    select: { id: true, name: true, type: true }
                }
            }
        });
        // Log the activity
        await prisma.activity.create({
            data: {
                userId,
                propertyId: alert.propertyId,
                action: 'ALERT_STATUS_UPDATED',
                description: `Updated alert status from ${alert.status} to ${status}`,
                metadata: {
                    alertId: id,
                    oldStatus: alert.status,
                    newStatus: status
                }
            }
        });
        res.json({
            success: true,
            data: updatedAlert,
            message: 'Alert status updated successfully'
        });
    }
    catch (error) {
        console.error('Error updating alert status:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update alert status',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};
exports.updateAlertStatus = updateAlertStatus;
// Delete alert
const deleteAlert = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user?.id;
        const userRole = req.user?.role;
        const alert = await prisma.alert.findUnique({
            where: { id }
        });
        if (!alert) {
            return res.status(404).json({
                success: false,
                message: 'Alert not found'
            });
        }
        // Check permissions - only admin can delete alerts
        if (userRole !== 'SUPER_ADMIN') {
            return res.status(403).json({
                success: false,
                message: 'Access denied to delete alerts'
            });
        }
        await prisma.alert.delete({
            where: { id }
        });
        // Log the activity
        await prisma.activity.create({
            data: {
                userId,
                propertyId: alert.propertyId,
                action: 'ALERT_DELETED',
                description: `Deleted alert: ${alert.title}`,
                metadata: {
                    alertId: id,
                    severity: alert.severity
                }
            }
        });
        res.json({
            success: true,
            message: 'Alert deleted successfully'
        });
    }
    catch (error) {
        console.error('Error deleting alert:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete alert',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};
exports.deleteAlert = deleteAlert;
// Get alert statistics
const getAlertStatistics = async (req, res) => {
    try {
        const { propertyId, dateFrom, dateTo } = req.query;
        const userId = req.user?.id;
        const userRole = req.user?.role;
        // Build filter
        let filter = {};
        // Property-based access control
        if (userRole !== 'SUPER_ADMIN') {
            const userProperties = await prisma.userProperty.findMany({
                where: { userId },
                select: { propertyId: true }
            });
            const assignedPropertyIds = userProperties.map(up => up.propertyId);
            if (assignedPropertyIds.length > 0) {
                filter.propertyId = { in: assignedPropertyIds };
            }
            else {
                return res.json({
                    success: true,
                    data: {
                        totalAlerts: 0,
                        openAlerts: 0,
                        acknowledgedAlerts: 0,
                        resolvedAlerts: 0,
                        criticalAlerts: 0,
                        highAlerts: 0,
                        mediumAlerts: 0,
                        lowAlerts: 0,
                        alertsByCategory: {},
                        alertsByProperty: {},
                        trends: []
                    }
                });
            }
        }
        if (propertyId) {
            filter.propertyId = propertyId;
        }
        // Date range filter
        if (dateFrom || dateTo) {
            filter.createdAt = {};
            if (dateFrom)
                filter.createdAt.gte = new Date(dateFrom);
            if (dateTo)
                filter.createdAt.lte = new Date(dateTo);
        }
        // Get statistics
        const [totalAlerts, openAlerts, acknowledgedAlerts, resolvedAlerts, criticalAlerts, highAlerts, mediumAlerts, lowAlerts, alertsByCategory, alertsByProperty] = await Promise.all([
            prisma.alert.count({ where: filter }),
            prisma.alert.count({ where: { ...filter, status: 'OPEN' } }),
            prisma.alert.count({ where: { ...filter, status: 'ACKNOWLEDGED' } }),
            prisma.alert.count({ where: { ...filter, status: 'RESOLVED' } }),
            prisma.alert.count({ where: { ...filter, severity: 'CRITICAL' } }),
            prisma.alert.count({ where: { ...filter, severity: 'HIGH' } }),
            prisma.alert.count({ where: { ...filter, severity: 'MEDIUM' } }),
            prisma.alert.count({ where: { ...filter, severity: 'LOW' } }),
            prisma.alert.groupBy({
                by: ['category'],
                where: filter,
                _count: { category: true }
            }),
            prisma.alert.groupBy({
                by: ['propertyId'],
                where: filter,
                _count: { propertyId: true }
            })
        ]);
        // Get trends (last 30 days)
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const trends = await prisma.alert.groupBy({
            by: ['createdAt'],
            where: {
                ...filter,
                createdAt: { gte: thirtyDaysAgo }
            },
            _count: { id: true }
        });
        const statistics = {
            totalAlerts,
            openAlerts,
            acknowledgedAlerts,
            resolvedAlerts,
            criticalAlerts,
            highAlerts,
            mediumAlerts,
            lowAlerts,
            alertsByCategory: alertsByCategory.reduce((acc, item) => {
                acc[item.category || 'Unknown'] = item._count.category;
                return acc;
            }, {}),
            alertsByProperty: alertsByProperty.reduce((acc, item) => {
                acc[item.propertyId || 'Unknown'] = item._count.propertyId;
                return acc;
            }, {}),
            trends: trends.map(trend => ({
                date: trend.createdAt.toISOString().split('T')[0],
                count: trend._count.id
            }))
        };
        res.json({
            success: true,
            data: statistics
        });
    }
    catch (error) {
        console.error('Error fetching alert statistics:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch alert statistics',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};
exports.getAlertStatistics = getAlertStatistics;
