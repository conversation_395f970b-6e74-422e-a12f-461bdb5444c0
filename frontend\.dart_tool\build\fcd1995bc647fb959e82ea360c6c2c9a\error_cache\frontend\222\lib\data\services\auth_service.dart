["drift_dev on lib/data/services/auth_service.dart", ["", "Invalid argument(s): Missing library: package:http/http.dart\nLibraries: [dart:async, dart:collection, dart:convert, dart:core, dart:developer, dart:ffi, dart:_http, dart:io, dart:isolate, dart:math, dart:nativewrappers, dart:typed_data, dart:_internal, dart:ui, package:frontend/core/utils/api_response.dart, package:json_annotation/src/allowed_keys_helpers.dart, package:json_annotation/src/checked_helpers.dart, package:meta/meta_meta.dart, package:json_annotation/src/json_converter.dart, package:json_annotation/src/enum_helpers.dart, package:json_annotation/src/json_key.dart, package:json_annotation/src/json_serializable.dart, package:json_annotation/src/json_value.dart, package:json_annotation/src/json_enum.dart, package:json_annotation/src/json_literal.dart, package:json_annotation/json_annotation.dart, package:meta/meta.dart, package:flutter/src/foundation/annotations.dart, package:flutter/src/foundation/constants.dart, package:flutter/src/foundation/basic_types.dart, package:flutter/src/foundation/print.dart, package:flutter/src/foundation/object.dart, package:flutter/src/foundation/stack_frame.dart, package:flutter/src/foundation/_platform_io.dart, package:flutter/src/foundation/assertions.dart, package:flutter/src/foundation/debug.dart, package:flutter/src/foundation/diagnostics.dart, package:flutter/src/foundation/memory_allocations.dart, package:flutter/src/foundation/platform.dart, package:flutter/src/foundation/service_extensions.dart, package:flutter/src/foundation/_timeline_io.dart, package:flutter/src/foundation/timeline.dart, package:flutter/src/foundation/binding.dart, package:flutter/src/foundation/_bitfield_io.dart, package:flutter/src/foundation/bitfield.dart, package:flutter/src/foundation/_capabilities_io.dart, package:flutter/src/foundation/capabilities.dart, package:flutter/src/foundation/change_notifier.dart, package:flutter/src/foundation/collections.dart, package:flutter/src/foundation/consolidate_response.dart, package:flutter/src/foundation/_isolates_io.dart, package:flutter/src/foundation/isolates.dart, package:flutter/src/foundation/key.dart, package:flutter/src/foundation/licenses.dart, package:flutter/src/foundation/node.dart, package:flutter/src/foundation/observer_list.dart, package:flutter/src/foundation/persistent_hash_map.dart, package:flutter/src/foundation/serialization.dart, package:flutter/src/foundation/synchronous_future.dart, package:flutter/src/foundation/unicode.dart, package:flutter/foundation.dart, package:shared_preferences_platform_interface/types.dart, package:shared_preferences_platform_interface/shared_preferences_async_platform_interface.dart, package:flutter/src/services/binary_messenger.dart, package:collection/src/utils.dart, package:collection/src/algorithms.dart, package:collection/src/empty_unmodifiable_set.dart, package:collection/src/unmodifiable_wrappers.dart, package:collection/src/wrappers.dart, package:collection/src/boollist.dart, package:collection/src/canonicalized_map.dart, package:collection/src/combined_wrappers/combined_iterator.dart, package:collection/src/combined_wrappers/combined_iterable.dart, package:collection/src/combined_wrappers/combined_list.dart, package:collection/src/combined_wrappers/combined_map.dart, package:collection/src/comparators.dart, package:collection/src/equality.dart, package:collection/src/equality_map.dart, package:collection/src/equality_set.dart, package:collection/src/functions.dart, package:collection/src/iterable_extensions.dart, package:collection/src/iterable_zip.dart, package:collection/src/list_extensions.dart, package:collection/src/priority_queue.dart, package:collection/src/queue_list.dart, package:collection/src/union_set.dart, package:collection/src/union_set_controller.dart, package:collection/collection.dart, package:flutter/src/scheduler/debug.dart, package:flutter/src/scheduler/priority.dart, package:flutter/src/scheduler/service_extensions.dart, package:flutter/src/scheduler/binding.dart, package:flutter/src/scheduler/ticker.dart, package:flutter/scheduler.dart, package:flutter/src/services/service_extensions.dart, package:flutter/src/services/keyboard_key.g.dart, package:flutter/src/services/keyboard_maps.g.dart, package:flutter/src/services/text_editing.dart, package:vector_math/vector_math_64.dart, package:flutter/src/services/keyboard_inserted_content.dart, package:flutter/src/services/_background_isolate_binary_messenger_io.dart, package:flutter/src/services/asset_bundle.dart, package:flutter/src/services/autofill.dart, package:flutter/src/services/binding.dart, package:flutter/src/services/clipboard.dart, package:flutter/src/services/debug.dart, package:flutter/src/services/hardware_keyboard.dart, package:flutter/src/services/message_codec.dart, package:flutter/src/services/message_codecs.dart, package:flutter/src/services/platform_channel.dart, package:flutter/src/services/raw_keyboard.dart, package:flutter/src/services/raw_keyboard_android.dart, package:flutter/src/services/raw_keyboard_fuchsia.dart, package:flutter/src/services/raw_keyboard_ios.dart, package:flutter/src/services/raw_keyboard_linux.dart, package:flutter/src/services/raw_keyboard_macos.dart, package:flutter/src/services/raw_keyboard_web.dart, package:flutter/src/services/raw_keyboard_windows.dart, package:flutter/src/services/restoration.dart, package:flutter/src/services/system_channels.dart, package:flutter/src/services/system_chrome.dart, package:flutter/src/services/text_editing_delta.dart, package:flutter/src/services/text_input.dart, package:flutter/src/services/asset_manifest.dart, package:flutter/src/services/browser_context_menu.dart, package:flutter/src/services/deferred_component.dart, package:flutter/src/services/flavor.dart, package:flutter/src/services/flutter_version.dart, package:flutter/src/services/font_loader.dart, package:flutter/src/services/haptic_feedback.dart, package:flutter/src/services/live_text.dart, package:flutter/src/gestures/debug.dart, package:flutter/src/gestures/arena.dart, package:flutter/src/gestures/constants.dart, package:flutter/src/gestures/gesture_settings.dart, package:flutter/src/gestures/events.dart, package:flutter/src/gestures/converter.dart, package:flutter/src/gestures/hit_test.dart, package:flutter/src/gestures/pointer_router.dart, package:flutter/src/gestures/pointer_signal_resolver.dart, package:flutter/src/gestures/resampler.dart, package:flutter/src/gestures/binding.dart, package:flutter/src/gestures/lsq_solver.dart, package:flutter/src/gestures/velocity_tracker.dart, package:flutter/src/gestures/drag_details.dart, package:flutter/src/gestures/drag.dart, package:flutter/src/gestures/team.dart, package:flutter/src/gestures/recognizer.dart, package:flutter/src/gestures/eager.dart, package:flutter/src/gestures/force_press.dart, package:flutter/src/gestures/long_press.dart, package:flutter/src/gestures/monodrag.dart, package:flutter/src/gestures/multidrag.dart, package:flutter/src/gestures/tap.dart, package:flutter/src/gestures/multitap.dart, package:flutter/src/gestures/scale.dart, package:flutter/src/gestures/tap_and_drag.dart, package:flutter/gestures.dart, package:flutter/src/services/mouse_cursor.dart, package:flutter/src/services/mouse_tracking.dart, package:flutter/src/services/platform_views.dart, package:flutter/src/services/predictive_back_event.dart, package:flutter/src/services/process_text.dart, package:flutter/src/services/scribe.dart, package:flutter/src/services/spell_check.dart, package:flutter/src/services/system_navigator.dart, package:flutter/src/services/system_sound.dart, package:characters/src/grapheme_clusters/constants.dart, package:characters/src/grapheme_clusters/table.dart, package:characters/src/grapheme_clusters/breaks.dart, package:characters/src/characters.dart, package:characters/src/characters_impl.dart, package:characters/src/extensions.dart, package:characters/characters.dart, package:flutter/src/services/text_layout_metrics.dart, package:flutter/src/services/text_boundary.dart, package:flutter/src/services/text_formatter.dart, package:flutter/services.dart, package:flutter/src/services/undo_manager.dart, package:plugin_platform_interface/plugin_platform_interface.dart, package:shared_preferences_platform_interface/method_channel_shared_preferences.dart, package:shared_preferences_platform_interface/shared_preferences_platform_interface.dart, package:shared_preferences/shared_preferences.dart, package:shared_preferences/src/shared_preferences_async.dart, package:shared_preferences/src/shared_preferences_devtools_extension_data.dart, package:shared_preferences/src/shared_preferences_legacy.dart, package:frontend/data/models/user_model.dart, package:http/src/exception.dart, package:http/src/byte_stream.dart, package:path/src/path_exception.dart, package:path/src/characters.dart, package:string_scanner/src/charcode.dart, package:http_parser/src/case_insensitive_map.dart, package:http_parser/src/chunked_coding/charcodes.dart, package:http_parser/src/chunked_coding/encoder.dart, package:http/src/boundary_characters.dart, package:frontend/core/constants/api_constants.dart, package:flutter/src/semantics/debug.dart, package:flutter/src/semantics/binding.dart, package:flutter/src/painting/basic_types.dart, package:flutter/src/painting/debug.dart, package:flutter/src/painting/shader_warm_up.dart, package:flutter/src/painting/box_shadow.dart, package:flutter/src/painting/colors.dart, package:flutter/src/painting/box_fit.dart, package:flutter/src/painting/clip.dart, package:flutter/src/painting/text_scaler.dart, package:flutter/src/painting/inline_span.dart, package:flutter/src/painting/placeholder_span.dart, package:flutter/src/painting/strut_style.dart, package:flutter/src/painting/text_painter.dart, package:flutter/src/painting/text_span.dart, package:flutter/src/painting/text_style.dart, package:flutter/src/painting/matrix_utils.dart, package:flutter/src/rendering/service_extensions.dart, package:flutter/src/widgets/service_extensions.dart, package:flutter/src/widgets/icon_data.dart, package:flutter/src/widgets/standard_component_type.dart, package:logging/src/level.dart, package:logging/src/log_record.dart, package:logging/src/logger.dart, package:logging/logging.dart, package:go_router/src/logging.dart, package:go_router/src/misc/errors.dart, package:state_notifier/state_notifier.dart, package:riverpod/src/pragma.dart, package:riverpod/src/result.dart, package:riverpod/src/run_guarded.dart, package:stack_trace/src/utils.dart, package:path/path.dart, package:path/src/context.dart, package:path/src/path_map.dart, package:path/src/path_set.dart, package:path/src/style.dart, package:stack_trace/src/chain.dart, package:stack_trace/src/frame.dart, package:stack_trace/src/lazy_chain.dart, package:stack_trace/src/lazy_trace.dart, package:stack_trace/src/stack_zone_specification.dart, package:stack_trace/src/trace.dart, package:stack_trace/src/unparsed_frame.dart, package:stack_trace/src/vm_trace.dart, package:stack_trace/stack_trace.dart, package:riverpod/src/stack_trace.dart, package:riverpod/src/common/env.dart, package:riverpod/src/state_controller.dart, package:riverpod/src/async_notifier.dart, package:riverpod/src/builders.dart, package:riverpod/src/common.dart, package:riverpod/src/framework.dart, package:riverpod/src/future_provider.dart, package:riverpod/src/internals.dart, package:riverpod/src/listenable.dart, package:riverpod/src/notifier.dart, package:riverpod/src/provider.dart, package:riverpod/src/state_notifier_provider.dart, package:riverpod/src/state_provider.dart, package:riverpod/src/stream_provider.dart, package:riverpod/riverpod.dart, package:frontend/data/models/api_response.dart, package:frontend/data/models/user.dart, package:dio/src/redirect_record.dart, package:term_glyph/src/generated/glyph_set.dart, package:term_glyph/src/generated/ascii_glyph_set.dart, package:term_glyph/src/generated/unicode_glyph_set.dart, package:term_glyph/src/generated/top_level.dart, package:term_glyph/term_glyph.dart, package:source_span/src/file.dart, package:source_span/src/location.dart, package:source_span/src/location_mixin.dart, package:source_span/src/span.dart, package:source_span/src/span_mixin.dart, package:source_span/src/span_with_context.dart, package:source_span/src/span_exception.dart, package:source_span/source_span.dart, package:string_scanner/src/exception.dart, package:string_scanner/src/string_scanner.dart, package:string_scanner/src/line_scanner.dart, package:string_scanner/src/span_scanner.dart, package:string_scanner/string_scanner.dart, package:http_parser/src/authentication_challenge.dart, package:http_parser/src/http_date.dart, package:http_parser/src/media_type.dart, package:dio/src/transformers/util/consolidate_bytes.dart, package:dio/src/transformers/util/transform_empty_to_null.dart, package:async/src/async_memoizer.dart, package:async/src/cancelable_operation.dart, package:async/src/byte_collector.dart, package:async/src/chunked_stream_reader.dart, package:async/src/delegate/event_sink.dart, package:async/src/delegate/future.dart, package:async/src/delegate/sink.dart, package:async/src/delegate/stream.dart, package:async/src/delegate/stream_consumer.dart, package:async/src/delegate/stream_sink.dart, package:async/src/typed/stream_subscription.dart, package:async/src/delegate/stream_subscription.dart, package:async/src/future_group.dart, package:async/src/stream_completer.dart, package:async/src/lazy_stream.dart, package:async/src/null_stream_sink.dart, package:async/src/restartable_timer.dart, package:async/src/stream_sink_transformer.dart, package:async/src/stream_sink_transformer/handler_transformer.dart, package:async/src/stream_sink_transformer/stream_transformer_wrapper.dart, package:async/src/stream_sink_transformer/typed.dart, package:async/src/result/capture_sink.dart, package:async/src/result/capture_transformer.dart, package:async/src/result/error.dart, package:async/src/result/release_sink.dart, package:async/src/result/release_transformer.dart, package:async/src/result/result.dart, package:async/src/result/value.dart, package:async/src/result/future.dart, package:async/src/single_subscription_transformer.dart, package:async/src/sink_base.dart, package:async/src/stream_closer.dart, package:async/src/stream_extensions.dart, package:async/src/stream_group.dart, package:async/src/stream_splitter.dart, package:async/src/subscription_stream.dart, package:async/src/stream_queue.dart, package:async/src/stream_sink_completer.dart, package:async/src/stream_sink_transformer/reject_errors.dart, package:async/src/stream_sink_extensions.dart, package:async/src/stream_subscription_transformer.dart, package:async/src/stream_zip.dart, package:async/src/typed_stream_transformer.dart, package:async/async.dart, package:async/src/async_cache.dart, package:frontend/core/constants/app_constants.dart, package:frontend/data/models/property.dart, package:frontend/data/models/alert.dart, package:frontend/data/models/activity.dart, package:frontend/data/models/dashboard.dart, package:frontend/data/models/office.dart, package:frontend/data/models/notification.dart, package:cross_file/src/types/base.dart, package:cross_file/src/types/interface.dart, package:cross_file/src/x_file.dart, package:cross_file/cross_file.dart, package:image_picker_platform_interface/src/types/picked_file/base.dart, package:image_picker_platform_interface/src/types/picked_file/unsupported.dart, package:image_picker_platform_interface/src/types/camera_device.dart, package:image_picker_platform_interface/src/types/camera_delegate.dart, package:image_picker_platform_interface/src/types/image_source.dart, package:image_picker_platform_interface/src/types/media_selection_type.dart, package:image_picker_platform_interface/src/types/retrieve_type.dart, package:image_picker_platform_interface/image_picker_platform_interface.dart, package:image_picker_platform_interface/src/method_channel/method_channel_image_picker.dart, package:image_picker_platform_interface/src/platform_interface/image_picker_platform.dart, package:image_picker_platform_interface/src/types/image_options.dart, package:image_picker_platform_interface/src/types/lost_data_response.dart, package:image_picker_platform_interface/src/types/media_options.dart, package:image_picker_platform_interface/src/types/multi_image_picker_options.dart, package:image_picker_platform_interface/src/types/picked_file/lost_data.dart, package:image_picker_platform_interface/src/types/picked_file/picked_file.dart, package:image_picker_platform_interface/src/types/types.dart, package:image_picker/image_picker.dart, package:file_picker/src/platform_file.dart, package:file_picker/src/file_picker_result.dart, package:file_picker/src/file_picker.dart, package:file_picker/file_picker.dart, package:file_picker/src/file_picker_io.dart, package:file_picker/src/file_picker_macos.dart, package:file_picker/src/linux/dialog_handler.dart, package:file_picker/src/linux/file_picker_linux.dart, package:file_picker/src/linux/kdialog_handler.dart, package:file_picker/src/linux/qarma_and_zenity_handler.dart, package:file_picker/src/utils.dart, package:file_picker/src/windows/file_picker_windows_stub.dart, package:frontend/core/services/cache_manager.dart, package:connectivity_plus_platform_interface/src/enums.dart, package:connectivity_plus_platform_interface/connectivity_plus_platform_interface.dart, package:connectivity_plus_platform_interface/method_channel_connectivity.dart, package:connectivity_plus_platform_interface/src/utils.dart, package:dbus/src/dbus_address.dart, package:dbus/src/dbus_uuid.dart, package:dbus/src/getsid_stub.dart, package:dbus/src/getsid.dart, package:dbus/src/getuid_stub.dart, package:dbus/src/getuid.dart, package:dbus/src/dbus_auth_client.dart, package:dbus/src/dbus_bus_name.dart, package:dbus/src/dbus_error_name.dart, package:dbus/src/dbus_interface_name.dart, package:dbus/src/dbus_member_name.dart, package:dbus/src/dbus_value.dart, package:dbus/src/dbus_message.dart, package:dbus/src/dbus_match_rule.dart, package:dbus/src/dbus_method_call.dart, package:dbus/src/dbus_method_response.dart, package:xml/src/xml/exceptions/exception.dart, package:xml/src/xml/enums/node_type.dart, package:xml/src/xml/enums/attribute_type.dart, package:xml/src/xml/utils/token.dart, package:xml/src/xml/entities/entity_mapping.dart, package:xml/src/xml/utils/predicate.dart, package:xml/src/xml/dtd/external_id.dart, package:petitparser/src/shared/annotations.dart, package:petitparser/src/core/context.dart, package:petitparser/src/core/exception.dart, package:petitparser/src/core/parser.dart, package:petitparser/src/core/result.dart, package:petitparser/src/core/token.dart, package:petitparser/src/matcher/matches.dart, package:petitparser/src/matcher/matches/matches_iterable.dart, package:petitparser/src/matcher/matches/matches_iterator.dart, package:petitparser/src/parser/action/token.dart, package:petitparser/src/parser/combinator/delegate.dart, package:petitparser/src/parser/misc/newline.dart, package:petitparser/core.dart, package:xml/src/xml/exceptions/format_exception.dart, package:xml/src/xml/exceptions/parser_exception.dart, package:xml/src/xml/exceptions/tag_exception.dart, package:xml/src/xml/entities/named_entities.dart, package:xml/src/xml/entities/default_mapping.dart, package:petitparser/petitparser.dart, package:xml/src/xml_events/utils/conversion_sink.dart, package:xml/src/xml_events/utils/list_converter.dart, package:xml/src/xml/utils/cache.dart, package:xml/src/xml/utils/character_data_parser.dart, package:xml/src/xml_events/streams/flatten.dart, package:xml/src/xml/entities/null_mapping.dart, package:dbus/src/dbus_buffer.dart, package:dbus/src/dbus_read_buffer.dart, package:dbus/src/dbus_signal.dart, package:dbus/src/dbus_write_buffer.dart, package:dbus/src/dbus_auth_server.dart, package:frontend/data/models/maintenance.dart, package:fl_chart/src/chart/base/base_chart/fl_touch_event.dart, package:equatable/equatable.dart, package:equatable/src/equatable.dart, package:equatable/src/equatable_config.dart, package:equatable/src/equatable_mixin.dart, package:equatable/src/equatable_utils.dart, package:fl_chart/src/utils/list_wrapper.dart, package:fl_chart/src/utils/path_drawing/dash_path.dart, package:fl_chart/src/extensions/path_extension.dart, package:url_launcher_platform_interface/src/types.dart, package:url_launcher/src/types.dart, package:frontend/data/models/ott_service.dart, package:flutter/src/painting/image_stream.dart, package:flutter/src/painting/image_cache.dart, package:flutter/src/painting/binding.dart, package:flutter/src/painting/_network_image_io.dart, package:flutter/src/painting/image_provider.dart, package:flutter/src/painting/geometry.dart, package:flutter/src/painting/image_resolution.dart, package:flutter/src/painting/alignment.dart, package:flutter/src/painting/edge_insets.dart, package:flutter/src/painting/borders.dart, package:flutter/src/painting/decoration.dart, package:flutter/src/painting/decoration_image.dart, package:flutter/src/painting/gradient.dart, package:flutter/src/painting/circle_border.dart, package:flutter/src/painting/flutter_logo.dart, package:flutter/src/painting/fractional_offset.dart, package:flutter/src/painting/linear_border.dart, package:flutter/src/painting/notched_shapes.dart, package:flutter/src/painting/oval_border.dart, package:frontend/data/models/system.dart, package:flutter/src/painting/border_radius.dart, package:flutter/src/painting/beveled_rectangle_border.dart, package:flutter/src/painting/box_border.dart, package:flutter/src/painting/box_decoration.dart, package:flutter/src/painting/continuous_rectangle_border.dart, package:flutter/src/painting/image_decoder.dart, package:flutter/src/painting/paint_utilities.dart, package:flutter/src/painting/rounded_rectangle_border.dart, package:flutter/src/painting/shape_decoration.dart, package:flutter/src/painting/stadium_border.dart, package:flutter/src/painting/star_border.dart, package:flutter/painting.dart, package:flutter/src/semantics/semantics_event.dart, package:flutter/src/semantics/semantics.dart, package:flutter/src/semantics/semantics_service.dart, package:flutter/semantics.dart, package:flutter/src/rendering/table_border.dart, package:flutter/src/material/colors.dart, package:flutter/src/material/constants.dart, package:flutter/src/material/shadows.dart, package:xml/src/xml_events/annotations/has_buffer.dart, package:xml/src/xml_events/annotations/has_location.dart, package:xml/src/xml/exceptions/parent_exception.dart, package:xml/src/xml/exceptions/type_exception.dart, package:xml/src/xml/extensions/descendants.dart, package:xml/src/xml/extensions/mutator.dart, package:xml/src/xml/extensions/parent.dart, package:xml/src/xml/extensions/sibling.dart, package:xml/src/xml/extensions/string.dart, package:xml/src/xml/mixins/has_attributes.dart, package:xml/src/xml/mixins/has_children.dart, package:xml/src/xml/mixins/has_name.dart, package:xml/src/xml/mixins/has_parent.dart, package:xml/src/xml/mixins/has_value.dart, package:xml/src/xml/mixins/has_visitor.dart, package:xml/src/xml/mixins/has_writer.dart, package:xml/src/xml/nodes/attribute.dart, package:xml/src/xml/nodes/cdata.dart, package:xml/src/xml/nodes/comment.dart, package:xml/src/xml/nodes/data.dart, package:xml/src/xml/nodes/declaration.dart, package:xml/src/xml/nodes/doctype.dart, package:xml/src/xml/nodes/document.dart, package:xml/src/xml/nodes/document_fragment.dart, package:xml/src/xml/nodes/element.dart, package:xml/src/xml/nodes/node.dart, package:xml/src/xml/nodes/processing.dart, package:xml/src/xml/nodes/text.dart, package:xml/src/xml/utils/name.dart, package:xml/src/xml/utils/name_matcher.dart, package:xml/src/xml/utils/namespace.dart, package:xml/src/xml/utils/node_list.dart, package:xml/src/xml/utils/prefix_name.dart, package:xml/src/xml/utils/simple_name.dart, package:xml/src/xml/visitors/pretty_writer.dart, package:xml/src/xml/visitors/visitor.dart, package:xml/src/xml/visitors/writer.dart, package:xml/src/xml_events/annotations/annotator.dart, package:xml/src/xml_events/annotations/has_parent.dart, package:xml/src/xml_events/codec/event_codec.dart, package:xml/src/xml_events/codec/node_codec.dart, package:xml/src/xml_events/converters/event_decoder.dart, package:xml/src/xml_events/converters/event_encoder.dart, package:xml/src/xml_events/converters/node_decoder.dart, package:xml/src/xml_events/converters/node_encoder.dart, package:xml/src/xml_events/event.dart, package:xml/src/xml_events/events/cdata.dart, package:xml/src/xml_events/events/comment.dart, package:xml/src/xml_events/events/declaration.dart, package:xml/src/xml_events/events/doctype.dart, package:xml/src/xml_events/events/end_element.dart, package:xml/src/xml_events/events/processing.dart, package:xml/src/xml_events/events/start_element.dart, package:xml/src/xml_events/events/text.dart, package:xml/src/xml_events/iterable.dart, package:xml/src/xml_events/iterator.dart, package:xml/src/xml_events/parser.dart, package:xml/src/xml_events/streams/each_event.dart, package:xml/src/xml_events/streams/normalizer.dart, package:xml/src/xml_events/streams/subtree_selector.dart, package:xml/src/xml_events/streams/with_parent.dart, package:xml/src/xml_events/utils/event_attribute.dart, package:xml/src/xml_events/utils/named.dart, package:xml/src/xml_events/visitor.dart, package:xml/xml_events.dart, package:xml/src/xml/builder.dart, package:xml/src/xml/extensions/ancestors.dart, package:xml/src/xml/extensions/find.dart, package:xml/src/xml/extensions/following.dart, package:xml/src/xml/extensions/nodes.dart, package:xml/src/xml/extensions/preceding.dart, package:xml/src/xml/visitors/normalizer.dart, package:xml/src/xml/extensions/comparison.dart, package:xml/xml.dart, package:dbus/src/dbus_introspect.dart, package:dbus/src/dbus_peer.dart, package:dbus/src/dbus_client.dart, package:dbus/src/dbus_introspectable.dart, package:dbus/src/dbus_object.dart, package:dbus/src/dbus_object_manager.dart, package:dbus/src/dbus_object_tree.dart, package:dbus/src/dbus_properties.dart, package:dbus/src/dbus_remote_object.dart, package:dbus/src/dbus_remote_object_manager.dart, package:dbus/src/dbus_server.dart, package:dbus/dbus.dart, package:nm/src/network_manager_client.dart, package:nm/nm.dart, package:connectivity_plus/src/connectivity_plus_linux.dart, package:connectivity_plus/connectivity_plus.dart, package:frontend/core/services/connectivity_manager.dart, package:fl_chart/src/extensions/gradient_extension.dart, package:fl_chart/src/chart/base/line.dart, package:flutter/src/physics/tolerance.dart, package:flutter/src/physics/simulation.dart, package:flutter/src/physics/friction_simulation.dart, package:flutter/src/physics/gravity_simulation.dart, package:flutter/src/physics/utils.dart, package:flutter/src/physics/spring_simulation.dart, package:flutter/src/physics/clamped_simulation.dart, package:flutter/physics.dart, package:flutter/src/widgets/scroll_simulation.dart, package:flutter/src/painting/_web_image_info_io.dart, package:flutter/src/widgets/constants.dart, package:flutter/animation.dart, package:flutter/cupertino.dart, package:flutter/rendering.dart, package:flutter/src/animation/animation.dart, package:flutter/src/animation/animation_controller.dart, package:flutter/src/animation/animation_style.dart, package:flutter/src/animation/animations.dart, package:flutter/src/animation/curves.dart, package:flutter/src/animation/listener_helpers.dart, package:flutter/src/animation/tween.dart, package:flutter/src/animation/tween_sequence.dart, package:flutter/src/cupertino/activity_indicator.dart, package:flutter/src/cupertino/adaptive_text_selection_toolbar.dart, package:flutter/src/cupertino/app.dart, package:flutter/src/cupertino/bottom_tab_bar.dart, package:flutter/src/cupertino/button.dart, package:flutter/src/cupertino/checkbox.dart, package:flutter/src/cupertino/colors.dart, package:flutter/src/cupertino/constants.dart, package:flutter/src/cupertino/context_menu.dart, package:flutter/src/cupertino/context_menu_action.dart, package:flutter/src/cupertino/date_picker.dart, package:flutter/src/cupertino/debug.dart, package:flutter/src/cupertino/desktop_text_selection.dart, package:flutter/src/cupertino/desktop_text_selection_toolbar.dart, package:flutter/src/cupertino/desktop_text_selection_toolbar_button.dart, package:flutter/src/cupertino/dialog.dart, package:flutter/src/cupertino/form_row.dart, package:flutter/src/cupertino/form_section.dart, package:flutter/src/cupertino/icon_theme_data.dart, package:flutter/src/cupertino/icons.dart, package:flutter/src/cupertino/interface_level.dart, package:flutter/src/cupertino/list_section.dart, package:flutter/src/cupertino/list_tile.dart, package:flutter/src/cupertino/localizations.dart, package:flutter/src/cupertino/magnifier.dart, package:flutter/src/cupertino/nav_bar.dart, package:flutter/src/cupertino/page_scaffold.dart, package:flutter/src/cupertino/picker.dart, package:flutter/src/cupertino/radio.dart, package:flutter/src/cupertino/refresh.dart, package:flutter/src/cupertino/route.dart, package:flutter/src/cupertino/scrollbar.dart, package:flutter/src/cupertino/search_field.dart, package:flutter/src/cupertino/segmented_control.dart, package:flutter/src/cupertino/sheet.dart, package:flutter/src/cupertino/slider.dart, package:flutter/src/cupertino/sliding_segmented_control.dart, package:flutter/src/cupertino/spell_check_suggestions_toolbar.dart, package:flutter/src/cupertino/switch.dart, package:flutter/src/cupertino/tab_scaffold.dart, package:flutter/src/cupertino/tab_view.dart, package:flutter/src/cupertino/text_field.dart, package:flutter/src/cupertino/text_form_field_row.dart, package:flutter/src/cupertino/text_selection.dart, package:flutter/src/cupertino/text_selection_toolbar.dart, package:flutter/src/cupertino/text_selection_toolbar_button.dart, package:flutter/src/cupertino/text_theme.dart, package:flutter/src/cupertino/theme.dart, package:flutter/src/cupertino/thumb_painter.dart, package:flutter/src/rendering/animated_size.dart, package:flutter/src/rendering/binding.dart, package:flutter/src/rendering/box.dart, package:flutter/src/rendering/custom_layout.dart, package:flutter/src/rendering/custom_paint.dart, package:flutter/src/rendering/debug.dart, package:flutter/src/rendering/debug_overflow_indicator.dart, package:flutter/src/rendering/decorated_sliver.dart, package:flutter/src/rendering/editable.dart, package:flutter/src/rendering/error.dart, package:flutter/src/rendering/flex.dart, package:flutter/src/rendering/flow.dart, package:flutter/src/rendering/image.dart, package:flutter/src/rendering/layer.dart, package:flutter/src/rendering/layout_helper.dart, package:flutter/src/rendering/list_body.dart, package:flutter/src/rendering/list_wheel_viewport.dart, package:flutter/src/rendering/mouse_tracker.dart, package:flutter/src/rendering/object.dart, package:flutter/src/rendering/paragraph.dart, package:flutter/src/rendering/performance_overlay.dart, package:flutter/src/rendering/platform_view.dart, package:flutter/src/rendering/proxy_box.dart, package:flutter/src/rendering/proxy_sliver.dart, package:flutter/src/rendering/rotated_box.dart, package:flutter/src/rendering/selection.dart, package:flutter/src/rendering/shifted_box.dart, package:flutter/src/rendering/sliver.dart, package:flutter/src/rendering/sliver_fill.dart, package:flutter/src/rendering/sliver_fixed_extent_list.dart, package:flutter/src/rendering/sliver_grid.dart, package:flutter/src/rendering/sliver_group.dart, package:flutter/src/rendering/sliver_list.dart, package:flutter/src/rendering/sliver_multi_box_adaptor.dart, package:flutter/src/rendering/sliver_padding.dart, package:flutter/src/rendering/sliver_persistent_header.dart, package:flutter/src/rendering/sliver_tree.dart, package:flutter/src/rendering/stack.dart, package:flutter/src/rendering/table.dart, package:flutter/src/rendering/texture.dart, package:flutter/src/rendering/tweens.dart, package:flutter/src/rendering/view.dart, package:flutter/src/rendering/viewport.dart, package:flutter/src/rendering/viewport_offset.dart, package:flutter/src/rendering/wrap.dart, package:flutter/src/widgets/_html_element_view_io.dart, package:flutter/src/widgets/_platform_selectable_region_context_menu_io.dart, package:flutter/src/widgets/_web_image_io.dart, package:flutter/src/widgets/actions.dart, package:flutter/src/widgets/adapter.dart, package:flutter/src/widgets/animated_cross_fade.dart, package:flutter/src/widgets/animated_scroll_view.dart, package:flutter/src/widgets/animated_size.dart, package:flutter/src/widgets/animated_switcher.dart, package:flutter/src/widgets/annotated_region.dart, package:flutter/src/widgets/app.dart, package:flutter/src/widgets/app_lifecycle_listener.dart, package:flutter/src/widgets/async.dart, package:flutter/src/widgets/autocomplete.dart, package:flutter/src/widgets/autofill.dart, package:flutter/src/widgets/automatic_keep_alive.dart, package:flutter/src/widgets/banner.dart, package:flutter/src/widgets/basic.dart, package:flutter/src/widgets/binding.dart, package:flutter/src/widgets/bottom_navigation_bar_item.dart, package:flutter/src/widgets/color_filter.dart, package:flutter/src/widgets/container.dart, package:flutter/src/widgets/context_menu_button_item.dart, package:flutter/src/widgets/context_menu_controller.dart, package:flutter/src/widgets/debug.dart, package:flutter/src/widgets/decorated_sliver.dart, package:flutter/src/widgets/default_selection_style.dart, package:flutter/src/widgets/default_text_editing_shortcuts.dart, package:flutter/src/widgets/desktop_text_selection_toolbar_layout_delegate.dart, package:flutter/src/widgets/dismissible.dart, package:flutter/src/widgets/display_feature_sub_screen.dart, package:flutter/src/widgets/disposable_build_context.dart, package:flutter/src/widgets/drag_boundary.dart, package:flutter/src/widgets/drag_target.dart, package:flutter/src/widgets/draggable_scrollable_sheet.dart, package:flutter/src/widgets/dual_transition_builder.dart, package:flutter/src/widgets/editable_text.dart, package:flutter/src/widgets/expansible.dart, package:flutter/src/widgets/fade_in_image.dart, package:flutter/src/widgets/feedback.dart, package:flutter/src/widgets/flutter_logo.dart, package:flutter/src/widgets/focus_manager.dart, package:flutter/src/widgets/focus_scope.dart, package:flutter/src/widgets/focus_traversal.dart, package:flutter/src/widgets/form.dart, package:flutter/src/widgets/framework.dart, package:flutter/src/widgets/gesture_detector.dart, package:flutter/src/widgets/grid_paper.dart, package:flutter/src/widgets/heroes.dart, package:flutter/src/widgets/icon.dart, package:flutter/src/widgets/icon_theme.dart, package:flutter/src/widgets/icon_theme_data.dart, package:flutter/src/widgets/image.dart, package:flutter/src/widgets/image_filter.dart, package:flutter/src/widgets/image_icon.dart, package:flutter/src/widgets/implicit_animations.dart, package:flutter/src/widgets/inherited_model.dart, package:flutter/src/widgets/inherited_notifier.dart, package:flutter/src/widgets/inherited_theme.dart, package:flutter/src/widgets/interactive_viewer.dart, package:flutter/src/widgets/keyboard_listener.dart, package:flutter/src/widgets/layout_builder.dart, package:flutter/src/widgets/list_wheel_scroll_view.dart, package:flutter/src/widgets/localizations.dart, package:flutter/src/widgets/lookup_boundary.dart, package:flutter/src/widgets/magnifier.dart, package:flutter/src/widgets/media_query.dart, package:flutter/src/widgets/modal_barrier.dart, package:flutter/src/widgets/navigation_toolbar.dart, package:flutter/src/widgets/navigator.dart, package:flutter/src/widgets/navigator_pop_handler.dart, package:flutter/src/widgets/nested_scroll_view.dart, package:flutter/src/widgets/notification_listener.dart, package:flutter/src/widgets/orientation_builder.dart, package:flutter/src/widgets/overflow_bar.dart, package:flutter/src/widgets/overlay.dart, package:flutter/src/widgets/overscroll_indicator.dart, package:flutter/src/widgets/page_storage.dart, package:flutter/src/widgets/page_view.dart, package:flutter/src/widgets/pages.dart, package:flutter/src/widgets/performance_overlay.dart, package:flutter/src/widgets/pinned_header_sliver.dart, package:flutter/src/widgets/placeholder.dart, package:flutter/src/widgets/platform_menu_bar.dart, package:flutter/src/widgets/platform_selectable_region_context_menu.dart, package:flutter/src/widgets/platform_view.dart, package:flutter/src/widgets/pop_scope.dart, package:flutter/src/widgets/preferred_size.dart, package:flutter/src/widgets/primary_scroll_controller.dart, package:flutter/src/widgets/raw_keyboard_listener.dart, package:flutter/src/widgets/raw_menu_anchor.dart, package:flutter/src/widgets/reorderable_list.dart, package:flutter/src/widgets/restoration.dart, package:flutter/src/widgets/restoration_properties.dart, package:flutter/src/widgets/router.dart, package:flutter/src/widgets/routes.dart, package:flutter/src/widgets/safe_area.dart, package:flutter/src/widgets/scroll_activity.dart, package:flutter/src/widgets/scroll_aware_image_provider.dart, package:flutter/src/widgets/scroll_configuration.dart, package:flutter/src/widgets/scroll_context.dart, package:flutter/src/widgets/scroll_controller.dart, package:flutter/src/widgets/scroll_delegate.dart, package:flutter/src/widgets/scroll_metrics.dart, package:flutter/src/widgets/scroll_notification.dart, package:flutter/src/widgets/scroll_notification_observer.dart, package:flutter/src/widgets/scroll_physics.dart, package:flutter/src/widgets/scroll_position.dart, package:flutter/src/widgets/scroll_position_with_single_context.dart, package:flutter/src/widgets/scroll_view.dart, package:flutter/src/widgets/scrollable.dart, package:flutter/src/widgets/scrollable_helpers.dart, package:flutter/src/widgets/scrollbar.dart, package:flutter/src/widgets/selectable_region.dart, package:flutter/src/widgets/selection_container.dart, package:flutter/src/widgets/semantics_debugger.dart, package:flutter/src/widgets/shared_app_data.dart, package:flutter/src/widgets/shortcuts.dart, package:flutter/src/widgets/single_child_scroll_view.dart, package:flutter/src/widgets/size_changed_layout_notifier.dart, package:flutter/src/widgets/sliver.dart, package:flutter/src/widgets/sliver_fill.dart, package:flutter/src/widgets/sliver_floating_header.dart, package:flutter/src/widgets/sliver_layout_builder.dart, package:flutter/src/widgets/sliver_persistent_header.dart, package:flutter/src/widgets/sliver_prototype_extent_list.dart, package:flutter/src/widgets/sliver_resizing_header.dart, package:flutter/src/widgets/sliver_tree.dart, package:flutter/src/widgets/slotted_render_object_widget.dart, package:flutter/src/widgets/snapshot_widget.dart, package:flutter/src/widgets/spacer.dart, package:flutter/src/widgets/spell_check.dart, package:flutter/src/widgets/status_transitions.dart, package:flutter/src/widgets/system_context_menu.dart, package:flutter/src/widgets/table.dart, package:flutter/src/widgets/tap_region.dart, package:flutter/src/widgets/text.dart, package:flutter/src/widgets/text_editing_intents.dart, package:flutter/src/widgets/text_selection.dart, package:flutter/src/widgets/text_selection_toolbar_anchors.dart, package:flutter/src/widgets/text_selection_toolbar_layout_delegate.dart, package:flutter/src/widgets/texture.dart, package:flutter/src/widgets/ticker_provider.dart, package:flutter/src/widgets/title.dart, package:flutter/src/widgets/toggleable.dart, package:flutter/src/widgets/transitions.dart, package:flutter/src/widgets/tween_animation_builder.dart, package:flutter/src/widgets/two_dimensional_scroll_view.dart, package:flutter/src/widgets/two_dimensional_viewport.dart, package:flutter/src/widgets/undo_history.dart, package:flutter/src/widgets/unique_widget.dart, package:flutter/src/widgets/value_listenable_builder.dart, package:flutter/src/widgets/view.dart, package:flutter/src/widgets/viewport.dart, package:flutter/src/widgets/visibility.dart, package:flutter/src/widgets/widget_inspector.dart, package:flutter/src/widgets/widget_preview.dart, package:flutter/src/widgets/widget_span.dart, package:flutter/src/widgets/widget_state.dart, package:flutter/src/widgets/will_pop_scope.dart, package:flutter/widgets.dart, package:flutter/src/material/icons.dart, package:flutter/src/material/input_border.dart, package:flutter/src/material/material_state.dart, package:flutter/src/material/bottom_sheet_theme.dart, package:flutter/src/material/motion.dart, package:flutter/src/material/material_state_mixin.dart, package:material_color_utilities/utils/math_utils.dart, package:material_color_utilities/utils/color_utils.dart, package:material_color_utilities/hct/viewing_conditions.dart, package:material_color_utilities/hct/cam16.dart, package:material_color_utilities/hct/src/hct_solver.dart, package:material_color_utilities/hct/hct.dart, package:material_color_utilities/blend/blend.dart, package:material_color_utilities/contrast/contrast.dart, package:material_color_utilities/dislike/dislike_analyzer.dart, package:material_color_utilities/palettes/tonal_palette.dart, package:material_color_utilities/dynamiccolor/src/contrast_curve.dart, package:material_color_utilities/dynamiccolor/variant.dart, package:material_color_utilities/dynamiccolor/dynamic_color.dart, package:material_color_utilities/dynamiccolor/dynamic_scheme.dart, package:material_color_utilities/dynamiccolor/material_dynamic_colors.dart, package:material_color_utilities/dynamiccolor/src/tone_delta_pair.dart, package:material_color_utilities/palettes/core_palette.dart, package:material_color_utilities/quantize/quantizer.dart, package:material_color_utilities/quantize/src/point_provider.dart, package:material_color_utilities/quantize/src/point_provider_lab.dart, package:material_color_utilities/quantize/quantizer_wsmeans.dart, package:material_color_utilities/quantize/quantizer_map.dart, package:material_color_utilities/quantize/quantizer_wu.dart, package:material_color_utilities/quantize/quantizer_celebi.dart, package:material_color_utilities/scheme/scheme.dart, package:material_color_utilities/temperature/temperature_cache.dart, package:material_color_utilities/scheme/scheme_content.dart, package:material_color_utilities/scheme/scheme_expressive.dart, package:material_color_utilities/scheme/scheme_fidelity.dart, package:material_color_utilities/scheme/scheme_fruit_salad.dart, package:material_color_utilities/scheme/scheme_monochrome.dart, package:material_color_utilities/scheme/scheme_neutral.dart, package:material_color_utilities/scheme/scheme_rainbow.dart, package:material_color_utilities/scheme/scheme_tonal_spot.dart, package:material_color_utilities/scheme/scheme_vibrant.dart, package:material_color_utilities/score/score.dart, package:material_color_utilities/utils/string_utils.dart, package:material_color_utilities/material_color_utilities.dart, package:flutter/src/material/floating_action_button_theme.dart, package:flutter/src/material/curves.dart, package:flutter/src/material/tab_controller.dart, package:flutter/src/material/tab_indicator.dart, package:flutter/src/material/tooltip_visibility.dart, package:flutter/src/material/action_buttons.dart, package:flutter/src/material/action_icons_theme.dart, package:flutter/src/material/app_bar.dart, package:flutter/src/material/app_bar_theme.dart, package:flutter/src/material/badge_theme.dart, package:flutter/src/material/banner.dart, package:flutter/src/material/banner_theme.dart, package:flutter/src/material/bottom_app_bar_theme.dart, package:flutter/src/material/bottom_navigation_bar.dart, package:flutter/src/material/bottom_navigation_bar_theme.dart, package:flutter/src/material/bottom_sheet.dart, package:flutter/src/material/button.dart, package:flutter/src/material/button_bar_theme.dart, package:flutter/src/material/button_style.dart, package:flutter/src/material/button_style_button.dart, package:flutter/src/material/button_theme.dart, package:flutter/src/material/card_theme.dart, package:flutter/src/material/checkbox.dart, package:flutter/src/material/checkbox_theme.dart, package:flutter/src/material/chip_theme.dart, package:flutter/src/material/color_scheme.dart, package:flutter/src/material/data_table_theme.dart, package:flutter/src/material/date_picker_theme.dart, package:flutter/src/material/debug.dart, package:flutter/src/material/dialog_theme.dart, package:flutter/src/material/divider.dart, package:flutter/src/material/divider_theme.dart, package:flutter/src/material/drawer.dart, package:flutter/src/material/drawer_theme.dart, package:flutter/src/material/dropdown_menu_theme.dart, package:flutter/src/material/elevated_button.dart, package:flutter/src/material/elevated_button_theme.dart, package:flutter/src/material/elevation_overlay.dart, package:flutter/src/material/expansion_tile_theme.dart, package:flutter/src/material/filled_button.dart, package:flutter/src/material/filled_button_theme.dart, package:flutter/src/material/flexible_space_bar.dart, package:flutter/src/material/floating_action_button.dart, package:flutter/src/material/floating_action_button_location.dart, package:flutter/src/material/icon_button.dart, package:flutter/src/material/icon_button_theme.dart, package:flutter/src/material/ink_decoration.dart, package:flutter/src/material/ink_highlight.dart, package:flutter/src/material/ink_ripple.dart, package:flutter/src/material/ink_sparkle.dart, package:flutter/src/material/ink_splash.dart, package:flutter/src/material/ink_well.dart, package:flutter/src/material/input_decorator.dart, package:flutter/src/material/list_tile.dart, package:flutter/src/material/list_tile_theme.dart, package:flutter/src/material/material.dart, package:flutter/src/material/material_button.dart, package:flutter/src/material/material_localizations.dart, package:flutter/src/material/menu_anchor.dart, package:flutter/src/material/menu_bar_theme.dart, package:flutter/src/material/menu_button_theme.dart, package:flutter/src/material/menu_style.dart, package:flutter/src/material/menu_theme.dart, package:flutter/src/material/navigation_bar.dart, package:flutter/src/material/navigation_bar_theme.dart, package:flutter/src/material/navigation_drawer.dart, package:flutter/src/material/navigation_drawer_theme.dart, package:flutter/src/material/navigation_rail.dart, package:flutter/src/material/navigation_rail_theme.dart, package:flutter/src/material/outlined_button.dart, package:flutter/src/material/outlined_button_theme.dart, package:flutter/src/material/page_transitions_theme.dart, package:flutter/src/material/popup_menu_theme.dart, package:flutter/src/material/progress_indicator_theme.dart, package:flutter/src/material/radio.dart, package:flutter/src/material/radio_theme.dart, package:flutter/src/material/scaffold.dart, package:flutter/src/material/scrollbar.dart, package:flutter/src/material/scrollbar_theme.dart, package:flutter/src/material/search_bar_theme.dart, package:flutter/src/material/search_view_theme.dart, package:flutter/src/material/segmented_button_theme.dart, package:flutter/src/material/slider.dart, package:flutter/src/material/slider_theme.dart, package:flutter/src/material/slider_value_indicator_shape.dart, package:flutter/src/material/snack_bar.dart, package:flutter/src/material/snack_bar_theme.dart, package:flutter/src/material/switch_theme.dart, package:flutter/src/material/tab_bar_theme.dart, package:flutter/src/material/tabs.dart, package:flutter/src/material/text_button.dart, package:flutter/src/material/text_button_theme.dart, package:flutter/src/material/text_selection_theme.dart, package:flutter/src/material/text_theme.dart, package:flutter/src/material/theme.dart, package:flutter/src/material/theme_data.dart, package:flutter/src/material/time.dart, package:flutter/src/material/time_picker_theme.dart, package:flutter/src/material/toggle_buttons_theme.dart, package:flutter/src/material/tooltip.dart, package:flutter/src/material/tooltip_theme.dart, package:flutter/src/material/typography.dart, package:flutter/src/material/back_button.dart, package:flutter/src/material/card.dart, package:flutter/src/material/dialog.dart, package:flutter/src/material/page.dart, package:flutter/src/material/progress_indicator.dart, package:flutter/src/material/about.dart, package:flutter/src/material/chip.dart, package:flutter/src/material/action_chip.dart, package:flutter/src/material/text_selection_toolbar.dart, package:flutter/src/material/desktop_text_selection_toolbar.dart, package:flutter/src/material/desktop_text_selection_toolbar_button.dart, package:flutter/src/material/text_selection_toolbar_text_button.dart, package:flutter/src/material/adaptive_text_selection_toolbar.dart, package:flutter/src/material/animated_icons.dart, package:flutter/src/material/arc.dart, package:flutter/src/material/app.dart, package:flutter/src/material/desktop_text_selection.dart, package:flutter/src/material/magnifier.dart, package:flutter/src/material/text_selection.dart, package:flutter/src/material/selectable_text.dart, package:flutter/src/material/spell_check_suggestions_toolbar_layout_delegate.dart, package:flutter/src/material/spell_check_suggestions_toolbar.dart, package:flutter/src/material/text_field.dart, package:flutter/src/material/text_form_field.dart, package:flutter/src/material/autocomplete.dart, package:flutter/src/material/badge.dart, package:flutter/src/material/bottom_app_bar.dart, package:flutter/src/material/button_bar.dart, package:flutter/src/material/date.dart, package:flutter/src/material/calendar_date_picker.dart, package:flutter/src/material/carousel.dart, package:flutter/src/material/checkbox_list_tile.dart, package:flutter/src/material/choice_chip.dart, package:flutter/src/material/circle_avatar.dart, package:flutter/src/material/dropdown.dart, package:flutter/src/material/data_table.dart, package:flutter/src/material/data_table_source.dart, package:flutter/src/material/input_date_picker_form_field.dart, package:flutter/src/material/date_picker.dart, package:flutter/src/material/drawer_header.dart, package:flutter/src/material/dropdown_menu.dart, package:flutter/src/material/expand_icon.dart, package:flutter/src/material/mergeable_material.dart, package:flutter/src/material/expansion_panel.dart, package:flutter/src/material/expansion_tile.dart, package:flutter/src/material/filter_chip.dart, package:flutter/src/material/grid_tile.dart, package:flutter/src/material/grid_tile_bar.dart, package:flutter/src/material/input_chip.dart, package:flutter/src/material/no_splash.dart, package:flutter/src/material/paginated_data_table.dart, package:flutter/src/material/popup_menu.dart, package:flutter/src/material/predictive_back_page_transitions_builder.dart, package:flutter/src/material/radio_list_tile.dart, package:flutter/src/material/range_slider.dart, package:flutter/src/material/refresh_indicator.dart, package:flutter/src/material/reorderable_list.dart, package:flutter/src/material/search.dart, package:flutter/src/material/search_anchor.dart, package:flutter/src/material/segmented_button.dart, package:flutter/src/material/selection_area.dart, package:flutter/src/material/stepper.dart, package:flutter/src/material/switch.dart, package:flutter/src/material/switch_list_tile.dart, package:flutter/src/material/time_picker.dart, package:flutter/src/material/toggle_buttons.dart, package:flutter/src/material/user_accounts_drawer_header.dart, package:flutter/material.dart, package:flutter_riverpod/src/builders.dart, package:flutter_riverpod/src/change_notifier_provider.dart, package:flutter_riverpod/src/consumer.dart, package:flutter_riverpod/src/framework.dart, package:flutter_riverpod/src/internals.dart, package:flutter_riverpod/flutter_riverpod.dart, package:nested/nested.dart, package:provider/src/reassemble_handler.dart, package:provider/src/provider.dart, package:provider/src/async_provider.dart, package:provider/src/proxy_provider.dart, package:provider/src/change_notifier_provider.dart, package:provider/src/listenable_provider.dart, package:provider/src/consumer.dart, package:provider/src/selector.dart, package:provider/src/value_listenable_provider.dart, package:provider/provider.dart, package:go_router/src/pages/custom_transition_page.dart, package:go_router/src/builder.dart, package:go_router/src/configuration.dart, package:go_router/src/delegate.dart, package:go_router/src/information_provider.dart, package:go_router/src/match.dart, package:go_router/src/misc/error_screen.dart, package:go_router/src/misc/extensions.dart, package:go_router/src/misc/inherited_router.dart, package:go_router/src/pages/cupertino.dart, package:go_router/src/pages/material.dart, package:go_router/src/parser.dart, package:go_router/src/path_utils.dart, package:go_router/src/route.dart, package:go_router/src/route_data.dart, package:go_router/src/router.dart, package:go_router/src/state.dart, package:go_router/go_router.dart, package:frontend/core/theme/app_theme.dart, package:fl_chart/src/extensions/color_extension.dart, package:fl_chart/src/utils/utils.dart, package:fl_chart/src/extensions/paint_extension.dart, package:fl_chart/src/extensions/rrect_extension.dart, package:fl_chart/src/chart/base/axis_chart/side_titles/side_titles_flex.dart, package:fl_chart/src/extensions/edge_insets_extension.dart, package:fl_chart/src/extensions/border_extension.dart, package:fl_chart/src/extensions/text_align_extension.dart, package:fl_chart/fl_chart.dart, package:fl_chart/src/chart/bar_chart/bar_chart.dart, package:fl_chart/src/chart/bar_chart/bar_chart_data.dart, package:fl_chart/src/chart/bar_chart/bar_chart_helper.dart, package:fl_chart/src/chart/bar_chart/bar_chart_painter.dart, package:fl_chart/src/chart/bar_chart/bar_chart_renderer.dart, package:fl_chart/src/chart/base/axis_chart/axis_chart_data.dart, package:fl_chart/src/chart/base/axis_chart/axis_chart_extensions.dart, package:fl_chart/src/chart/base/axis_chart/axis_chart_helper.dart, package:fl_chart/src/chart/base/axis_chart/axis_chart_painter.dart, package:fl_chart/src/chart/base/axis_chart/axis_chart_scaffold_widget.dart, package:fl_chart/src/chart/base/axis_chart/axis_chart_widgets.dart, package:fl_chart/src/chart/base/axis_chart/side_titles/side_titles_widget.dart, package:fl_chart/src/chart/base/base_chart/base_chart_data.dart, package:fl_chart/src/chart/base/base_chart/base_chart_painter.dart, package:fl_chart/src/chart/base/base_chart/render_base_chart.dart, package:fl_chart/src/chart/line_chart/line_chart.dart, package:fl_chart/src/chart/line_chart/line_chart_data.dart, package:fl_chart/src/chart/line_chart/line_chart_helper.dart, package:fl_chart/src/chart/line_chart/line_chart_painter.dart, package:fl_chart/src/chart/line_chart/line_chart_renderer.dart, package:fl_chart/src/chart/pie_chart/pie_chart.dart, package:fl_chart/src/chart/pie_chart/pie_chart_data.dart, package:fl_chart/src/chart/pie_chart/pie_chart_helper.dart, package:fl_chart/src/chart/pie_chart/pie_chart_painter.dart, package:fl_chart/src/chart/pie_chart/pie_chart_renderer.dart, package:fl_chart/src/chart/radar_chart/radar_chart.dart, package:fl_chart/src/chart/radar_chart/radar_chart_data.dart, package:fl_chart/src/chart/radar_chart/radar_chart_painter.dart, package:fl_chart/src/chart/radar_chart/radar_chart_renderer.dart, package:fl_chart/src/chart/radar_chart/radar_extension.dart, package:fl_chart/src/chart/scatter_chart/scatter_chart.dart, package:fl_chart/src/chart/scatter_chart/scatter_chart_data.dart, package:fl_chart/src/chart/scatter_chart/scatter_chart_helper.dart, package:fl_chart/src/chart/scatter_chart/scatter_chart_painter.dart, package:fl_chart/src/chart/scatter_chart/scatter_chart_renderer.dart, package:fl_chart/src/extensions/bar_chart_data_extension.dart, package:fl_chart/src/extensions/fl_border_data_extension.dart, package:fl_chart/src/extensions/fl_titles_data_extension.dart, package:fl_chart/src/extensions/side_titles_extension.dart, package:fl_chart/src/utils/canvas_wrapper.dart, package:fl_chart/src/utils/lerp.dart, package:frontend/presentation/screens/dashboard/widgets/dashboard_header.dart, package:frontend/presentation/screens/dashboard/widgets/system_status_card.dart, package:frontend/presentation/screens/dashboard/widgets/property_grid.dart, package:frontend/presentation/screens/dashboard/widgets/alerts_feed.dart, package:frontend/presentation/screens/dashboard/widgets/system_health_chart.dart, package:frontend/presentation/screens/dashboard/widgets/quick_actions.dart, package:frontend/presentation/screens/dashboard/widgets/performance_metrics.dart, package:url_launcher_platform_interface/link.dart, package:url_launcher_platform_interface/method_channel_url_launcher.dart, package:url_launcher_platform_interface/src/url_launcher_platform.dart, package:url_launcher_platform_interface/url_launcher_platform_interface.dart, package:url_launcher/src/legacy_api.dart, package:url_launcher/src/type_conversion.dart, package:url_launcher/src/url_launcher_string.dart, package:url_launcher/url_launcher_string.dart, package:url_launcher/src/url_launcher_uri.dart, package:url_launcher/url_launcher.dart, package:matcher/src/interfaces.dart, package:matcher/src/core_matchers.dart, package:matcher/src/custom_matcher.dart, package:matcher/src/description.dart, package:matcher/src/equals_matcher.dart, package:matcher/src/feature_matcher.dart, package:matcher/src/having_matcher.dart, package:matcher/src/pretty_print.dart, package:matcher/src/type_matcher.dart, package:matcher/src/util.dart, package:matcher/src/error_matchers.dart, package:matcher/src/iterable_matchers.dart, package:matcher/src/map_matchers.dart, package:matcher/src/numeric_matchers.dart, package:matcher/src/operator_matchers.dart, package:matcher/src/order_matchers.dart, package:matcher/src/string_matchers.dart, package:matcher/matcher.dart, package:test_api/src/backend/closed_exception.dart, package:test_api/src/backend/configuration/timeout.dart, package:boolean_selector/boolean_selector.dart, package:test_api/src/backend/configuration/skip.dart, package:test_api/src/backend/compiler.dart, package:test_api/src/backend/operating_system.dart, package:test_api/src/backend/runtime.dart, package:test_api/src/backend/suite_platform.dart, package:test_api/src/backend/platform_selector.dart, package:test_api/src/backend/util/identifier_regex.dart, package:test_api/src/backend/util/pretty_print.dart, package:test_api/src/backend/metadata.dart, package:test_api/src/backend/message.dart, package:test_api/src/backend/state.dart, package:test_api/src/backend/group.dart, package:test_api/src/backend/group_entry.dart, package:test_api/src/backend/live_test.dart, package:test_api/src/backend/suite.dart, package:test_api/src/backend/test.dart, package:test_api/src/backend/live_test_controller.dart, package:test_api/src/backend/test_failure.dart, package:test_api/src/backend/declarer.dart, package:test_api/src/backend/invoker.dart, package:test_api/src/backend/stack_trace_mapper.dart, package:test_api/src/backend/stack_trace_formatter.dart, package:test_api/src/scaffolding/utils.dart, package:test_api/hooks.dart, package:matcher/src/expect/util/pretty_print.dart, package:matcher/src/expect/async_matcher.dart, package:matcher/src/expect/expect.dart, package:matcher/src/expect/future_matchers.dart, package:matcher/src/expect/prints_matcher.dart, package:matcher/src/expect/throws_matcher.dart, package:matcher/src/expect/util/placeholder.dart, package:matcher/src/expect/expect_async.dart, package:matcher/src/expect/never_called.dart, package:matcher/src/expect/stream_matcher.dart, package:matcher/src/expect/stream_matchers.dart, package:matcher/src/expect/throws_matchers.dart, package:matcher/expect.dart, package:flutter_test/src/test_async_utils.dart, package:flutter_test/src/_goldens_io.dart, package:flutter_test/src/goldens.dart, package:test_api/src/backend/configuration/on_platform.dart, package:test_api/src/backend/configuration/retry.dart, package:test_api/src/backend/configuration/tags.dart, package:test_api/src/backend/configuration/test_on.dart, package:stream_channel/src/close_guarantee_channel.dart, package:stream_channel/src/delegating_stream_channel.dart, package:stream_channel/src/disconnector.dart, package:stream_channel/src/guarantee_channel.dart, package:stream_channel/src/json_document_transformer.dart, package:stream_channel/src/multi_channel.dart, package:stream_channel/src/stream_channel_completer.dart, package:stream_channel/src/stream_channel_controller.dart, package:stream_channel/src/stream_channel_transformer.dart, package:stream_channel/stream_channel.dart, package:test_api/src/backend/remote_exception.dart, package:test_api/src/utils.dart, package:test_api/src/scaffolding/test_structure.dart, package:test_api/src/scaffolding/spawn_hybrid.dart, package:test_api/scaffolding.dart, package:clock/src/utils.dart, package:clock/clock.dart, package:clock/src/clock.dart, package:clock/src/default.dart, package:clock/src/stopwatch.dart, package:fake_async/fake_async.dart, package:flutter_test/src/platform.dart, package:flutter_test/src/restoration.dart, package:flutter_test/src/stack_manipulation.dart, package:flutter_test/src/test_exception_reporter.dart, package:flutter_test/src/window.dart, package:flutter_test/src/test_pointer.dart, package:flutter_test/src/tree_traversal.dart, package:flutter_test/src/mock_event_channel.dart, package:vm_service/src/_stream_helpers.dart, package:vm_service/src/snapshot_graph.dart, package:vm_service/src/vm_service.dart, package:vm_service/src/dart_io_extensions.dart, package:vm_service/vm_service.dart, package:leak_tracker/src/shared/_primitives.dart, package:leak_tracker/src/shared/_util.dart, package:leak_tracker/src/shared/_formatting.dart, package:vm_service/vm_service_io.dart, package:leak_tracker/src/leak_tracking/primitives/_retaining_path/_connection.dart, package:leak_tracker/src/leak_tracking/primitives/_retaining_path/_retaining_path_web.dart, package:leak_tracker/src/leak_tracking/primitives/_retaining_path/_retaining_path.dart, package:leak_tracker/src/leak_tracking/helpers.dart, package:leak_tracker/src/shared/shared_model.dart, package:leak_tracker/src/devtools_integration/messages.dart, package:leak_tracker/src/devtools_integration/_protocol.dart, package:leak_tracker/src/devtools_integration/primitives.dart, package:leak_tracker/src/devtools_integration/delivery.dart, package:leak_tracker/src/devtools_integration/_registration.dart, package:leak_tracker/src/leak_tracking/primitives/_print_bytes.dart, package:leak_tracker/src/leak_tracking/primitives/model.dart, package:leak_tracker/src/leak_tracking/_baseliner.dart, package:leak_tracker/src/leak_tracking/_leak_reporter.dart, package:leak_tracker/src/leak_tracking/primitives/_gc_counter.dart, package:leak_tracker/src/leak_tracking/primitives/_test_helper_detector.dart, package:leak_tracker/src/leak_tracking/_object_record.dart, package:leak_tracker/src/leak_tracking/_leak_filter.dart, package:leak_tracker/src/leak_tracking/_object_record_set.dart, package:leak_tracker/src/leak_tracking/_object_records.dart, package:leak_tracker/src/leak_tracking/primitives/_finalizer.dart, package:leak_tracker/src/leak_tracking/_object_tracker.dart, package:leak_tracker/src/leak_tracking/_leak_tracker.dart, package:leak_tracker/src/leak_tracking/primitives/_dispatcher.dart, package:leak_tracker/src/leak_tracking/leak_tracking.dart, package:leak_tracker/leak_tracker.dart, package:leak_tracker_testing/src/matchers.dart, package:leak_tracker_testing/src/leak_testing.dart, package:leak_tracker_testing/leak_tracker_testing.dart, package:leak_tracker_flutter_testing/src/matchers.dart, package:leak_tracker_flutter_testing/src/model.dart, package:leak_tracker_flutter_testing/src/testing.dart, package:leak_tracker_flutter_testing/src/testing_for_testing/leaking_classes.dart, package:leak_tracker_flutter_testing/src/testing_for_testing/test_case.dart, package:leak_tracker_flutter_testing/src/testing_for_testing/test_settings.dart, package:leak_tracker_flutter_testing/leak_tracker_flutter_testing.dart, package:test_api/src/frontend/fake.dart, package:test_api/fake.dart, package:flutter_test/src/test_compat.dart, package:flutter_test/src/_binding_io.dart, package:flutter_test/src/_matchers_io.dart, package:flutter_test/src/accessibility.dart, package:flutter_test/src/binding.dart, package:flutter_test/src/controller.dart, package:flutter_test/src/event_simulation.dart, package:flutter_test/src/finders.dart, package:flutter_test/src/matchers.dart, package:flutter_test/src/test_default_binary_messenger.dart, package:flutter_test/src/test_text_input.dart, package:flutter_test/src/test_text_input_key_handler.dart, package:flutter_test/src/widget_tester.dart, package:flutter_test/src/_test_selector_io.dart, package:flutter_test/src/animation_sheet.dart, package:flutter_test/src/deprecated.dart, package:flutter_test/src/frame_timing_summarizer.dart, package:flutter_test/src/image.dart, package:flutter_test/src/recording_canvas.dart, package:flutter_test/src/mock_canvas.dart, package:flutter_test/src/nonconst.dart, package:flutter_test/src/test_vsync.dart, package:flutter_test/flutter_test.dart, package:integration_test/common.dart, package:integration_test/src/channel.dart, package:integration_test/src/_callback_io.dart, package:integration_test/src/callback.dart, package:integration_test/src/_extension_io.dart, package:integration_test/src/extension.dart, package:integration_test/src/vm_service_golden_client.dart, package:integration_test/integration_test.dart, package:frontend/presentation/widgets/enhanced_error_widget.dart, package:frontend/presentation/widgets/retry_widget.dart, package:drift/src/drift_dev_helper.dart, package:drift/src/runtime/executor/helpers/results.dart, package:drift/src/utils/synchronized.dart, package:drift/src/runtime/cancellation_zone.dart, package:drift/src/utils/single_transformer.dart, package:drift/src/runtime/utils.dart, package:drift/src/utils/async_map.dart, package:drift/src/utils/async.dart, package:sqlite3/src/constants.dart, package:sqlite3/src/functions.dart, package:sqlite3/src/result_set.dart, package:sqlite3/src/exception.dart, package:sqlite3/src/statement.dart, package:sqlite3/src/database.dart, package:typed_data/src/typed_buffer.dart, package:typed_data/typed_buffers.dart, package:sqlite3/src/vfs.dart, package:sqlite3/src/utils.dart, package:sqlite3/src/in_memory_vfs.dart, package:sqlite3/src/jsonb.dart, package:sqlite3/src/sqlite3.dart, package:sqlite3/common.dart, package:convert/src/accumulator_sink.dart, package:typed_data/src/typed_queue.dart, package:typed_data/typed_data.dart, package:convert/src/byte_accumulator_sink.dart, package:convert/src/codepage.dart, package:convert/src/fixed_datetime_formatter.dart, package:convert/src/charcodes.dart, package:convert/src/utils.dart, package:convert/src/hex/decoder.dart, package:convert/src/hex/encoder.dart, package:convert/src/hex.dart, package:convert/src/identity_codec.dart, package:convert/src/percent/decoder.dart, package:convert/src/percent/encoder.dart, package:convert/src/percent.dart, package:convert/src/string_accumulator_sink.dart, package:convert/convert.dart, package:drift/backends.dart, package:drift/drift.dart, package:drift/internal/versioned_schema.dart, package:drift/src/dsl/dsl.dart, package:drift/src/remote/protocol.dart, package:drift/src/runtime/api/options.dart, package:drift/src/runtime/api/runtime_api.dart, package:drift/src/runtime/custom_result_set.dart, package:drift/src/runtime/data_class.dart, package:drift/src/runtime/data_verification.dart, package:drift/src/runtime/devtools/devtools.dart, package:drift/src/runtime/devtools/service_extension.dart, package:drift/src/runtime/devtools/shared.dart, package:drift/src/runtime/exceptions.dart, package:drift/src/runtime/executor/connection_pool.dart, package:drift/src/runtime/executor/delayed_stream_queries.dart, package:drift/src/runtime/executor/executor.dart, package:drift/src/runtime/executor/helpers/delegates.dart, package:drift/src/runtime/executor/helpers/engines.dart, package:drift/src/runtime/executor/interceptor.dart, package:drift/src/runtime/executor/stream_queries.dart, package:drift/src/runtime/executor/transactions.dart, package:drift/src/runtime/manager/manager.dart, package:drift/src/runtime/query_builder/components/table_valued_function.dart, package:drift/src/runtime/query_builder/expressions/bitwise.dart, package:drift/src/runtime/query_builder/expressions/case_when.dart, package:drift/src/runtime/query_builder/expressions/internal.dart, package:drift/src/runtime/query_builder/helpers.dart, package:drift/src/runtime/query_builder/on_table.dart, package:drift/src/runtime/query_builder/query_builder.dart, package:drift/src/runtime/types/converters.dart, package:drift/src/runtime/types/mapping.dart, package:drift/src/utils/lazy_database.dart, package:drift/extensions/geopoly.dart]\nRoot children: [dart:async, dart:collection, dart:concurrent, dart:convert, dart:core, dart:developer, dart:ffi, dart:html, dart:html_common, dart:indexed_db, dart:_http, dart:io, dart:isolate, dart:js, dart:_js, dart:js_interop, dart:js_interop_unsafe, dart:js_util, dart:math, dart:mirrors, dart:nativewrappers, dart:typed_data, dart:_native_typed_data, dart:cli, dart:svg, dart:vmservice_io, dart:web_audio, dart:web_gl, dart:_internal, dart:_js_helper, dart:_late_helper, dart:_rti, dart:_dart2js_only, dart:_dart2js_runtime_metrics, dart:_interceptors, dart:_foreign_helper, dart:_js_names, dart:_js_primitives, dart:_js_embedded_names, dart:_js_shared_embedded_names, dart:_js_types, dart:_async_status_codes, dart:_invocation_mirror_constants, dart:_recipe_syntax, dart:_load_library_priority, dart:_metadata, dart:_js_annotations, dart:_wasm, dart:_macros, dart:ui, ... (1469 total)]\nReaders: [dart:async, dart:collection, dart:concurrent, dart:convert, dart:core, dart:developer, dart:ffi, dart:html, dart:html_common, dart:indexed_db, dart:_http, dart:io, dart:isolate, dart:js, dart:_js, dart:js_interop, dart:js_interop_unsafe, dart:js_util, dart:math, dart:mirrors, dart:nativewrappers, dart:typed_data, dart:_native_typed_data, dart:cli, dart:svg, dart:vmservice_io, dart:web_audio, dart:web_gl, dart:_internal, dart:_js_helper, dart:_late_helper, dart:_rti, dart:_dart2js_only, dart:_dart2js_runtime_metrics, dart:_interceptors, dart:_foreign_helper, dart:_js_names, dart:_js_primitives, dart:_js_embedded_names, dart:_js_shared_embedded_names, dart:_js_types, dart:_async_status_codes, dart:_invocation_mirror_constants, dart:_recipe_syntax, dart:_load_library_priority, dart:_metadata, dart:_js_annotations, dart:_wasm, dart:_macros, dart:ui, ... (92 total)]\nLog: [removeLibraries][uriSet: {}][#0      LinkedElementFactory.removeLibraries (package:analyzer/src/summary2/linked_element_factory.dart:262:67)\n#1      LibraryContext.remove (package:analyzer/src/dart/analysis/library_context.dart:301:20)\n#2      AnalysisDriver._removePotentiallyAffectedLibraries (package:analyzer/src/dart/analysis/driver.dart:2028:22)\n#3      AnalysisDriver._applyPendingFileChanges (package:analyzer/src/dart/analysis/driver.dart:1511:7)\n#4      AnalysisDriverScheduler._run (package:analyzer/src/dart/analysis/driver.dart:2402:16)\n<asynchronous suspension>\n]\n[removeLibraries][uriSet: {}][#0      LinkedElementFactory.removeLibraries (package:analyzer/src/summary2/linked_element_factory.dart:262:67)\n#1      LibraryContext.remove (package:analyzer/src/dart/analysis/library_context.dart:301:20)\n#2      AnalysisDriver._removePotentiallyAffectedLibraries (package:analyzer/src/dart/analysis/driver.dart:2028:22)\n#3      AnalysisDriver._applyPendingFileChanges (package:analyzer/src/dart/analysis/driver.dart:1511:7)\n#4      AnalysisDriverScheduler._run (package:analyzer/src/dart/analysis/driver.dart:2402:16)\n<asynchronous suspension>\n]\n[removeLibraries][uriSet: {}][#0      LinkedElementFactory.removeLibraries (package:analyzer/src/summary2/linked_element_factory.dart:262:67)\n#1      LibraryContext.remove (package:analyzer/src/dart/analysis/library_context.dart:301:20)\n#2      AnalysisDriver._removePotentiallyAffectedLibraries (package:analyzer/src/dart/analysis/driver.dart:2028:22)\n#3      AnalysisDriver._applyPendingFileChanges (package:analyzer/src/dart/analysis/driver.dart:1511:7)\n#4      AnalysisDriverScheduler._run (package:analyzer/src/dart/analysis/driver.dart:2402:16)\n<asynchronous suspension>\n]\n[removeLibraries][uriSet: {}][#0      LinkedElementFactory.removeLibraries (package:analyzer/src/summary2/linked_element_factory.dart:262:67)\n#1      LibraryContext.remove (package:analyzer/src/dart/analysis/library_context.dart:301:20)\n#2      AnalysisDriver._removePotentiallyAffectedLibraries (package:analyzer/src/dart/analysis/driver.dart:2028:22)\n#3      AnalysisDriver._applyPendingFileChanges (package:analyzer/src/dart/analysis/driver.dart:1511:7)\n#4      AnalysisDriverScheduler._run (package:analyzer/src/dart/analysis/driver.dart:2402:16)\n<asynchronous suspension>\n]\n[removeLibraries][uriSet: {}][#0      LinkedElementFactory.removeLibraries (package:analyzer/src/summary2/linked_element_factory.dart:262:67)\n#1      LibraryContext.remove (package:analyzer/src/dart/analysis/library_context.dart:301:20)\n#2      AnalysisDriver._removePotentiallyAffectedLibraries (package:analyzer/src/dart/analysis/driver.dart:2028:22)\n#3      AnalysisDriver._applyPendingFileChanges (package:analyzer/src/dart/analysis/driver.dart:1511:7)\n#4      AnalysisDriverScheduler._run (package:analyzer/src/dart/analysis/driver.dart:2402:16)\n<asynchronous suspension>\n]\n[removeLibraries][uriSet: {}][#0      LinkedElementFactory.removeLibraries (package:analyzer/src/summary2/linked_element_factory.dart:262:67)\n#1      LibraryContext.remove (package:analyzer/src/dart/analysis/library_context.dart:301:20)\n#2      AnalysisDriver._removePotentiallyAffectedLibraries (package:analyzer/src/dart/analysis/driver.dart:2028:22)\n#3      AnalysisDriver._applyPendingFileChanges (package:analyzer/src/dart/analysis/driver.dart:1511:7)\n#4      AnalysisDriverScheduler._run (package:analyzer/src/dart/analysis/driver.dart:2402:16)\n<asynchronous suspension>\n]\n[removeLibraries][uriSet: {}][#0      LinkedElementFactory.removeLibraries (package:analyzer/src/summary2/linked_element_factory.dart:262:67)\n#1      LibraryContext.remove (package:analyzer/src/dart/analysis/library_context.dart:301:20)\n#2      AnalysisDriver._removePotentiallyAffectedLibraries (package:analyzer/src/dart/analysis/driver.dart:2028:22)\n#3      AnalysisDriver._applyPendingFileChanges (package:analyzer/src/dart/analysis/driver.dart:1511:7)\n#4      AnalysisDriverScheduler._run (package:analyzer/src/dart/analysis/driver.dart:2402:16)\n<asynchronous suspension>\n]\n[removeLibraries][uriSet: {}][#0      LinkedElementFactory.removeLibraries (package:analyzer/src/summary2/linked_element_factory.dart:262:67)\n#1      LibraryContext.remove (package:analyzer/src/dart/analysis/library_context.dart:301:20)\n#2      AnalysisDriver._removePotentiallyAffectedLibraries (package:analyzer/src/dart/analysis/driver.dart:2028:22)\n#3      AnalysisDriver._applyPendingFileChanges (package:analyzer/src/dart/analysis/driver.dart:1511:7)\n#4      AnalysisDriverScheduler._run (package:analyzer/src/dart/analysis/driver.dart:2402:16)\n<asynchronous suspension>\n]\n[load][targetLibrary: package:drift/src/drift_dev_helper.dart = /drift/lib/src/drift_dev_helper.dart]\n[load][targetLibrary: package:drift/src/drift_dev_helper.dart = /drift/lib/src/drift_dev_helper.dart]\n", "#0      LinkedElementFactory.createLibraryElementForReading (package:analyzer/src/summary2/linked_element_factory.dart:153:7)\n#1      LinkedElementFactory.elementOfReference (package:analyzer/src/summary2/linked_element_factory.dart:212:14)\n#2      LinkedElementFactory.libraryOfUri (package:analyzer/src/summary2/linked_element_factory.dart:241:12)\n#3      LinkedElementFactory.libraryOfUri2 (package:analyzer/src/summary2/linked_element_factory.dart:245:19)\n#4      ResolutionReader.libraryOfUri (package:analyzer/src/summary2/bundle_reader.dart:1996:28)\n#5      CompilationUnitElementLinkedData._read (package:analyzer/src/summary2/bundle_reader.dart:196:30)\n#6      ElementLinkedData.read (package:analyzer/src/summary2/bundle_reader.dart:286:5)\n#7      CompilationUnitElementImpl.libraryExports (package:analyzer/src/dart/element/element.dart:957:17)\n#8      LibraryOrAugmentationElementImpl.libraryExports (package:analyzer/src/dart/element/element.dart:6389:36)\n#9      LibraryElementImpl.children (package:analyzer/src/dart/element/element.dart:5738:12)\n#10     ElementImpl.visitChildren (package:analyzer/src/dart/element/element.dart:3065:27)\n#11     RecursiveElementVisitor.visitLibraryElement (package:analyzer/dart/element/visitor.dart:289:13)\n#12     _FindDartElements.find (package:drift_dev/src/analysis/resolver/discover.dart:194:5)\n#13     DiscoverStep.discover (package:drift_dev/src/analysis/resolver/discover.dart:79:22)\n<asynchronous suspension>\n#14     DriftAnalysisDriver.discoverIfNecessary (package:drift_dev/src/analysis/driver/driver.dart:153:7)\n<asynchronous suspension>\n#15     DriftAnalysisDriver.findLocalElements (package:drift_dev/src/analysis/driver/driver.dart:190:7)\n<asynchronous suspension>\n#16     DriftDiscover.build (package:drift_dev/src/backends/build/analyzer.dart:36:22)\n<asynchronous suspension>\n#17     runBuilder.buildForInput (package:build/src/generate/run_builder.dart:83:7)\n<asynchronous suspension>\n#18     Future.wait.<anonymous closure> (dart:async/future.dart:525:21)\n<asynchronous suspension>\n#19     scopeLogAsync.<anonymous closure> (package:build/src/builder/logging.dart:32:40)\n<asynchronous suspension>\n"]]