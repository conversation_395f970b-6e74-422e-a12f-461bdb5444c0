import { Router } from 'express';
import { UserRole } from '@prisma/client';
import { prisma } from '@/lib/prisma';
import { AuthService } from '@/lib/auth';
import { 
  createUserSchema, 
  updateUserSchema,
  paginationSchema 
} from '@/lib/validation';
import { validateRequest, asyncHandler, AppError } from '@/middleware/errorHandler';
import { authenticate, authorize, requireAction } from '@/middleware/auth';
import { conditionalRateLimit } from '@/middleware/rateLimiter';
import { 
  CreateUserRequest, 
  UpdateUserRequest,
  UserWithPermissions,
  PaginatedResponse 
} from '@/types';

const router = Router();

// Apply authentication and rate limiting
router.use(authenticate);
router.use(conditionalRateLimit);

/**
 * @swagger
 * /users:
 *   get:
 *     tags: [Users]
 *     summary: List users
 *     description: Get paginated list of users (Admin only)
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *       - in: query
 *         name: role
 *         schema:
 *           type: string
 *           enum: [SUPER_ADMIN, PROPERTY_MANAGER, OFFICE_MANAGER, SECURITY_PERSONNEL, MAINTENANCE_STAFF, CONSTRUCTION_SUPERVISOR]
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Users retrieved successfully
 */
router.get('/',
  authorize([UserRole.SUPER_ADMIN], 'canManageUsers'),
  validateRequest(paginationSchema, 'query'),
  asyncHandler(async (req, res) => {
    const { page, limit, role, search } = req.query as any;

    // Build where clause
    let whereClause: any = {};
    if (role) whereClause.role = role;
    if (search) {
      whereClause.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Get total count
    const total = await prisma.user.count({ where: whereClause });

    // Get users with pagination
    const users = await prisma.user.findMany({
      where: whereClause,
      include: {
        assignedProperties: {
          include: {
            property: {
              select: {
                id: true,
                name: true,
                type: true,
              },
            },
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      skip: (page - 1) * limit,
      take: limit,
    });

    // Transform users to response format
    const usersResponse: UserWithPermissions[] = users.map(user => ({
      id: user.id,
      name: user.name,
      email: user.email,
      phone: user.phone,
      role: user.role,
      assignedProperties: user.assignedProperties.map(ap => ap.propertyId),
      isActive: user.isActive,
      avatar: user.avatar,
      timezone: user.timezone,
      language: user.language,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      lastLogin: user.lastLogin,
      permissions: AuthService.getUserPermissions(user.role),
    }));

    const totalPages = Math.ceil(total / limit);

    const response: PaginatedResponse<UserWithPermissions> = {
      data: usersResponse,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };

    res.json({
      success: true,
      data: response,
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /users:
 *   post:
 *     tags: [Users]
 *     summary: Create new user
 *     description: Create a new user (Admin only)
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateUserRequest'
 *     responses:
 *       201:
 *         description: User created successfully
 */
router.post('/',
  authorize([UserRole.SUPER_ADMIN], 'canManageUsers'),
  requireAction('create'),
  validateRequest(createUserSchema),
  asyncHandler(async (req, res) => {
    const userData: CreateUserRequest = req.body;
    const currentUser = req.user!;

    // Check if email already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: userData.email },
    });

    if (existingUser) {
      throw new AppError('Email already exists', 400, 'EMAIL_EXISTS');
    }

    // Generate password if not provided
    const password = userData.password || 'password123';
    const hashedPassword = await AuthService.hashPassword(password);

    // Create user
    const user = await prisma.user.create({
      data: {
        name: userData.name,
        email: userData.email,
        phone: userData.phone,
        password: hashedPassword,
        role: userData.role as UserRole,
        isActive: true,
        timezone: 'Asia/Kolkata',
        language: 'en',
      },
    });

    // Assign properties if provided
    if (userData.assignedProperties && userData.assignedProperties.length > 0) {
      await prisma.userProperty.createMany({
        data: userData.assignedProperties.map(propertyId => ({
          userId: user.id,
          propertyId,
        })),
      });
    }

    // Log activity
    await prisma.activity.create({
      data: {
        userId: currentUser.id,
        action: 'CREATE_USER',
        description: `Created user: ${user.name} (${user.email})`,
        metadata: {
          newUserId: user.id,
          role: user.role,
        },
        ipAddress: req.ip,
        userAgent: req.headers['user-agent'],
      },
    });

    // Prepare response (exclude password)
    const userResponse: UserWithPermissions = {
      id: user.id,
      name: user.name,
      email: user.email,
      phone: user.phone,
      role: user.role,
      assignedProperties: userData.assignedProperties || [],
      isActive: user.isActive,
      avatar: user.avatar,
      timezone: user.timezone,
      language: user.language,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      lastLogin: user.lastLogin,
      permissions: AuthService.getUserPermissions(user.role),
    };

    res.status(201).json({
      success: true,
      data: userResponse,
      message: 'User created successfully',
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /users/{userId}:
 *   get:
 *     tags: [Users]
 *     summary: Get user details
 *     description: Get specific user details (Admin only)
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: User retrieved successfully
 *       404:
 *         description: User not found
 */
router.get('/:userId',
  authorize([UserRole.SUPER_ADMIN], 'canManageUsers'),
  asyncHandler(async (req, res) => {
    const { userId } = req.params;

    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        assignedProperties: {
          include: {
            property: {
              select: {
                id: true,
                name: true,
                type: true,
              },
            },
          },
        },
      },
    });

    if (!user) {
      throw new AppError('User not found', 404, 'USER_NOT_FOUND');
    }

    const userResponse: UserWithPermissions = {
      id: user.id,
      name: user.name,
      email: user.email,
      phone: user.phone,
      role: user.role,
      assignedProperties: user.assignedProperties.map(ap => ap.propertyId),
      isActive: user.isActive,
      avatar: user.avatar,
      timezone: user.timezone,
      language: user.language,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      lastLogin: user.lastLogin,
      permissions: AuthService.getUserPermissions(user.role),
    };

    res.json({
      success: true,
      data: userResponse,
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /users/{userId}:
 *   put:
 *     tags: [Users]
 *     summary: Update user
 *     description: Update user information (Admin only)
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateUserRequest'
 *     responses:
 *       200:
 *         description: User updated successfully
 */
router.put('/:userId',
  authorize([UserRole.SUPER_ADMIN], 'canManageUsers'),
  requireAction('update'),
  validateRequest(updateUserSchema),
  asyncHandler(async (req, res) => {
    const { userId } = req.params;
    const updateData: UpdateUserRequest = req.body;
    const currentUser = req.user!;

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!existingUser) {
      throw new AppError('User not found', 404, 'USER_NOT_FOUND');
    }

    // Update user
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: updateData,
    });

    // Log activity
    await prisma.activity.create({
      data: {
        userId: currentUser.id,
        action: 'UPDATE_USER',
        description: `Updated user: ${updatedUser.name} (${updatedUser.email})`,
        metadata: {
          updatedUserId: userId,
          changes: updateData as any,
        },
        ipAddress: req.ip,
        userAgent: req.headers['user-agent'],
      },
    });

    const userResponse: UserWithPermissions = {
      id: updatedUser.id,
      name: updatedUser.name,
      email: updatedUser.email,
      phone: updatedUser.phone,
      role: updatedUser.role,
      assignedProperties: [], // Would need to fetch separately
      isActive: updatedUser.isActive,
      avatar: updatedUser.avatar,
      timezone: updatedUser.timezone,
      language: updatedUser.language,
      createdAt: updatedUser.createdAt,
      updatedAt: updatedUser.updatedAt,
      lastLogin: updatedUser.lastLogin,
      permissions: AuthService.getUserPermissions(updatedUser.role),
    };

    res.json({
      success: true,
      data: userResponse,
      message: 'User updated successfully',
      timestamp: new Date().toISOString(),
    });
  })
);

export default router;
