import 'package:json_annotation/json_annotation.dart';

part 'api_response.g.dart';

/// Generic API response wrapper
@JsonSerializable(genericArgumentFactories: true)
class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? message;
  final String? error;
  final String? timestamp;
  final String? path;
  final Map<String, dynamic>? metadata;

  const ApiResponse({
    required this.success,
    this.data,
    this.message,
    this.error,
    this.timestamp,
    this.path,
    this.metadata,
  });

  /// Create a successful response
  factory ApiResponse.success(T data, {String? message}) {
    return ApiResponse<T>(
      success: true,
      data: data,
      message: message,
      timestamp: DateTime.now().toIso8601String(),
    );
  }

  /// Create an error response
  factory ApiResponse.error(String error, {String? message, String? path}) {
    return ApiResponse<T>(
      success: false,
      error: error,
      message: message,
      path: path,
      timestamp: DateTime.now().toIso8601String(),
    );
  }

  /// Create a loading response
  factory ApiResponse.loading({String? message}) {
    return ApiResponse<T>(
      success: false,
      message: message ?? 'Loading...',
      timestamp: DateTime.now().toIso8601String(),
    );
  }

  /// Create from JSON with custom data parser
  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$ApiResponseFromJson(json, fromJsonT);

  /// Convert to JSON with custom data serializer
  Map<String, dynamic> toJson(Object? Function(T value) toJsonT) =>
      _$ApiResponseToJson(this, toJsonT);

  /// Check if response is successful and has data
  bool get isSuccess => success && data != null;

  /// Check if response is an error
  bool get isError => !success && error != null;

  /// Check if response is loading
  bool get isLoading => !success && error == null && data == null;

  /// Get error message or default
  String getErrorMessage([String defaultMessage = 'An error occurred']) {
    return error ?? message ?? defaultMessage;
  }

  /// Get success message or default
  String getSuccessMessage([String defaultMessage = 'Operation successful']) {
    return message ?? defaultMessage;
  }

  /// Transform the data type
  ApiResponse<R> transform<R>(R Function(T data) transformer) {
    if (isSuccess && data != null) {
      try {
        final transformedData = transformer(data!);
        return ApiResponse<R>.success(transformedData, message: message);
      } catch (e) {
        return ApiResponse<R>.error('Data transformation failed: $e');
      }
    } else {
      return ApiResponse<R>(
        success: success,
        message: message,
        error: error,
        timestamp: timestamp,
        path: path,
        metadata: metadata,
      );
    }
  }

  /// Map the data if successful
  ApiResponse<R> map<R>(R Function(T data) mapper) {
    return transform(mapper);
  }

  /// Handle the response with callbacks
  R when<R>({
    required R Function(T data) success,
    required R Function(String error) error,
    R Function()? loading,
  }) {
    if (isSuccess && data != null) {
      return success(data!);
    } else if (isError) {
      return error(getErrorMessage());
    } else {
      return loading?.call() ?? error('Unknown state');
    }
  }

  /// Copy with new values
  ApiResponse<T> copyWith({
    bool? success,
    T? data,
    String? message,
    String? error,
    String? timestamp,
    String? path,
    Map<String, dynamic>? metadata,
  }) {
    return ApiResponse<T>(
      success: success ?? this.success,
      data: data ?? this.data,
      message: message ?? this.message,
      error: error ?? this.error,
      timestamp: timestamp ?? this.timestamp,
      path: path ?? this.path,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ApiResponse<T> &&
        other.success == success &&
        other.data == data &&
        other.message == message &&
        other.error == error;
  }

  @override
  int get hashCode {
    return success.hashCode ^
        data.hashCode ^
        message.hashCode ^
        error.hashCode;
  }

  @override
  String toString() {
    return 'ApiResponse<$T>(success: $success, data: $data, message: $message, error: $error)';
  }
}

/// Paginated API response
@JsonSerializable(genericArgumentFactories: true)
class PaginatedApiResponse<T> extends ApiResponse<List<T>> {
  final int? page;
  final int? limit;
  final int? total;
  final int? totalPages;
  final bool? hasNext;
  final bool? hasPrevious;

  const PaginatedApiResponse({
    required bool success,
    List<T>? data,
    String? message,
    String? error,
    String? timestamp,
    String? path,
    Map<String, dynamic>? metadata,
    this.page,
    this.limit,
    this.total,
    this.totalPages,
    this.hasNext,
    this.hasPrevious,
  }) : super(
          success: success,
          data: data,
          message: message,
          error: error,
          timestamp: timestamp,
          path: path,
          metadata: metadata,
        );

  /// Create a successful paginated response
  factory PaginatedApiResponse.success(
    List<T> data, {
    String? message,
    int? page,
    int? limit,
    int? total,
    int? totalPages,
    bool? hasNext,
    bool? hasPrevious,
  }) {
    return PaginatedApiResponse<T>(
      success: true,
      data: data,
      message: message,
      timestamp: DateTime.now().toIso8601String(),
      page: page,
      limit: limit,
      total: total,
      totalPages: totalPages,
      hasNext: hasNext,
      hasPrevious: hasPrevious,
    );
  }

  /// Create an error paginated response
  factory PaginatedApiResponse.error(String error, {String? message, String? path}) {
    return PaginatedApiResponse<T>(
      success: false,
      error: error,
      message: message,
      path: path,
      timestamp: DateTime.now().toIso8601String(),
    );
  }

  /// Create from JSON with custom data parser
  factory PaginatedApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$PaginatedApiResponseFromJson(json, fromJsonT);

  /// Convert to JSON with custom data serializer
  Map<String, dynamic> toJson(Object? Function(T value) toJsonT) =>
      _$PaginatedApiResponseToJson(this, toJsonT);

  /// Get pagination info as a map
  Map<String, dynamic> get paginationInfo => {
        'page': page,
        'limit': limit,
        'total': total,
        'totalPages': totalPages,
        'hasNext': hasNext,
        'hasPrevious': hasPrevious,
      };

  @override
  String toString() {
    return 'PaginatedApiResponse<$T>(success: $success, data: ${data?.length} items, page: $page/$totalPages, total: $total)';
  }
}

/// API error details
@JsonSerializable()
class ApiError {
  final String code;
  final String message;
  final String? details;
  final String? field;
  final Map<String, dynamic>? metadata;

  const ApiError({
    required this.code,
    required this.message,
    this.details,
    this.field,
    this.metadata,
  });

  factory ApiError.fromJson(Map<String, dynamic> json) => _$ApiErrorFromJson(json);
  Map<String, dynamic> toJson() => _$ApiErrorToJson(this);

  @override
  String toString() {
    return 'ApiError(code: $code, message: $message, details: $details)';
  }
}
