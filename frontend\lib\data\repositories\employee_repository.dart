import '../models/employee.dart';
import '../models/api_response.dart';
import '../../core/network/api_client.dart';
import '../../core/constants/api_constants.dart';

class EmployeeRepository {
  final ApiClient _apiClient = ApiClient();

  /// Get employees with optional filters
  Future<ApiResponse<List<Employee>>> getEmployees({
    String? propertyId,
    String? search,
    String? department,
    String? status,
    int page = 1,
    int limit = 50,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (propertyId != null) queryParams['propertyId'] = propertyId;
      if (search != null) queryParams['search'] = search;
      if (department != null) queryParams['department'] = department;
      if (status != null) queryParams['status'] = status;

      final response = await _apiClient.get(
        ApiConstants.employees,
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        final List<dynamic> employeeList = response.data['data'] ?? [];
        final employees = employeeList.map((json) => Employee.fromJson(json)).toList();
        return ApiResponse.success(data: employees);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch employees: $e');
    }
  }

  /// Get a single employee by ID
  Future<ApiResponse<Employee>> getEmployee(String id) async {
    try {
      final response = await _apiClient.get(
        ApiConstants.employee.replaceAll('{id}', id),
      );

      if (response.isSuccess) {
        final employee = Employee.fromJson(response.data['data']);
        return ApiResponse.success(data: employee);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch employee: $e');
    }
  }

  /// Create a new employee
  Future<ApiResponse<Employee>> createEmployee({
    String? propertyId,
    required String name,
    required String email,
    String? phone,
    String? position,
    required String department,
    required String status,
    DateTime? hireDate,
    double? salary,
  }) async {
    try {
      final requestBody = <String, dynamic>{
        'name': name,
        'email': email,
        'department': department,
        'status': status,
      };

      if (propertyId != null) requestBody['propertyId'] = propertyId;
      if (phone != null) requestBody['phone'] = phone;
      if (position != null) requestBody['position'] = position;
      if (hireDate != null) requestBody['hireDate'] = hireDate.toIso8601String();
      if (salary != null) requestBody['salary'] = salary;

      final response = await _apiClient.post(
        ApiConstants.employees,
        data: requestBody,
      );

      if (response.isSuccess) {
        final employee = Employee.fromJson(response.data['data']);
        return ApiResponse.success(data: employee);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to create employee: $e');
    }
  }

  /// Update an existing employee
  Future<ApiResponse<Employee>> updateEmployee({
    required String id,
    String? name,
    String? email,
    String? phone,
    String? position,
    String? department,
    String? status,
    double? salary,
  }) async {
    try {
      final requestBody = <String, dynamic>{};
      
      if (name != null) requestBody['name'] = name;
      if (email != null) requestBody['email'] = email;
      if (phone != null) requestBody['phone'] = phone;
      if (position != null) requestBody['position'] = position;
      if (department != null) requestBody['department'] = department;
      if (status != null) requestBody['status'] = status;
      if (salary != null) requestBody['salary'] = salary;

      final response = await _apiClient.put(
        ApiConstants.employee.replaceAll('{id}', id),
        data: requestBody,
      );

      if (response.isSuccess) {
        final employee = Employee.fromJson(response.data['data']);
        return ApiResponse.success(data: employee);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to update employee: $e');
    }
  }

  /// Delete an employee
  Future<ApiResponse<bool>> deleteEmployee(String id) async {
    try {
      final response = await _apiClient.delete(
        ApiConstants.employee.replaceAll('{id}', id),
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: true);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to delete employee: $e');
    }
  }

  /// Get employee statistics
  Future<ApiResponse<Map<String, dynamic>>> getEmployeeStatistics({
    String? propertyId,
    String? department,
  }) async {
    try {
      final queryParams = <String, dynamic>{};

      if (propertyId != null) queryParams['propertyId'] = propertyId;
      if (department != null) queryParams['department'] = department;

      final response = await _apiClient.get(
        ApiConstants.employeeStats,
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data['data']);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch employee statistics: $e');
    }
  }

  /// Get employee attendance
  Future<ApiResponse<List<Map<String, dynamic>>>> getEmployeeAttendance({
    String? employeeId,
    String? propertyId,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{};

      if (employeeId != null) queryParams['employeeId'] = employeeId;
      if (propertyId != null) queryParams['propertyId'] = propertyId;
      if (fromDate != null) queryParams['fromDate'] = fromDate.toIso8601String();
      if (toDate != null) queryParams['toDate'] = toDate.toIso8601String();

      final response = await _apiClient.get(
        ApiConstants.employeeAttendance,
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        final List<dynamic> attendanceList = response.data['data'] ?? [];
        return ApiResponse.success(data: attendanceList.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch employee attendance: $e');
    }
  }

  /// Submit employee attendance
  Future<ApiResponse<bool>> submitAttendance({
    required String employeeId,
    String? propertyId,
    required DateTime date,
    required String status,
    String? notes,
  }) async {
    try {
      final requestBody = <String, dynamic>{
        'employeeId': employeeId,
        'date': date.toIso8601String(),
        'status': status,
      };

      if (propertyId != null) requestBody['propertyId'] = propertyId;
      if (notes != null) requestBody['notes'] = notes;

      final response = await _apiClient.post(
        ApiConstants.employeeAttendance,
        data: requestBody,
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: true);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to submit attendance: $e');
    }
  }
}
