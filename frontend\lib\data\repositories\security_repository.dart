import '../models/api_response.dart';
import '../models/security_system.dart';
import '../../core/services/api_client.dart';
import '../../core/services/service_locator.dart';
import '../../core/constants/api_constants.dart';

class SecurityRepository {
  final ApiClient _apiClient = serviceLocator.apiClient;

  Future<ApiResponse<List<SecuritySystem>>> getSecuritySystems({required String propertyId}) async {
    try {
      final endpoint = ApiConstants.securitySystems.replaceAll('{propertyId}', propertyId);
      final response = await _apiClient.get(endpoint);

      if (response.isSuccess) {
        final responseData = response.data['data'];
        final List<dynamic> systems = responseData['systems'] ?? [];
        final securitySystems = systems.map((json) => SecuritySystem.fromBackendJson(json)).toList();
        return ApiResponse.success(data: securitySystems);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch security systems: $e');
    }
  }


}
