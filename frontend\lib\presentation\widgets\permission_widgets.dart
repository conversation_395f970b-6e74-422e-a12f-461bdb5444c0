import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:provider/provider.dart' as provider;
import '../../core/providers/permission_provider.dart';
import '../../data/services/rbac_service.dart';
import '../providers/auth_provider.dart';

// Permission-aware screen wrapper
class PermissionScreen extends ConsumerWidget {
  final String screenName;
  final Widget child;
  final Widget? unauthorizedWidget;

  const PermissionScreen({
    super.key,
    required this.screenName,
    required this.child,
    this.unauthorizedWidget,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return PermissionBuilder(
      screen: screenName,
      builder: (context, hasPermission) {
        if (hasPermission) {
          return child;
        } else {
          return unauthorizedWidget ?? _buildUnauthorizedScreen(context);
        }
      },
    );
  }

  Widget _buildUnauthorizedScreen(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Access Denied'),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.lock_outline,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'Access Denied',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'You don\'t have permission to access this screen.',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

// Permission-aware tab wrapper
class PermissionTab extends ConsumerWidget {
  final String screenName;
  final String tabId;
  final Widget child;
  final Widget? restrictedWidget;
  final Widget? readOnlyWidget;

  const PermissionTab({
    super.key,
    required this.screenName,
    required this.tabId,
    required this.child,
    this.restrictedWidget,
    this.readOnlyWidget,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final uiPermissionsAsync = ref.watch(uiPermissionsProvider);

    return uiPermissionsAsync.when(
      data: (permissions) {
        final tabKey = '$screenName.$tabId';
        final tabPermission = permissions.tabs[tabKey];

        if (tabPermission == null || tabPermission.hasNoAccess) {
          return _buildNoAccessWidget();
        }

        if (tabPermission.isReadOnly) {
          return readOnlyWidget ?? _wrapWithReadOnlyIndicator(child);
        }

        if (tabPermission.isRestricted) {
          return restrictedWidget ?? _wrapWithRestrictedIndicator(child);
        }

        return child;
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => _buildErrorWidget(),
    );
  }

  Widget _wrapWithReadOnlyIndicator(Widget child) {
    return Stack(
      children: [
        child,
        Positioned(
          top: 8,
          right: 8,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.8),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Text(
              'Read Only',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _wrapWithRestrictedIndicator(Widget child) {
    return Stack(
      children: [
        child,
        Positioned(
          top: 8,
          right: 8,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.8),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Text(
              'Restricted',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNoAccessWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.block, size: 48, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'Access Denied',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          Text(
            'You don\'t have permission to access this tab.',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return const Center(
      child: Text(
        'Error loading permissions',
        style: TextStyle(color: Colors.red),
      ),
    );
  }
}

// Permission-aware card widget
class PermissionCard extends ConsumerWidget {
  final String cardId;
  final Widget child;
  final bool showPlaceholder;

  const PermissionCard({
    super.key,
    required this.cardId,
    required this.child,
    this.showPlaceholder = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return PermissionBuilder(
      widget: cardId,
      builder: (context, hasPermission) {
        if (hasPermission) {
          return child;
        } else if (showPlaceholder) {
          return _buildPlaceholder();
        } else {
          return const SizedBox.shrink();
        }
      },
    );
  }

  Widget _buildPlaceholder() {
    return Card(
      child: Container(
        height: 100,
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: Text(
            'Content not available',
            style: TextStyle(
              color: Colors.grey,
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      ),
    );
  }
}

// Permission-aware action button
class PermissionActionButton extends ConsumerWidget {
  final String action;
  final VoidCallback? onPressed;
  final Widget child;
  final PermissionContext? context;

  const PermissionActionButton({
    super.key,
    required this.action,
    required this.onPressed,
    required this.child,
    this.context,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return PermissionBuilder(
      permission: action,
      context: this.context,
      builder: (context, hasPermission) {
        if (hasPermission) {
          return ElevatedButton(
            onPressed: onPressed,
            child: child,
          );
        } else {
          return ElevatedButton(
            onPressed: null,
            child: child,
          );
        }
      },
    );
  }
}

// Permission-aware menu item
class PermissionMenuItem extends ConsumerWidget {
  final String permission;
  final Widget child;
  final VoidCallback? onTap;

  const PermissionMenuItem({
    super.key,
    required this.permission,
    required this.child,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return PermissionBuilder(
      permission: permission,
      builder: (context, hasPermission) {
        if (hasPermission) {
          return ListTile(
            onTap: onTap,
            title: child,
          );
        } else {
          return const SizedBox.shrink();
        }
      },
    );
  }
}

// Permission-aware data table
class PermissionDataTable extends ConsumerWidget {
  final List<DataColumn> columns;
  final List<DataRow> rows;
  final String? tablePermission;
  final List<String>? columnPermissions;

  const PermissionDataTable({
    super.key,
    required this.columns,
    required this.rows,
    this.tablePermission,
    this.columnPermissions,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final uiPermissionsAsync = ref.watch(uiPermissionsProvider);

    return uiPermissionsAsync.when(
      data: (permissions) {
        // Filter columns based on permissions
        List<DataColumn> filteredColumns = [];
        List<int> visibleColumnIndices = [];

        for (int i = 0; i < columns.length; i++) {
          bool showColumn = true;
          
          if (columnPermissions != null && i < columnPermissions!.length) {
            final columnPerm = columnPermissions![i];
            showColumn = permissions.actions.contains(columnPerm);
          }

          if (showColumn) {
            filteredColumns.add(columns[i]);
            visibleColumnIndices.add(i);
          }
        }

        // Filter row cells based on visible columns
        List<DataRow> filteredRows = rows.map((row) {
          List<DataCell> filteredCells = [];
          for (int i in visibleColumnIndices) {
            if (i < row.cells.length) {
              filteredCells.add(row.cells[i]);
            }
          }
          return DataRow(cells: filteredCells);
        }).toList();

        return DataTable(
          columns: filteredColumns,
          rows: filteredRows,
        );
      },
      loading: () => const CircularProgressIndicator(),
      error: (error, stack) => DataTable(
        columns: columns,
        rows: rows,
      ),
    );
  }
}

// Permission-aware navigation drawer
class PermissionDrawer extends ConsumerWidget {
  final List<DrawerItem> items;

  const PermissionDrawer({
    super.key,
    required this.items,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final uiPermissionsAsync = ref.watch(uiPermissionsProvider);

    return Drawer(
      child: uiPermissionsAsync.when(
        data: (permissions) {
          final filteredItems = items.where((item) {
            return item.permission == null || 
                   permissions.screens.contains(item.permission);
          }).toList();

          return ListView(
            padding: EdgeInsets.zero,
            children: [
              const DrawerHeader(
                decoration: BoxDecoration(
                  color: Colors.blue,
                ),
                child: Text(
                  'SRSR Property',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                  ),
                ),
              ),
              ...filteredItems.map((item) => ListTile(
                leading: item.icon,
                title: Text(item.title),
                onTap: item.onTap,
              )),
            ],
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => const Center(child: Text('Error loading menu')),
      ),
    );
  }
}

class DrawerItem {
  final String title;
  final Icon? icon;
  final String? permission;
  final VoidCallback? onTap;

  const DrawerItem({
    required this.title,
    this.icon,
    this.permission,
    this.onTap,
  });
}

// Permission-aware content filter
class PermissionContentFilter extends ConsumerWidget {
  final Map<String, dynamic> data;
  final List<String> sensitiveFields;
  final Widget Function(Map<String, dynamic> filteredData) builder;

  const PermissionContentFilter({
    super.key,
    required this.data,
    required this.sensitiveFields,
    required this.builder,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final uiPermissionsAsync = ref.watch(uiPermissionsProvider);

    return uiPermissionsAsync.when(
      data: (permissions) {
        final filteredData = Map<String, dynamic>.from(data);

        // Remove sensitive fields based on permissions
        for (final field in sensitiveFields) {
          final fieldPermission = permissions.widgets['field_$field'];
          if (fieldPermission?.visible != true) {
            filteredData.remove(field);
          }
        }

        return builder(filteredData);
      },
      loading: () => builder(data),
      error: (error, stack) => builder(data),
    );
  }
}

// Simple RBAC-based permission wrapper
class PermissionWrapper extends StatelessWidget {
  final String permission;
  final Widget child;
  final Widget? fallback;

  const PermissionWrapper({
    Key? key,
    required this.permission,
    required this.child,
    this.fallback,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return provider.Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        final user = authProvider.currentUser;
        final hasPermission = RbacService.hasPermission(user, permission);

        if (hasPermission) {
          return child;
        } else {
          return fallback ?? const SizedBox.shrink();
        }
      },
    );
  }
}

// Screen-level permission wrapper
class ScreenPermissionWrapper extends StatelessWidget {
  final String screenName;
  final Widget child;
  final Widget? unauthorizedWidget;

  const ScreenPermissionWrapper({
    Key? key,
    required this.screenName,
    required this.child,
    this.unauthorizedWidget,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return provider.Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        final user = authProvider.currentUser;
        final canAccess = RbacService.canAccessScreen(user, screenName);

        if (canAccess) {
          return child;
        } else {
          return unauthorizedWidget ?? _buildUnauthorizedScreen(context);
        }
      },
    );
  }

  Widget _buildUnauthorizedScreen(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Access Denied'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.lock_outline,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'Access Denied',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'You don\'t have permission to access this screen.',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
