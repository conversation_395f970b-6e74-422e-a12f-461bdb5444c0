import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../core/constants/api_constants.dart';
import '../../core/utils/api_response.dart';
import '../models/user_model.dart';

class RbacService {
  static const String _baseUrl = '${ApiConstants.apiBaseUrl}/rbac';

  // Get all available roles and their permissions
  Future<ApiResponse<Map<String, dynamic>>> getRoles() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/roles'),
        headers: ApiConstants.getHeaders(),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return ApiResponse.success(data['data']);
      } else {
        final error = json.decode(response.body);
        return ApiResponse.error(error['message'] ?? 'Failed to fetch roles');
      }
    } catch (e) {
      return ApiResponse.error('Network error: $e');
    }
  }

  // Get all available permissions in the system
  Future<ApiResponse<Map<String, dynamic>>> getPermissions() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/permissions'),
        headers: ApiConstants.getHeaders(),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return ApiResponse.success(data['data']);
      } else {
        final error = json.decode(response.body);
        return ApiResponse.error(error['message'] ?? 'Failed to fetch permissions');
      }
    } catch (e) {
      return ApiResponse.error('Network error: $e');
    }
  }

  // Get user's effective permissions
  Future<ApiResponse<Map<String, dynamic>>> getUserPermissions(String userId) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/user/$userId/permissions'),
        headers: ApiConstants.getHeaders(),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return ApiResponse.success(data['data']);
      } else {
        final error = json.decode(response.body);
        return ApiResponse.error(error['message'] ?? 'Failed to fetch user permissions');
      }
    } catch (e) {
      return ApiResponse.error('Network error: $e');
    }
  }

  // Update user's role
  Future<ApiResponse<UserModel>> updateUserRole(String userId, UserRole role) async {
    try {
      final response = await http.put(
        Uri.parse('$_baseUrl/user/$userId/role'),
        headers: ApiConstants.getHeaders(),
        body: json.encode({
          'role': role.toString().split('.').last.toUpperCase(),
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return ApiResponse.success(UserModel.fromJson(data['data']));
      } else {
        final error = json.decode(response.body);
        return ApiResponse.error(error['message'] ?? 'Failed to update user role');
      }
    } catch (e) {
      return ApiResponse.error('Network error: $e');
    }
  }

  // Check if user has specific permission
  static bool hasPermission(UserModel? user, String permission) {
    if (user == null) return false;
    
    // Super admin has all permissions
    if (user.role == UserRole.superAdmin) return true;
    
    final rolePermissions = _getRolePermissions(user.role);
    
    // Check for wildcard permissions
    if (rolePermissions.contains('*')) return true;
    
    // Check for exact permission match
    if (rolePermissions.contains(permission)) return true;
    
    // Check for wildcard resource permissions (e.g., 'employees.*')
    final parts = permission.split('.');
    if (parts.length >= 2) {
      final resourceWildcard = '${parts[0]}.*';
      if (rolePermissions.contains(resourceWildcard)) return true;
    }
    
    return false;
  }

  // Check if user can access specific screen
  static bool canAccessScreen(UserModel? user, String screenName) {
    if (user == null) return false;
    
    // Super admin can access all screens
    if (user.role == UserRole.superAdmin) return true;
    
    final allowedScreens = _getRoleScreens(user.role);
    return allowedScreens.contains('*') || allowedScreens.contains(screenName);
  }

  // Get role-based permissions
  static List<String> _getRolePermissions(UserRole role) {
    switch (role) {
      case UserRole.superAdmin:
        return ['*'];
      case UserRole.propertyManager:
        return [
          'employees.*', 'maintenance.*', 'systems.*', 'dashboard.*',
          'alerts.*', 'properties.*', 'reports.view'
        ];
      case UserRole.officeManager:
        return [
          'employees.view', 'employees.create', 'employees.update',
          'employees.attendance.*', 'dashboard.view', 'reports.view'
        ];
      case UserRole.securityPersonnel:
        return [
          'systems.view', 'security.*', 'alerts.*', 'dashboard.view'
        ];
      case UserRole.maintenanceStaff:
        return [
          'maintenance.*', 'systems.view', 'dashboard.view'
        ];
      case UserRole.constructionSupervisor:
        return [
          'employees.view', 'employees.attendance.view', 'systems.view',
          'dashboard.view', 'reports.view'
        ];
    }
  }

  // Get role-based screen access
  static List<String> _getRoleScreens(UserRole role) {
    switch (role) {
      case UserRole.superAdmin:
        return ['*'];
      case UserRole.propertyManager:
        return [
          'dashboard', 'employees', 'maintenance', 'systems',
          'properties', 'alerts', 'reports', 'admin'
        ];
      case UserRole.officeManager:
        return ['dashboard', 'employees', 'reports'];
      case UserRole.securityPersonnel:
        return ['dashboard', 'systems', 'alerts'];
      case UserRole.maintenanceStaff:
        return ['dashboard', 'maintenance', 'systems'];
      case UserRole.constructionSupervisor:
        return ['dashboard', 'employees', 'systems', 'reports'];
    }
  }

  // Get user-friendly role name
  static String getRoleDisplayName(UserRole role) {
    switch (role) {
      case UserRole.superAdmin:
        return 'Super Administrator';
      case UserRole.propertyManager:
        return 'Property Manager';
      case UserRole.officeManager:
        return 'Office Manager';
      case UserRole.securityPersonnel:
        return 'Security Personnel';
      case UserRole.maintenanceStaff:
        return 'Maintenance Staff';
      case UserRole.constructionSupervisor:
        return 'Construction Supervisor';
    }
  }

  // Get role description
  static String getRoleDescription(UserRole role) {
    switch (role) {
      case UserRole.superAdmin:
        return 'Full system access with all permissions';
      case UserRole.propertyManager:
        return 'Manages properties, employees, and systems';
      case UserRole.officeManager:
        return 'Manages office operations and employee attendance';
      case UserRole.securityPersonnel:
        return 'Monitors security systems and manages alerts';
      case UserRole.maintenanceStaff:
        return 'Handles maintenance requests and system upkeep';
      case UserRole.constructionSupervisor:
        return 'Oversees construction projects and worker attendance';
    }
  }

  // Get role color for UI
  static String getRoleColor(UserRole role) {
    switch (role) {
      case UserRole.superAdmin:
        return '#F44336'; // Red
      case UserRole.propertyManager:
        return '#2196F3'; // Blue
      case UserRole.officeManager:
        return '#4CAF50'; // Green
      case UserRole.securityPersonnel:
        return '#FF9800'; // Orange
      case UserRole.maintenanceStaff:
        return '#9C27B0'; // Purple
      case UserRole.constructionSupervisor:
        return '#009688'; // Teal
    }
  }

  // Check if user can perform action on resource
  static bool canPerformAction(UserModel? user, String resource, String action) {
    return hasPermission(user, '$resource.$action');
  }

  // Get all available roles for dropdown/selection
  static List<UserRole> getAvailableRoles() {
    return UserRole.values;
  }

  // Check if current user can manage other user's role
  static bool canManageUserRole(UserModel? currentUser, UserModel? targetUser) {
    if (currentUser == null || targetUser == null) return false;
    
    // Super admin can manage all roles
    if (currentUser.role == UserRole.superAdmin) return true;
    
    // Property manager can manage non-admin roles
    if (currentUser.role == UserRole.propertyManager) {
      return targetUser.role != UserRole.superAdmin;
    }
    
    // Others cannot manage roles
    return false;
  }

  // Get filtered navigation items based on user role
  static List<String> getAccessibleScreens(UserModel? user) {
    if (user == null) return [];
    return _getRoleScreens(user.role);
  }

  // Check if user has property access
  static bool hasPropertyAccess(UserModel? user, String propertyId) {
    if (user == null) return false;
    
    // Super admin has access to all properties
    if (user.role == UserRole.superAdmin) return true;
    
    // Check if user is assigned to this property
    return user.assignedProperties?.contains(propertyId) ?? false;
  }
}
