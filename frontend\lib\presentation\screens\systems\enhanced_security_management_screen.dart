import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/providers/permission_provider.dart';
import '../../widgets/permission_widgets.dart';

// Enhanced Security Management Screen with Granular Tab Permissions
class EnhancedSecurityManagementScreen extends ConsumerStatefulWidget {
  final String propertyId;

  const EnhancedSecurityManagementScreen({
    super.key,
    required this.propertyId,
  });

  @override
  ConsumerState<EnhancedSecurityManagementScreen> createState() =>
      _EnhancedSecurityManagementScreenState();
}

class _EnhancedSecurityManagementScreenState
    extends ConsumerState<EnhancedSecurityManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  final List<TabInfo> _tabs = [
    TabInfo(id: 'overview', name: 'Overview', icon: Icons.dashboard),
    TabInfo(id: 'cctv', name: 'CCTV', icon: Icons.videocam),
    TabInfo(id: 'access_control', name: 'Access Control', icon: Icons.security),
    TabInfo(id: 'maintenance', name: 'Maintenance', icon: Icons.build),
    TabInfo(id: 'contacts', name: 'Contacts', icon: Icons.contacts),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PermissionScreen(
      screenName: 'security_management',
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Security Management'),
          bottom: _buildTabBar(),
        ),
        body: TabBarView(
          controller: _tabController,
          children: _tabs.map((tab) => _buildTabContent(tab)).toList(),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildTabBar() {
    final uiPermissionsAsync = ref.watch(uiPermissionsProvider);

    return uiPermissionsAsync.when(
      data: (permissions) {
        // Filter tabs based on permissions
        final visibleTabs = _tabs.where((tab) {
          final tabKey = 'security_management.${tab.id}';
          final tabPermission = permissions.tabs[tabKey];
          return tabPermission?.visible ?? false;
        }).toList();

        return TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: visibleTabs.map((tab) {
            final tabKey = 'security_management.${tab.id}';
            final tabPermission = permissions.tabs[tabKey];
            
            return Tab(
              icon: Stack(
                children: [
                  Icon(tab.icon),
                  if (tabPermission?.isReadOnly ?? false)
                    Positioned(
                      right: 0,
                      top: 0,
                      child: Container(
                        width: 8,
                        height: 8,
                        decoration: const BoxDecoration(
                          color: Colors.orange,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                  if (tabPermission?.isRestricted ?? false)
                    Positioned(
                      right: 0,
                      top: 0,
                      child: Container(
                        width: 8,
                        height: 8,
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                ],
              ),
              text: tab.name,
            );
          }).toList(),
        );
      },
      loading: () => const TabBar(tabs: [Tab(text: 'Loading...')]),
      error: (error, stack) => const TabBar(tabs: [Tab(text: 'Error')]),
    );
  }

  Widget _buildTabContent(TabInfo tab) {
    return PermissionTab(
      screenName: 'security_management',
      tabId: tab.id,
      readOnlyWidget: _getReadOnlyTabContent(tab.id),
      restrictedWidget: _getRestrictedTabContent(tab.id),
      child: _getTabContent(tab.id),
    );
  }

  Widget _getTabContent(String tabId) {
    switch (tabId) {
      case 'overview':
        return _buildOverviewTab();
      case 'cctv':
        return _buildCCTVTab();
      case 'access_control':
        return _buildAccessControlTab();
      case 'maintenance':
        return _buildMaintenanceTab();
      case 'contacts':
        return _buildContactsTab();
      default:
        return const Center(child: Text('Tab not found'));
    }
  }

  Widget _getReadOnlyTabContent(String tabId) {
    // Return read-only version of the tab content
    switch (tabId) {
      case 'cctv':
        return _buildCCTVTabReadOnly();
      case 'access_control':
        return _buildAccessControlTabReadOnly();
      default:
        return _getTabContent(tabId);
    }
  }

  Widget _getRestrictedTabContent(String tabId) {
    // Return restricted version of the tab content
    switch (tabId) {
      case 'cctv':
        return _buildCCTVTabRestricted();
      case 'access_control':
        return _buildAccessControlTabRestricted();
      default:
        return _getTabContent(tabId);
    }
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Permission-aware cards
          PermissionCard(
            cardId: 'security_management.overview.system_status',
            child: _buildSystemStatusCard(),
          ),
          const SizedBox(height: 16),
          PermissionCard(
            cardId: 'security_management.overview.recent_alerts',
            child: _buildRecentAlertsCard(),
          ),
          const SizedBox(height: 16),
          PermissionCard(
            cardId: 'security_management.overview.financial_summary',
            child: _buildFinancialSummaryCard(),
          ),
        ],
      ),
    );
  }

  Widget _buildCCTVTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Full access CCTV controls
          _buildCameraGrid(),
          const SizedBox(height: 16),
          _buildCameraControls(),
          const SizedBox(height: 16),
          _buildRecordingSettings(),
        ],
      ),
    );
  }

  Widget _buildCCTVTabReadOnly() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Read-only CCTV view
          _buildCameraGridReadOnly(),
          const SizedBox(height: 16),
          const Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Text(
                'Camera controls are disabled in read-only mode',
                style: TextStyle(color: Colors.orange),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCCTVTabRestricted() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Restricted CCTV view - only live feeds, no recordings
          _buildCameraGridLiveOnly(),
          const SizedBox(height: 16),
          _buildBasicCameraInfo(),
          const SizedBox(height: 16),
          const Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Text(
                'Limited access: Live feeds only. Recording access restricted.',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccessControlTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildAccessControlSystem(),
          const SizedBox(height: 16),
          _buildUserManagement(),
          const SizedBox(height: 16),
          _buildAccessLogs(),
        ],
      ),
    );
  }

  Widget _buildAccessControlTabReadOnly() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildAccessControlSystemReadOnly(),
          const SizedBox(height: 16),
          _buildAccessLogsReadOnly(),
        ],
      ),
    );
  }

  Widget _buildAccessControlTabRestricted() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildEmergencyControls(),
          const SizedBox(height: 16),
          _buildLimitedAccessLogs(),
        ],
      ),
    );
  }

  Widget _buildMaintenanceTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildMaintenanceSchedule(),
          const SizedBox(height: 16),
          _buildMaintenanceHistory(),
          const SizedBox(height: 16),
          _buildVendorContacts(),
        ],
      ),
    );
  }

  Widget _buildContactsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildEmergencyContacts(),
          const SizedBox(height: 16),
          _buildVendorContacts(),
          const SizedBox(height: 16),
          _buildServiceProviders(),
        ],
      ),
    );
  }

  // Helper methods for building specific components
  Widget _buildSystemStatusCard() {
    return const Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('System Status', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            SizedBox(height: 8),
            Text('CCTV: Online (12/12 cameras)'),
            Text('Access Control: Online'),
            Text('Alarms: Armed'),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentAlertsCard() {
    return const Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Recent Alerts', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            SizedBox(height: 8),
            Text('No recent alerts'),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialSummaryCard() {
    return const Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Financial Summary', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            SizedBox(height: 8),
            Text('Monthly Cost: ₹15,000'),
            Text('Annual Contract: ₹1,80,000'),
          ],
        ),
      ),
    );
  }

  // Placeholder methods for other components
  Widget _buildCameraGrid() => const Card(child: Padding(padding: EdgeInsets.all(16), child: Text('Full Camera Grid')));
  Widget _buildCameraGridReadOnly() => const Card(child: Padding(padding: EdgeInsets.all(16), child: Text('Read-Only Camera Grid')));
  Widget _buildCameraGridLiveOnly() => const Card(child: Padding(padding: EdgeInsets.all(16), child: Text('Live Feeds Only')));
  Widget _buildCameraControls() => const Card(child: Padding(padding: EdgeInsets.all(16), child: Text('Camera Controls')));
  Widget _buildRecordingSettings() => const Card(child: Padding(padding: EdgeInsets.all(16), child: Text('Recording Settings')));
  Widget _buildBasicCameraInfo() => const Card(child: Padding(padding: EdgeInsets.all(16), child: Text('Basic Camera Info')));
  Widget _buildAccessControlSystem() => const Card(child: Padding(padding: EdgeInsets.all(16), child: Text('Access Control System')));
  Widget _buildAccessControlSystemReadOnly() => const Card(child: Padding(padding: EdgeInsets.all(16), child: Text('Access Control (Read-Only)')));
  Widget _buildUserManagement() => const Card(child: Padding(padding: EdgeInsets.all(16), child: Text('User Management')));
  Widget _buildAccessLogs() => const Card(child: Padding(padding: EdgeInsets.all(16), child: Text('Access Logs')));
  Widget _buildAccessLogsReadOnly() => const Card(child: Padding(padding: EdgeInsets.all(16), child: Text('Access Logs (Read-Only)')));
  Widget _buildEmergencyControls() => const Card(child: Padding(padding: EdgeInsets.all(16), child: Text('Emergency Controls')));
  Widget _buildLimitedAccessLogs() => const Card(child: Padding(padding: EdgeInsets.all(16), child: Text('Limited Access Logs')));
  Widget _buildMaintenanceSchedule() => const Card(child: Padding(padding: EdgeInsets.all(16), child: Text('Maintenance Schedule')));
  Widget _buildMaintenanceHistory() => const Card(child: Padding(padding: EdgeInsets.all(16), child: Text('Maintenance History')));
  Widget _buildEmergencyContacts() => const Card(child: Padding(padding: EdgeInsets.all(16), child: Text('Emergency Contacts')));
  Widget _buildVendorContacts() => const Card(child: Padding(padding: EdgeInsets.all(16), child: Text('Vendor Contacts')));
  Widget _buildServiceProviders() => const Card(child: Padding(padding: EdgeInsets.all(16), child: Text('Service Providers')));
}

class TabInfo {
  final String id;
  final String name;
  final IconData icon;

  const TabInfo({
    required this.id,
    required this.name,
    required this.icon,
  });
}
