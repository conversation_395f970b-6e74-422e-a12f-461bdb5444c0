import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../services/service_locator.dart';
import '../services/connectivity_manager.dart';

// Connectivity Manager Provider
final connectivityManagerProvider = Provider<ConnectivityManager>((ref) {
  return serviceLocator.connectivityManager;
});

// Connectivity Status Provider
final connectivityStatusProvider = StreamProvider<ConnectivityStatus>((ref) {
  final connectivityManager = ref.read(connectivityManagerProvider);
  return connectivityManager.statusStream;
});

// Current Connectivity Provider
final currentConnectivityProvider = FutureProvider<List<ConnectivityResult>>((ref) async {
  final connectivityManager = ref.read(connectivityManagerProvider);
  return await connectivityManager.getCurrentConnectivity();
});

// Connection Status Providers
final isOnlineProvider = Provider<bool>((ref) {
  final connectivityManager = ref.read(connectivityManagerProvider);
  return connectivityManager.isOnline;
});

final isOfflineProvider = Provider<bool>((ref) {
  final connectivityManager = ref.read(connectivityManagerProvider);
  return connectivityManager.isOffline;
});

final connectionTypeProvider = Provider<String>((ref) {
  final connectivityManager = ref.read(connectivityManagerProvider);
  return connectivityManager.connectionType;
});

// Connectivity Operations Provider
class ConnectivityNotifier extends StateNotifier<AsyncValue<bool>> {
  final ConnectivityManager _connectivityManager;

  ConnectivityNotifier(this._connectivityManager) : super(const AsyncValue.data(false)) {
    _init();
  }

  void _init() async {
    try {
      final isOnline = await _connectivityManager.checkConnectivity();
      state = AsyncValue.data(isOnline);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<bool> checkConnectivity() async {
    state = const AsyncValue.loading();
    try {
      final isOnline = await _connectivityManager.checkConnectivity();
      state = AsyncValue.data(isOnline);
      return isOnline;
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      return false;
    }
  }

  Future<void> waitForConnection({Duration timeout = const Duration(seconds: 30)}) async {
    try {
      await _connectivityManager.waitForConnection(timeout: timeout);
      state = const AsyncValue.data(true);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<T> retryWhenOnline<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 2),
  }) async {
    return await _connectivityManager.retryWhenOnline(
      operation,
      maxRetries: maxRetries,
      retryDelay: retryDelay,
    );
  }
}

final connectivityNotifierProvider = StateNotifierProvider<ConnectivityNotifier, AsyncValue<bool>>((ref) {
  final connectivityManager = ref.read(connectivityManagerProvider);
  return ConnectivityNotifier(connectivityManager);
});

// Utility providers for connectivity checks
final hasConnectionProvider = Provider.family<bool, List<ConnectivityResult>>((ref, results) {
  final connectivityManager = ref.read(connectivityManagerProvider);
  return connectivityManager.hasConnection(results);
});

final hasWifiConnectionProvider = Provider.family<bool, List<ConnectivityResult>>((ref, results) {
  final connectivityManager = ref.read(connectivityManagerProvider);
  return connectivityManager.hasWifiConnection(results);
});

final hasMobileConnectionProvider = Provider.family<bool, List<ConnectivityResult>>((ref, results) {
  final connectivityManager = ref.read(connectivityManagerProvider);
  return connectivityManager.hasMobileConnection(results);
});

final hasEthernetConnectionProvider = Provider.family<bool, List<ConnectivityResult>>((ref, results) {
  final connectivityManager = ref.read(connectivityManagerProvider);
  return connectivityManager.hasEthernetConnection(results);
});

// Network-aware operation provider
final networkAwareOperationProvider = Provider.family<Future<dynamic> Function(), Map<String, dynamic>>((ref, params) {
  final connectivityNotifier = ref.read(connectivityNotifierProvider.notifier);
  final operation = params['operation'] as Future<dynamic> Function();
  final maxRetries = params['maxRetries'] as int? ?? 3;
  final retryDelay = params['retryDelay'] as Duration? ?? const Duration(seconds: 2);

  return () => connectivityNotifier.retryWhenOnline<dynamic>(
    operation,
    maxRetries: maxRetries,
    retryDelay: retryDelay,
  );
});
