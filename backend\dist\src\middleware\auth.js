"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.authenticateToken = exports.checkPermission = exports.enhancedAuthorize = exports.optionalAuth = exports.requireAction = exports.requirePropertyAccess = exports.authorize = exports.authenticate = void 0;
const client_1 = require("@prisma/client");
const auth_1 = require("@/lib/auth");
const prisma_1 = require("@/lib/prisma");
const authenticate = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            res.status(401).json({
                success: false,
                error: 'UNAUTHORIZED',
                message: 'Authentication required',
                timestamp: new Date().toISOString(),
                path: req.path,
            });
            return;
        }
        const token = authHeader.substring(7);
        try {
            const payload = auth_1.AuthService.verifyAccessToken(token);
            // Verify user still exists and is active
            const user = await prisma_1.prisma.user.findFirst({
                where: {
                    id: payload.userId,
                    isActive: true,
                },
                include: {
                    assignedProperties: {
                        select: {
                            propertyId: true,
                        },
                    },
                },
            });
            if (!user) {
                res.status(401).json({
                    success: false,
                    error: 'UNAUTHORIZED',
                    message: 'Invalid or expired token',
                    timestamp: new Date().toISOString(),
                    path: req.path,
                });
                return;
            }
            // Attach user to request
            req.user = {
                id: user.id,
                email: user.email,
                role: user.role,
                assignedProperties: user.assignedProperties.map(ap => ap.propertyId),
            };
            next();
        }
        catch (jwtError) {
            res.status(401).json({
                success: false,
                error: 'UNAUTHORIZED',
                message: 'Invalid or expired token',
                timestamp: new Date().toISOString(),
                path: req.path,
            });
            return;
        }
    }
    catch (error) {
        console.error('Authentication error:', error);
        res.status(500).json({
            success: false,
            error: 'INTERNAL_SERVER_ERROR',
            message: 'Authentication service error',
            timestamp: new Date().toISOString(),
            path: req.path,
        });
    }
};
exports.authenticate = authenticate;
const authorize = (requiredRoles = [], requiredPermission) => {
    return (req, res, next) => {
        const user = req.user;
        if (!user) {
            res.status(401).json({
                success: false,
                error: 'UNAUTHORIZED',
                message: 'Authentication required',
                timestamp: new Date().toISOString(),
                path: req.path,
            });
            return;
        }
        // Check role-based access
        if (requiredRoles.length > 0 && !requiredRoles.includes(user.role)) {
            res.status(403).json({
                success: false,
                error: 'FORBIDDEN',
                message: 'Insufficient permissions for this operation',
                timestamp: new Date().toISOString(),
                path: req.path,
            });
            return;
        }
        // Check permission-based access
        if (requiredPermission) {
            const hasPermission = auth_1.AuthService.hasPermission(user.role, requiredPermission);
            if (!hasPermission) {
                res.status(403).json({
                    success: false,
                    error: 'FORBIDDEN',
                    message: 'Insufficient permissions for this operation',
                    timestamp: new Date().toISOString(),
                    path: req.path,
                });
                return;
            }
        }
        next();
    };
};
exports.authorize = authorize;
const requirePropertyAccess = (propertyIdParam = 'propertyId') => {
    return (req, res, next) => {
        const user = req.user;
        const propertyId = req.params[propertyIdParam];
        if (!user) {
            res.status(401).json({
                success: false,
                error: 'UNAUTHORIZED',
                message: 'Authentication required',
                timestamp: new Date().toISOString(),
                path: req.path,
            });
            return;
        }
        // Super admins have access to all properties
        if (user.role === client_1.UserRole.SUPER_ADMIN) {
            next();
            return;
        }
        // Check if user has access to the specific property
        if (!user.assignedProperties.includes(propertyId)) {
            res.status(403).json({
                success: false,
                error: 'FORBIDDEN',
                message: 'Access denied to this property',
                timestamp: new Date().toISOString(),
                path: req.path,
            });
            return;
        }
        next();
    };
};
exports.requirePropertyAccess = requirePropertyAccess;
const requireAction = (action) => {
    return (req, res, next) => {
        const user = req.user;
        if (!user) {
            res.status(401).json({
                success: false,
                error: 'UNAUTHORIZED',
                message: 'Authentication required',
                timestamp: new Date().toISOString(),
                path: req.path,
            });
            return;
        }
        const canPerformAction = auth_1.AuthService.canPerformAction(user.role, action);
        if (!canPerformAction) {
            res.status(403).json({
                success: false,
                error: 'FORBIDDEN',
                message: `Insufficient permissions to ${action} this resource`,
                timestamp: new Date().toISOString(),
                path: req.path,
            });
            return;
        }
        next();
    };
};
exports.requireAction = requireAction;
const optionalAuth = async (req, res, next) => {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        next();
        return;
    }
    try {
        const token = authHeader.substring(7);
        const payload = auth_1.AuthService.verifyAccessToken(token);
        const user = await prisma_1.prisma.user.findFirst({
            where: {
                id: payload.userId,
                isActive: true,
            },
            include: {
                assignedProperties: {
                    select: {
                        propertyId: true,
                    },
                },
            },
        });
        if (user) {
            req.user = {
                id: user.id,
                email: user.email,
                role: user.role,
                assignedProperties: user.assignedProperties.map(ap => ap.propertyId),
            };
        }
    }
    catch (error) {
        // Ignore authentication errors for optional auth
        console.warn('Optional authentication failed:', error);
    }
    next();
};
exports.optionalAuth = optionalAuth;
// Enhanced authorization with RBAC/UBAC
const enhancedAuthorize = (resource, action, options = {}) => {
    return async (req, res, next) => {
        try {
            const user = req.user;
            if (!user) {
                res.status(401).json({
                    success: false,
                    error: 'UNAUTHORIZED',
                    message: 'Authentication required',
                    timestamp: new Date().toISOString(),
                    path: req.path,
                });
                return;
            }
            // Build permission context
            const context = {
                userId: user.id,
                role: user.role,
                propertyId: req.params.propertyId || req.body.propertyId,
                officeId: req.params.officeId || req.body.officeId,
                resourceId: req.params.id || req.params.resourceId,
                timestamp: new Date(),
                ipAddress: req.ip || req.connection.remoteAddress,
                userAgent: req.get('User-Agent')
            };
            // Simple permission check based on role and action
            const hasPermission = checkResourcePermission(user.role, resource, action);
            if (!hasPermission) {
                res.status(403).json({
                    success: false,
                    error: 'FORBIDDEN',
                    message: `Access denied for ${action} on ${resource}`,
                    timestamp: new Date().toISOString(),
                    path: req.path,
                });
                return;
            }
            // Additional property access check
            if (options.requirePropertyAccess && context.propertyId) {
                if (user.role !== client_1.UserRole.SUPER_ADMIN &&
                    !user.assignedProperties.includes(context.propertyId)) {
                    res.status(403).json({
                        success: false,
                        error: 'FORBIDDEN',
                        message: 'Access denied to this property',
                        timestamp: new Date().toISOString(),
                        path: req.path,
                    });
                    return;
                }
            }
            // Store permission context for use in route handlers
            req.permissionContext = context;
            next();
        }
        catch (error) {
            console.error('Enhanced authorization error:', error);
            res.status(500).json({
                success: false,
                error: 'INTERNAL_SERVER_ERROR',
                message: 'Authorization service error',
                timestamp: new Date().toISOString(),
                path: req.path,
            });
        }
    };
};
exports.enhancedAuthorize = enhancedAuthorize;
// Simple permission checker
function checkResourcePermission(role, resource, action) {
    const permissions = {
        [client_1.UserRole.SUPER_ADMIN]: ['*'], // All permissions
        [client_1.UserRole.PROPERTY_MANAGER]: [
            'employees.*', 'maintenance.*', 'systems.*', 'dashboard.*', 'alerts.*', 'properties.*'
        ],
        [client_1.UserRole.OFFICE_MANAGER]: [
            'employees.view', 'employees.create', 'employees.update', 'employees.attendance.*', 'dashboard.view'
        ],
        [client_1.UserRole.SECURITY_PERSONNEL]: [
            'systems.view', 'security.*', 'alerts.*', 'dashboard.view'
        ],
        [client_1.UserRole.MAINTENANCE_STAFF]: [
            'maintenance.*', 'systems.view', 'dashboard.view'
        ],
        [client_1.UserRole.CONSTRUCTION_SUPERVISOR]: [
            'employees.view', 'employees.attendance.view', 'systems.view', 'dashboard.view'
        ],
    };
    const userPermissions = permissions[role] || [];
    const permissionKey = `${resource}.${action}`;
    return userPermissions.includes('*') ||
        userPermissions.includes(permissionKey) ||
        userPermissions.includes(`${resource}.*`);
}
// Permission check function for use in routes
const checkPermission = (permission) => {
    return (req, res, next) => {
        try {
            const user = req.user;
            if (!user) {
                res.status(401).json({
                    success: false,
                    error: 'UNAUTHORIZED',
                    message: 'Authentication required',
                    timestamp: new Date().toISOString(),
                    path: req.path,
                });
                return;
            }
            const [resource, action] = permission.split('.');
            const hasPermission = checkResourcePermission(user.role, resource, action);
            if (!hasPermission) {
                res.status(403).json({
                    success: false,
                    error: 'FORBIDDEN',
                    message: `Permission denied: ${permission}`,
                    timestamp: new Date().toISOString(),
                    path: req.path,
                });
                return;
            }
            next();
        }
        catch (error) {
            console.error('Permission check error:', error);
            res.status(500).json({
                success: false,
                error: 'INTERNAL_SERVER_ERROR',
                message: 'Permission service error',
                timestamp: new Date().toISOString(),
                path: req.path,
            });
        }
    };
};
exports.checkPermission = checkPermission;
// Alias for backward compatibility
exports.authenticateToken = exports.authenticate;
