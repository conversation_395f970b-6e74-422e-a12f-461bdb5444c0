// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_settings.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserSettings _$UserSettingsFromJson(Map<String, dynamic> json) => UserSettings(
      userId: json['userId'] as String,
      notifications: NotificationSettings.fromJson(
          json['notifications'] as Map<String, dynamic>),
      appearance: AppearanceSettings.fromJson(
          json['appearance'] as Map<String, dynamic>),
      security:
          SecuritySettings.from<PERSON>son(json['security'] as Map<String, dynamic>),
      privacy:
          PrivacySettings.fromJson(json['privacy'] as Map<String, dynamic>),
      accessibility: AccessibilitySettings.fromJson(
          json['accessibility'] as Map<String, dynamic>),
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
    );

Map<String, dynamic> _$UserSettingsToJson(UserSettings instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'notifications': instance.notifications,
      'appearance': instance.appearance,
      'security': instance.security,
      'privacy': instance.privacy,
      'accessibility': instance.accessibility,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
    };

NotificationSettings _$NotificationSettingsFromJson(
        Map<String, dynamic> json) =>
    NotificationSettings(
      pushNotifications: json['pushNotifications'] as bool,
      emailNotifications: json['emailNotifications'] as bool,
      smsNotifications: json['smsNotifications'] as bool,
      systemAlerts: json['systemAlerts'] as bool,
      maintenanceAlerts: json['maintenanceAlerts'] as bool,
      securityAlerts: json['securityAlerts'] as bool,
      propertyUpdates: json['propertyUpdates'] as bool,
      officeUpdates: json['officeUpdates'] as bool,
      quietHoursStart: json['quietHoursStart'] as String,
      quietHoursEnd: json['quietHoursEnd'] as String,
      weekendNotifications: json['weekendNotifications'] as bool,
    );

Map<String, dynamic> _$NotificationSettingsToJson(
        NotificationSettings instance) =>
    <String, dynamic>{
      'pushNotifications': instance.pushNotifications,
      'emailNotifications': instance.emailNotifications,
      'smsNotifications': instance.smsNotifications,
      'systemAlerts': instance.systemAlerts,
      'maintenanceAlerts': instance.maintenanceAlerts,
      'securityAlerts': instance.securityAlerts,
      'propertyUpdates': instance.propertyUpdates,
      'officeUpdates': instance.officeUpdates,
      'quietHoursStart': instance.quietHoursStart,
      'quietHoursEnd': instance.quietHoursEnd,
      'weekendNotifications': instance.weekendNotifications,
    };

AppearanceSettings _$AppearanceSettingsFromJson(Map<String, dynamic> json) =>
    AppearanceSettings(
      theme: json['theme'] as String,
      language: json['language'] as String,
      timezone: json['timezone'] as String,
      dateFormat: json['dateFormat'] as String,
      timeFormat: json['timeFormat'] as String,
      currency: json['currency'] as String,
      fontSize: (json['fontSize'] as num).toDouble(),
      compactMode: json['compactMode'] as bool,
      showAnimations: json['showAnimations'] as bool,
    );

Map<String, dynamic> _$AppearanceSettingsToJson(AppearanceSettings instance) =>
    <String, dynamic>{
      'theme': instance.theme,
      'language': instance.language,
      'timezone': instance.timezone,
      'dateFormat': instance.dateFormat,
      'timeFormat': instance.timeFormat,
      'currency': instance.currency,
      'fontSize': instance.fontSize,
      'compactMode': instance.compactMode,
      'showAnimations': instance.showAnimations,
    };

SecuritySettings _$SecuritySettingsFromJson(Map<String, dynamic> json) =>
    SecuritySettings(
      biometricLogin: json['biometricLogin'] as bool,
      autoLock: json['autoLock'] as bool,
      autoLockTimeout: (json['autoLockTimeout'] as num).toInt(),
      requirePasswordForSensitiveActions:
          json['requirePasswordForSensitiveActions'] as bool,
      twoFactorAuthentication: json['twoFactorAuthentication'] as bool,
      sessionTimeout: json['sessionTimeout'] as bool,
      sessionTimeoutMinutes: (json['sessionTimeoutMinutes'] as num).toInt(),
      logSecurityEvents: json['logSecurityEvents'] as bool,
    );

Map<String, dynamic> _$SecuritySettingsToJson(SecuritySettings instance) =>
    <String, dynamic>{
      'biometricLogin': instance.biometricLogin,
      'autoLock': instance.autoLock,
      'autoLockTimeout': instance.autoLockTimeout,
      'requirePasswordForSensitiveActions':
          instance.requirePasswordForSensitiveActions,
      'twoFactorAuthentication': instance.twoFactorAuthentication,
      'sessionTimeout': instance.sessionTimeout,
      'sessionTimeoutMinutes': instance.sessionTimeoutMinutes,
      'logSecurityEvents': instance.logSecurityEvents,
    };

PrivacySettings _$PrivacySettingsFromJson(Map<String, dynamic> json) =>
    PrivacySettings(
      shareUsageData: json['shareUsageData'] as bool,
      shareLocationData: json['shareLocationData'] as bool,
      allowAnalytics: json['allowAnalytics'] as bool,
      showOnlineStatus: json['showOnlineStatus'] as bool,
      allowContactSync: json['allowContactSync'] as bool,
      dataRetentionPeriod: json['dataRetentionPeriod'] as String,
      autoDeleteOldData: json['autoDeleteOldData'] as bool,
    );

Map<String, dynamic> _$PrivacySettingsToJson(PrivacySettings instance) =>
    <String, dynamic>{
      'shareUsageData': instance.shareUsageData,
      'shareLocationData': instance.shareLocationData,
      'allowAnalytics': instance.allowAnalytics,
      'showOnlineStatus': instance.showOnlineStatus,
      'allowContactSync': instance.allowContactSync,
      'dataRetentionPeriod': instance.dataRetentionPeriod,
      'autoDeleteOldData': instance.autoDeleteOldData,
    };

AccessibilitySettings _$AccessibilitySettingsFromJson(
        Map<String, dynamic> json) =>
    AccessibilitySettings(
      highContrast: json['highContrast'] as bool,
      largeText: json['largeText'] as bool,
      reduceMotion: json['reduceMotion'] as bool,
      screenReader: json['screenReader'] as bool,
      voiceCommands: json['voiceCommands'] as bool,
      textScaling: (json['textScaling'] as num).toDouble(),
      hapticFeedback: json['hapticFeedback'] as bool,
      audioDescriptions: json['audioDescriptions'] as bool,
    );

Map<String, dynamic> _$AccessibilitySettingsToJson(
        AccessibilitySettings instance) =>
    <String, dynamic>{
      'highContrast': instance.highContrast,
      'largeText': instance.largeText,
      'reduceMotion': instance.reduceMotion,
      'screenReader': instance.screenReader,
      'voiceCommands': instance.voiceCommands,
      'textScaling': instance.textScaling,
      'hapticFeedback': instance.hapticFeedback,
      'audioDescriptions': instance.audioDescriptions,
    };
